{"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "Default Resource", "type": "urn:galaxion-tigo-sales-facade:resources:default", "ownerManagedAccess": false, "attributes": {}, "uris": ["/*"]}, {"name": "all_private_resource", "ownerManagedAccess": false, "displayName": "all_private_resource", "attributes": {}, "uris": ["/api/{version}/private/auth/*"], "scopes": [{"name": "read"}, {"name": "write"}]}, {"name": "all_public_resources", "ownerManagedAccess": false, "displayName": "all_public_resources", "uris": ["/api/{version}/public/auth/*"]}], "policies": [{"name": "Default Policy", "description": "A policy that grants access only for users within this realm", "type": "js", "logic": "POSITIVE", "decisionStrategy": "AFFIRMATIVE", "config": {"code": "// by default, grants any permission associated with this policy\n$evaluation.grant();\n"}}, {"name": "has_read_role", "type": "role", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"roles": "[{\"id\":\"galaxion-tigo-sales-facade/read\",\"required\":false}]"}}, {"name": "has_write_role", "type": "role", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"roles": "[{\"id\":\"galaxion-tigo-sales-facade/write\",\"required\":false}]"}}, {"name": "has_all_public_resources_role", "type": "role", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"roles": "[{\"id\":\"galaxion-tigo-sales-facade/all_public_resources\",\"required\":false}]"}}, {"name": "Default Permission", "description": "A permission that applies to the default resource type", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:galaxion-tigo-sales-facade:resources:default", "applyPolicies": "[\"Default Policy\"]"}}, {"name": "read_permission", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"scopes": "[\"read\"]", "applyPolicies": "[\"has_read_role\"]"}}, {"name": "write_permission", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"scopes": "[\"write\"]", "applyPolicies": "[\"has_write_role\"]"}}, {"name": "all_public_resources_permission", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"all_public_resources\"]", "applyPolicies": "[\"has_all_public_resources_role\"]"}}], "scopes": [{"name": "read"}, {"name": "write"}], "decisionStrategy": "UNANIMOUS"}