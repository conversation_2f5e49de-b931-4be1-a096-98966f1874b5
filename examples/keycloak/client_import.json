{"roles": {"client": {"galaxion-tigo-sales-facade": [{"name": "uma_protection"}, {"name": "all_public_resources"}, {"name": "read"}, {"name": "write"}]}}, "users": [{"createdTimestamp": *************, "username": "service-account-galaxion-tigo-sales-facade", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "galaxion-tigo-sales-facade", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-master"], "clientRoles": {"galaxion-tigo-sales-facade": ["uma_protection"]}, "notBefore": 0, "groups": []}], "clients": [{"clientId": "galaxion-tigo-sales-facade", "rootUrl": "https://tigo-sales-facade.dev.epicmt.internal", "adminUrl": "https://tigo-sales-facade.dev.epicmt.internal", "baseUrl": "/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["https://tigo-sales-facade.dev.epicmt.internal/*"], "webOrigins": ["https://tigo-sales-facade.dev.epicmt.internal"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"id.token.as.detached.signature": "false", "saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.artifact.binding": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "require.pushed.authorization.requests": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "Default Resource", "type": "urn:galaxion-tigo-sales-facade:resources:default", "ownerManagedAccess": false, "attributes": {}, "uris": ["/*"]}], "policies": [{"name": "Default Policy", "description": "A policy that grants access only for users within this realm", "type": "js", "logic": "POSITIVE", "decisionStrategy": "AFFIRMATIVE", "config": {"code": "// by default, grants any permission associated with this policy\n$evaluation.grant();\n"}}, {"name": "Default Permission", "description": "A permission that applies to the default resource type", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:galaxion-tigo-sales-facade:resources:default", "applyPolicies": "[\"Default Policy\"]"}}], "scopes": [], "decisionStrategy": "UNANIMOUS"}}]}