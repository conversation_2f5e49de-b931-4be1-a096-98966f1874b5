{"clientId": "tigo-sales-facade", "name": "Tigo Sales Facade", "rootUrl": "https://tigo-sales-facade.dev.epicmt.internal", "adminUrl": "https://tigo-sales-facade.dev.epicmt.internal", "baseUrl": "/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["https://tigo-sales-facade.dev.epicmt.internal/*", "http://localhost:13420/*"], "webOrigins": ["http://localhost:13420", "https://tigo-sales-facade.dev.epicmt.internal"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.force.post.binding": "false", "saml.multivalued.roles": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "require.pushed.authorization.requests": "false", "saml.client.signature": "false", "id.token.as.detached.signature": "false", "saml.assertion.signature": "false", "saml.encrypt": "false", "saml.server.signature": "false", "exclude.session.state.from.auth.response": "false", "saml.artifact.binding": "false", "saml_force_name_id_format": "false", "acr.loa.map": "{}", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "access": {"view": true, "configure": true, "manage": true}}