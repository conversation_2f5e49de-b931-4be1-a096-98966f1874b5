# Launch micro service

[[_TOC_]]

### For any further information regarding the following points please refer to [this page](https://confluence.itsf.io/display/ION/How+to+deploy+common):

* [Requirements and setting up service and DBMDL](https://confluence.itsf.io/display/ION/How+to+deploy+common#Howtodeploycommon-Requirements)
* [Docker image in CI](https://confluence.itsf.io/display/ION/How+to+deploy+common#Howtodeploycommon-DockerimageinCI)
* [Global environment variables](https://confluence.itsf.io/display/ION/How+to+deploy+common#Howtodeploycommon-Environmentvariables)
* [OPS variables](https://confluence.itsf.io/display/ION/How+to+deploy+common#Howtodeploycommon-OPS(Notmandatory))
