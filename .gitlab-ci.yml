include:
  - project: "glx/devops/piplib"
    ref: main
    file: "/pipelines/maven/maven-java17.gitlab-ci.yml"
  - project: "glx/devops/piplib"
    ref: main
    file: "/pipelines/modules/maven/liquibase.gitlab-ci.yml"
  - project: "glx/devops/piplib"
    ref: main
    file: "/pipelines/modules/chart.gitlab-ci.yml"
  - project: "glx/devops/piplib"
    ref: main
    file: "/pipelines/modules/maven/maven.release.gitlab-ci.yml"
    # Delivery
  - project: "glx/devops/piplib"
    ref: main
    file: "/pipelines/nexus/delivery-chart.gitlab-ci.yml"
  - project: "glx/devops/piplib"
    ref: main
    file: "/pipelines/nexus/delivery-docker.gitlab-ci.yml"

