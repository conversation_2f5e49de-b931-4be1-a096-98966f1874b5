global:
  name: tigo-sales-facade
  description: Tigo Sales Facade
  version: 1.0.2
  maintainers:
  - email: <EMAIL>
    name: <PERSON><PERSON><PERSON> CUNY
  - email: <EMAIL>
    name: <PERSON>e PRUNIER
  labels:
    team: itsf

main:
  replicas: 1
  securityContext: enabled
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-galaxion-qa.tigo.com.gt:5005/galaxion/tigo-sales-facade
    tag: auto
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "500m"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "2Gi"

dbmdl:
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "1024Mi"
  image:
    tag: auto
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  labels:
    owner: itsf
  env:
    LIQUIBASE_DATASOURCE_USERNAME: galaxion
    LIQUIBASE_DATASOURCE_PASSWORD: G4l4x12023
    LIQUIBASE_DATASOURCE_URL: *********************************
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml

configuration:
  env:
    ### HEADERS ###
    GALAXION_USER_IDENTIFIER: SYSTEM
    GALAXION_USER_TYPE: SYSTEM
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: INFO
    ### DATABASE ###
    SPRING_DATASOURCE_URL: *********************************
    SPRING_DATASOURCE_USERNAME: galaxion
    SPRING_DATASOURCE_PASSWORD: G4l4x12023
    ### RABBITMQ ###
    SPRING_RABBITMQ_USERNAME: rabbit
    SPRING_RABBITMQ_PASSWORD: CHANGE_ME_PLEASE
    SPRING_RABBITMQ_ADDRESSES: *************
    SPRING_RABBITMQ_VIRTUALHOST: amqp://rabbit:CHANGE_ME_PLEASE@localhost:5672/myvhost
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACQUISITIONPROSPECTSSERVICE: acquisition-prospects-service:8080
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_CATALOGSERVICE: catalog-service:8080
    ENVIRONMENT_URL_ADDRESSESSERVICE: addresses-service:8080
    ENVIRONMENT_URL_CROSSSELLSERVICE: cross-sell-service:8080
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_CREDITSCORESSERVICE: credit-scores-service:8080
    ENVIRONMENT_URL_COLLECTIONSSERVICE: collections-service:8080
    ENVIRONMENT_URL_TIGOPROSPECTLEADSERVICE: prospect-lead:8082
    ENVIRONMENT_URL_EXTERNALCUSTOMERSEARCHSERVICE: recognize-mockup:8082
    ENVIRONMENT_URL_TIGOCREDITSCORESSERVICE: TO_BE_DEFINED
    ENVIRONMENT_URL_TIGOGEOREFERENCESERVICE: http://microcks.microcks.svc.cluster.local:8080/rest/Georeferencing+and+Normalize+Address/1.0/v1
    ENVIRONMENT_URL_TIGOFRAUDMANAGEMENTSERVICE: http://microcks.microcks.svc.cluster.local:8080/rest/Fraud+management+API./1.0.0
    ENVIRONMENT_URL_TIGORISKASSESSMENTSERVICE: http://microcks.microcks.svc.cluster.local:8080/rest/risk_assessment/v1
    ENVIRONMENT_URL_TIMEINTERVALFIELDSERVICE: http://microcks.microcks.svc.cluster.local:8080/soap/timeInterval/v1
    ENVIRONMENT_URL_WORKFLOWENGINE: workflow-engine:8080
    ENVIRONMENT_URL_WORKFLOWENGINEFACADE: https://glx-crm-pa-stg.tigo.cam/api-gateway/wfe-order-facade/api/v1
    ENVIRONMENT_URL_TIGONOTIFICATIONSSERVICE: https://devapi-management.tigo.cam
    ENVIRONMENT_URL_SERVICECOVERAGE: http://service-coverage-mock-up.galaxion.svc:8082
    ENVIRONMENT_URL_OTP_MANAGEMENT_TOKEN: https://api.servers1.tigocloud.net/server/tokens
    ENVIRONMENT_URL_OTP_MANAGEMENT_CODE: https://test.id.tigo.com/tigoid/pub/v2
    ENVIRONMENT_URL_CBSSERVICE: https://glx-mockups-pa-stg.tigo.cam/rest/CBS+Account+Receivable+management+API./1.0.0
    ENVIRONMENT_URL_EVIDENTSERVICE: http://10.69.60.234:8085
    ENVIRONMENT_URL_CBSBUSINESSSERVICE: https://glx-mockups-pa-stg.tigo.cam/rest/CBS+Business+Control+management+API./1.0.0
    ENVIRONMENT_URL_WORKFLOWQUERYSERVICE: https://workflow-query.tigo.com.gt
    ENVIRONMENT_URL_CRMAPIWEFSERVICE: https://crm-api.tigo.com.gt
    ENVIRONMENT_URL_NOTIFICATIONSSERVICE: notifications-service:8080
    ENVIRONMENT_URL_TIGONOTIFICATIONSCIFRATORSERVICE: https://devapi-management.tigo.cam
    ### CACHE DURATION ###
    TIMETOLIVE_CACHE_CONTACTSSERVICE: '********'
    ### GET TOKEN TIGO OAUTH ###
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTID: galaxion-malta-workflow-engine-facade
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_AUTHORIZATIONGRANTTYPE: client_credentials
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_AUTHORIZATIONURI: https://glx-iam-pa-stg.tigo.cam/auth
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token

    KEYCLOAK_REALM: galaxion
    KEYCLOAK_AUTHSERVERURL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_RESOURCE: workflow-engine
    KEYCLOAK_CREDENTIALS_SECRET: '**********'

    DEFAULT_STRATUM: 3

    ### PROXY CONFIGURATION ###
    ENVIRONMENT_FIELDSERVICE_USEPROXY: true
    ENVIRONMENT_FIELDSERVICE_PROXY_HOST: proxyserver.epmtelco.com.co
    ENVIRONMENT_FIELDSERVICE_PROXY_PORT: 8080
    ALFRESCO_PROXY_USEPROXY: true
    ALFRESCO_PROXY_HOST: proxyserver.epmtelco.com.co
    ALFRESCO_PROXY_PORT: 8080

    ### OTP MANAGEMENT ###

    MANAGEMENT_OTP_SUB_ID: '166110c9-8b82-40c4-8c60-e7d1e9e9a0a8'
    MANAGEMENT_OTP_APP_ID: '7a77ff70-19bf-417c-85f8-9d597f199da8'
    MANAGEMENT_OTP_TEXT: 'El codigo de seguridad que solicitaste es {token}. TIGO nunca solicitara tu codigo, es personal y confidencial. No lo compartas con otra persona.'
    MANAGEMENT_OTP_LENGTH: 6
    MANAGEMENT_OTP_KEY: 'AiBRbLWJbHoTFF3ylPp3XVOyVHjMJJ5G'
    MANAGEMENT_OTP_TYPE: 'alpha'
    MANAGEMENT_OTP_TTL: 60000
    MANAGEMENT_OTP_COUNTRYCODE_CO: 502
    MANAGEMENT_OTP_COUNTRYCODE_GT: 502

    ################################################################################
    # GET TOKEN KEYCLOAK (Used to retrieve the token before a feign call when there is none)
    ################################################################################
    SECURITY_KEYCLOAK_URL: https://glx-iam-pa-stg.tigo.cam/auth
    SECURITY_KEYCLOAK_REALM: galaxion 
    SECURITY_KEYCLOAK_RESOURCE0: workflow-query
    SECURITY_KEYCLOAK_SECRET: **********