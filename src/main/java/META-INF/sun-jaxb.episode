<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bindings xmlns="http://java.sun.com/xml/ns/jaxb" if-exists="true" version="2.1">
      
    <!--

This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
See https://eclipse-ee4j.github.io/jaxb-ri 
Any modifications to this file will be lost upon recompilation of the source schema. 
Generated on: 2024.09.02 at 03:50:36 PM CST 

  -->
      
    <bindings xmlns:tns="http://www.clicksoftware.com" if-exists="true" scd="x-schema::tns">
            
        <schemaBindings map="false">
                  
            <package name="com.tigo.galaxion.sales.facade.soap.multiple_operations"/>
                
        </schemaBindings>
            
        <bindings if-exists="true" scd="tns:ExecuteMultipleOperations">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.ExecuteMultipleOperations"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:StandardOperations">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.StandardOperations"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:ExecuteMultipleOperationsResponse">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.ExecuteMultipleOperationsResponse"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:OperationsResults">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.OperationsResults"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:StandardOperation">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.StandardOperation"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BaseObjectWrapper">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.BaseObjectWrapper"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BaseObject">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.BaseObject"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCServices">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCServices"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCServicesExternalRef">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCServicesExternalRef"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCCustomerAsset">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCCustomerAsset"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCCustomerAssetExternalRef">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCCustomerAssetExternalRef"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Error">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.Error"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:BusinessObject">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.BusinessObject"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:W6RequestedProperties">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.W6RequestedProperties"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:Stamp">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.Stamp"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCServiceStatusReference">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCServiceStatusReference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCServiceCompletionReasonReference">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCServiceCompletionReasonReference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCMaterialTypeCategoryReference">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCMaterialTypeCategoryReference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCMaterialTypeReference">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCMaterialTypeReference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCPartTypeReference">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCPartTypeReference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCProvisioningStatusesReference">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCProvisioningStatusesReference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCServicesReference">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCServicesReference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AreaReference">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.AreaReference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AggregateValue">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.AggregateValue"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ObjectReference">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.ObjectReference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SucceededGetObjectOperation">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.SucceededGetObjectOperation"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:FailedOperation">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.FailedOperation"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:SucceededOperation">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.SucceededOperation"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ObjectReferenceWrapper">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.ObjectReferenceWrapper"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:OperationResult">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.OperationResult"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:MCPartsInStock">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.MCPartsInStock"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:AggregateStamp">
                  
            <class ref="com.tigo.galaxion.sales.facade.soap.multiple_operations.AggregateStamp"/>
                
        </bindings>
          
    </bindings>
    
</bindings>
