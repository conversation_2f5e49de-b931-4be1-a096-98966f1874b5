package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
public class AddOnResponse {

    private Long id;

    private String description;

    private Long displayOrder;

    @Builder.Default
    private List<UsageResponse> usages = new ArrayList<>();

    private String catalogCode;

    private AmountResponse amountVatIncluded;

    private AmountResponse amountVatExcluded;

    private Long max;

    private String itemGroup;

    @Builder.Default
    private List<RecurringAmountByOccurrenceResponse> recurringAmountsByOccurrence = new ArrayList<>();
}
