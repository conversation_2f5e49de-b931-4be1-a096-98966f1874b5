package com.tigo.galaxion.sales.facade.connector.alfresco;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.fasterxml.jackson.databind.JsonNode;
import com.tigo.galaxion.sales.facade.connector.alfresco.config.FeignProxyConfiguration;
import com.tigo.galaxion.sales.facade.connector.alfresco.request.AlfrescoDocumentRequest;
import com.tigo.galaxion.sales.facade.connector.alfresco.request.AlfrescoGenerateTokenRequest;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoAuthResponse;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoDocumentResponse;

@FeignClient(value = "tigo-alfresco-service", url = "${environment.url.tigo-alfresco-service}", configuration = {
        AlfrescoErrorDecoder.class, FeignProxyConfiguration.class })
public interface AlfrescoClient {

    @PostMapping(value = "/apirouter-wsrestsignature/oauth/token", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    AlfrescoAuthResponse getToken(@RequestHeader("Authorization") String authorization,
            @RequestBody AlfrescoGenerateTokenRequest body);

    @PostMapping(value = "/apirouter-rsdocmanager/api/v1/create/file", consumes = "application/json")
    AlfrescoDocumentResponse createFile(@RequestHeader("Authorization") String authorization,
            @RequestBody AlfrescoDocumentRequest request);

    @PostMapping(value = "/apirouter-rsdocmanager/api/v1/create/file", consumes = "application/json")
    AlfrescoDocumentResponse createFileByProspectId(@RequestHeader("Authorization") String authorization,
            @RequestBody JsonNode request);
}
