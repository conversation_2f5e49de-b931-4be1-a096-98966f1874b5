package com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("An Offer")
public class Offer {
    @NotBlank
    @ApiModelProperty(value = "The offer id", required = true)
    private String id;
    @NotBlank
    @ApiModelProperty(value = "The offer name", required = true)
    private String name;
    @NotNull
    @ApiModelProperty(value = "The offer value", required = true)
    private Integer value;
}
