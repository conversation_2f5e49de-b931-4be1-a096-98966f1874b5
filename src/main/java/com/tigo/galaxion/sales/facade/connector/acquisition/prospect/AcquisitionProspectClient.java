package com.tigo.galaxion.sales.facade.connector.acquisition.prospect;

import com.github.fge.jsonpatch.mergepatch.JsonMergePatch;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.AcquisitionProspectsAddOfferToCartRequest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.SearchAcquisitionProspectsOffersRequest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.UpdateProspectCreditScoreRequest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart.CartAddOnRequest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart.CartEquipmentRequest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.OfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.payment_method.PaymentMethodResponse;
import com.tigo.galaxion.sales.facade.domain.request.GetTariffPlansForAnOfferRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoProspectContactRequest;
import com.tigo.galaxion.sales.facade.domain.response.TigoContactResponse;

import com.tigo.galaxion.sales.facade.domain.response.ItemGroupWithAddOnsResponse;
import com.tigo.galaxion.sales.facade.domain.response.OffersAndConditionalDiscountsResponse;
import com.tigo.galaxion.sales.facade.domain.response.ProspectCatalogInclusiveEquipmentGroupResponse;
import com.tigo.galaxion.sales.facade.domain.response.TariffPlansAndConditionalDiscountsResponse;
import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "acquisition-prospects-service", url = "${environment.url.acquisition-prospects-service}",
             configuration = AcquisitionProspectErrorDecoder.class)
public interface AcquisitionProspectClient { 

    @GetMapping("/api/v1/prospects/{prospect_reference}")
    AcquisitionProspectResponse getAcquisitionProspect(@PathVariable("prospect_reference") String prospectReference);

   @GetMapping("/api/v1/prospects/{prospect_reference}/offers")
   OffersAndConditionalDiscountsResponse getOffers(
      @PathVariable("prospect_reference") String prospectReference,
      @SpringQueryMap SearchAcquisitionProspectsOffersRequest searchAcquisitionProspectsOffersRequest
   );

    @GetMapping("/api/v1/prospects/{prospect_reference}/contacts")
    TigoContactResponse getContact(@PathVariable("prospect_reference") String prospectReference);

    @PutMapping("/api/v1/prospects/{prospect_reference}/contacts")
    TigoContactResponse createOrUpdateContact(@PathVariable("prospect_reference") String prospectReference,
                                              @RequestBody TigoProspectContactRequest request);

    @PostMapping("/api/v1/private/auth/prospects/{reference}/carts/offers")
    CartResponse addOfferToCart(@PathVariable("reference") String prospectReference,
                                @RequestBody AcquisitionProspectsAddOfferToCartRequest request);

    @DeleteMapping("/api/v1/private/auth/prospects/{reference}/carts/offers/{offer_id}")
    CartResponse deleteOfferToCart(@PathVariable("reference") String prospectReference,
                                   @PathVariable("offer_id") long offerId);

    @PatchMapping("/api/v1/prospects/{prospect_reference}/credit-scores")
    TigoContactResponse updateProspectCreditScore(@PathVariable("prospect_reference") String prospectReference,
                                                  @RequestBody UpdateProspectCreditScoreRequest request);

    @GetMapping("/api/v1/prospects/{prospect_reference}/carts")
    CartResponse getProspectCart(@PathVariable("prospect_reference") String prospectReference);

    @GetMapping("/api/v1/prospects/{prospect_reference}/carts/offers/{offer_id}")
    OfferResponse getOffer(@PathVariable("prospect_reference") String prospectReference,
                           @PathVariable("offer_id") Long offerId);

    @DeleteMapping("/api/v1/prospects/{reference}/payment-methods")
    PaymentMethodResponse deletePaymentMethod(@PathVariable("reference") String reference);

    @PostMapping("/api/v1/prospects/{prospect_reference}/carts/offers/{offer_id}/add-ons")
    CartResponse addAddOnToOffer(@PathVariable("prospect_reference") String prospectReference,
                                 @PathVariable("offer_id") Long offerId,
                                 @RequestBody CartAddOnRequest request);

    @DeleteMapping("/api/v1/prospects/{prospect_reference}/carts/offers/{offer_id}/add-ons/{add_on_id}")
    CartResponse deleteAddOnFromOffer(@PathVariable("prospect_reference") String prospectReference,
                                      @PathVariable("offer_id") Long offerId,
                                      @PathVariable("add_on_id") Long addOnId);

   @GetMapping("/api/v1/private/auth/prospects/{prospect_reference}/offers/{offer_code}/tariff-plans")
   TariffPlansAndConditionalDiscountsResponse getTariffsPlansForAnOffer(
      @PathVariable("prospect_reference") String prospectReference,
      @PathVariable("offer_code") String offerCode,
      @SpringQueryMap GetTariffPlansForAnOfferRequest getTariffPlansForAnOfferRequest
   );

   @GetMapping("/api/v1/private/auth/prospects/{prospect_reference}/offers/{offer_id}/add-ons")
   List<ItemGroupWithAddOnsResponse> getAddOnsForAnOffer(
      @PathVariable("prospect_reference") String prospectReference,
      @PathVariable("offer_id") Long offerId
   );

   @GetMapping("/api/v1/private/auth/prospects/{prospect_reference}/offers/{offer_id}/equipments")
   List<ProspectCatalogInclusiveEquipmentGroupResponse> getEquipmentForAnOffer(
      @PathVariable("prospect_reference") String prospectReference,
      @PathVariable("offer_id") Long offerId
   );

   @PatchMapping("/api/v1/prospects/{prospect_reference}/carts/offers/{offer_id}")
   CartResponse updateOfferCart(
      @PathVariable("prospect_reference") String prospectReference,
      @PathVariable("offer_id") long offerId,
      @RequestBody JsonMergePatch patchRequest
   );

   @PostMapping("/api/v1/private/auth/prospects/{prospect_reference}/carts/offers/{offer_id}/equipments")
   CartResponse addEquipmentToOffer(@PathVariable("prospect_reference") String prospectReference,
                                 @PathVariable("offer_id") Long offerId,
                                 @RequestBody CartEquipmentRequest request);

   @DeleteMapping("/api/v1/private/auth/prospects/{prospect_reference}/carts/offers/{offer_id}/equipments/{equipment_id}")
   CartResponse deleteEquipmentFromOffer(@PathVariable("prospect_reference") String prospectReference,
                                      @PathVariable("offer_id") Long offerId,
                                      @PathVariable("equipment_id") Long equipmentId);
}
