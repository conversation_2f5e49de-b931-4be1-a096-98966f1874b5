package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

@SuperBuilder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoAccessoryEquipmentResponse extends TigoEquipmentResponse {

    @ApiModelProperty(value = "The service domains of the accessory")
    private List<TigoServiceDomainResponse> serviceDomains;

}
