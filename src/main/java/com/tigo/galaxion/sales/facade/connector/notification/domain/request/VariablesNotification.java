package com.tigo.galaxion.sales.facade.connector.notification.domain.request;


import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class VariablesNotification {

    @JsonProperty("email")
    private String email;

    @JsonProperty("values")
    private  ValuesNotification values;
}
