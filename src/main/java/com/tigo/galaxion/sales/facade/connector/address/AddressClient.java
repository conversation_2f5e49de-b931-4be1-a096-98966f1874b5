package com.tigo.galaxion.sales.facade.connector.address;

import com.tigo.galaxion.sales.facade.connector.address.domain.request.AddressRequest;
import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "addresses-service", url = "${environment.url.addresses-service}", configuration = AddressErrorDecoder.class)
public interface AddressClient {

    @GetMapping("/api/v1/addresses/{id}")
    AddressResponse getAddress(@PathVariable Long id);

    @PostMapping("/api/v1/addresses")
    long createAddress(@RequestBody AddressRequest addressRequest);

    @PatchMapping(value = "/api/v1/addresses/{id}", consumes = "application/merge-patch+json")
    void updateAddress(@PathVariable Long id, @RequestBody AddressRequest addressRequest);

}
