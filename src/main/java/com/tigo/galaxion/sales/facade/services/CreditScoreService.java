package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.UpdateProspectCreditScoreRequest;
import com.tigo.galaxion.sales.facade.connector.contact.ContactClient;
import com.tigo.galaxion.sales.facade.connector.contact.domain.enumeration.TypeEmailEnum;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactV2Response;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.TigoCreditScoreClient;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.request.TigoCreditScoreRequest;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.response.TigoCreditScoreResponse;
import com.tigo.galaxion.sales.facade.domain.problem.IdentityDocumentAlreadyExistsProblem;
import com.tigo.galaxion.sales.facade.domain.request.CreditScoreRequest;
import com.tigo.galaxion.sales.facade.model.entity.ContactIdentityDocumentEntity;
import com.tigo.galaxion.sales.facade.model.repository.ContactIdentityDocumentRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.springframework.util.CollectionUtils.isEmpty;

@Service
@RequiredArgsConstructor
public class CreditScoreService {

    private final TigoCreditScoreClient tigoCreditScoreClient;
    private final ContactClient contactClient;
    private final AcquisitionProspectClient acquisitionProspectClient;
    private final ContactIdentityDocumentRepository contactIdentityDocumentRepository;

    @Transactional
    public void executeCreditScore(String prospectReference,
                                   CreditScoreRequest request) {
        assertIdentityDocumentNotExistsInGalaxion(request);
        var tigoCreditScoreResponse = getTigoCreditScore(request);
        updateProspectCreditScore(prospectReference, tigoCreditScoreResponse);
        updateContactIdentityDocument(prospectReference, request);
    }

    private void updateContactIdentityDocument(String prospectReference, CreditScoreRequest request) {
        var contactIdentityDocumentEntity = ContactIdentityDocumentEntity
                .builder()
                .reference(prospectReference)
                .documentIdentifier(request.getDocumentId())
                .documentType(request.getDocumentType())
                .nationality(request.getNationality())
                .build();
        contactIdentityDocumentRepository.save(contactIdentityDocumentEntity);
    }

    private void updateProspectCreditScore(String prospectReference, TigoCreditScoreResponse tigoCreditScoreResponse) {
        var updateProspectCreditScoreRequest = UpdateProspectCreditScoreRequest
                .builder()
                .isBlacklisted(tigoCreditScoreResponse.getBlacklisted())
                .creditScore(tigoCreditScoreResponse.getScore())
                .build();
        acquisitionProspectClient.updateProspectCreditScore(prospectReference, updateProspectCreditScoreRequest);
    }

    public TigoCreditScoreResponse getTigoCreditScore(CreditScoreRequest request) {
        var tigoCreditScoreRequest = TigoCreditScoreRequest
                .builder()
                .type(request.getDocumentType())
                .documentNumber(request.getDocumentId())
                .nationality(request.getNationality())
                .build();
        return tigoCreditScoreClient.score(tigoCreditScoreRequest);
    }

    private void assertIdentityDocumentNotExistsInGalaxion(CreditScoreRequest request) {
        var contacts = contactClient.searchContact(request.getDocumentId());

        if (!contacts.isEmpty() && hasSameNationality(contacts, request.getNationality())) {
            var contact = contacts.get(0);
            var firstname = contact.getFirstName();
            var lastname = contact.getLastName();
            var birthDate = contact.getBirthDate();
            var email = contact.getEmails()
                               .stream()
                               .filter(contactEmailV2Response -> TypeEmailEnum.MAIN.equals(contactEmailV2Response.getType()))
                               .findFirst()
                               .orElse(contact.getEmails().get(0))
                               .getEmail();
            throw new IdentityDocumentAlreadyExistsProblem(firstname, lastname, email, birthDate);
        }
    }

    private boolean hasSameNationality(List<ContactV2Response> contacts, String nationality) {
        if (isEmpty(contacts))  return false;
        return contacts.stream().flatMap(contact -> contact.getIdentityDocuments().stream()).anyMatch(identityDocument -> nationality.equals(identityDocument.getNationality()));
    }
}
