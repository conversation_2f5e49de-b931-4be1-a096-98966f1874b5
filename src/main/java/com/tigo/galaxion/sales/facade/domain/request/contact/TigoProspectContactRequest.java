package com.tigo.galaxion.sales.facade.domain.request.contact;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.contact.AcquisitionPermissionGroupListRequest;
import com.tigo.galaxion.sales.facade.domain.enumeration.BillDeliveryTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Locale;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoProspectContactRequest {

    @NotBlank
    @ApiModelProperty(value = "First name of the person.", example = "Guido", required = true)
    private String firstName;

    @NotBlank
    @ApiModelProperty(value = "Last name of the person.", example = "van Rossum", required = true)
    private String lastName;

    @NotNull
    @ApiModelProperty(value = "Birth date of the person.", example = "1951-05-04", required = true)
    private LocalDate birthDate;

    @Email
    @NotBlank
    @ApiModelProperty(value = "Email of the customer", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank
    @ApiModelProperty(value = "Primary phone number of the customer", example = "0812345678", required = true)
    private String primaryPhoneNumber;

    @ApiModelProperty(value = "Primary phone number of the customer", example = "0452345678")
    private String secondaryPhoneNumber;

    @NotNull
    @ApiModelProperty(value = "Preferred language code", example = "en_MT", required = true, dataType = "java.lang.String")
    private Locale preferredLanguageCode;

    @NotNull
    @ApiModelProperty(value = "Bill delivery type", example = "ONLINE", required = true)
    private BillDeliveryTypeEnum billDeliveryType;

    @NotNull
    @Valid
    private AcquisitionPermissionGroupListRequest permissions;

    @NotNull
    @Valid
    @ApiModelProperty(value = "The billing address")
    private TigoAddressRequest billingAddress;

    @NotNull
    @Valid
    @ApiModelProperty(value = "The delivery address")
    private TigoAddressRequest deliveryAddress;

    @Setter
    @ApiModelProperty(value = "The document identifier", example = "0123456789")
    private String documentIdentifier;

    @Setter
    @ApiModelProperty(value = "The document type", example = "NIT")
    private String documentType;

    @Setter
    @ApiModelProperty(value = "The nationality", example = "COLOMBIAN")
    private String nationality;
}
