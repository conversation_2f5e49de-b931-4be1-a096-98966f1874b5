package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import java.util.List;

import javax.validation.constraints.NotBlank;

import lombok.Data;

@Data
public class CreateInclusiveEquipmentV3Request {
	private List<CreateAddonV3Request> addons;
	@NotBlank
	private String catalogCode;
	private String orderReference;
	private String partNumber;
	private String serialNumber;
	@NotBlank
	private String type;

}
