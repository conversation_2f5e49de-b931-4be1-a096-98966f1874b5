package com.tigo.galaxion.sales.facade.connector.evident.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.CodeDataOTP;
import com.tigo.galaxion.sales.facade.connector.evident.domain.Identification;
import com.tigo.galaxion.sales.facade.connector.evident.domain.QuestionnaireData;
import com.tigo.galaxion.sales.facade.connector.evident.domain.RecognizeData;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentGenerateRequestBody {
    
    @JsonProperty("identification")
    private Identification identification; 

    @JsonProperty("questionnaireData")
    private QuestionnaireData questionnaireData; 

    @JsonProperty("recognizeData")
    private RecognizeData recognizeData;

    @JsonProperty("codeDataOTP")
    private CodeDataOTP codeDataOTP;

}
