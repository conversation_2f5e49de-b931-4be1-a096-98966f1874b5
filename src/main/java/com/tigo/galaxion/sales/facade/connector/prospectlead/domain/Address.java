package com.tigo.galaxion.sales.facade.connector.prospectlead.domain;

import java.math.BigInteger;
import java.util.List;

import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Address {
    @ApiModelProperty(value = "Address Type", required = true, example = "1")
    @JsonProperty("addressType")
    private Integer addressType;

    @ApiModelProperty(value = "Department Code", required = true, example = "5")
    @JsonProperty("departmentCode")
    private String departmentCode;

    @ApiModelProperty(value = "Department Name", required = true, example = "ANTIOQUIA")
    @JsonProperty("department")
    private String department;

    @ApiModelProperty(value = "Municipality Code", required = true, example = "4")
    @JsonProperty("municipalityCode")
    private String municipalityCode;

    @ApiModelProperty(value = "Municipality Name", required = true, example = "SABANETA")
    @JsonProperty("municipality")
    private String municipality;

    @ApiModelProperty(value = "Address", required = true, example = "KR 43 A # 53 D - 46 SUR IN 1609")
    @JsonProperty("address")
    private String address;

    @ApiModelProperty(value = "Latitude", required = true, example = "6.157284259796143")
    @JsonProperty("latitude")
    private String latitude;

    @ApiModelProperty(value = "Longitude", required = true, example = "-75.60497283935547")
    @JsonProperty("longitude")
    private String longitude;

    @ApiModelProperty(value = "Stratum", required = true, example = "2")
    @Size(min = 1, max = 2)
    @JsonProperty("stratum")
    private int stratum;

    @ApiModelProperty(value = "Micro Zone", required = false, example = "CALDAS")
    @JsonProperty("microzone")
    private String microzone;

    @ApiModelProperty(value = "Country Code", required = true, example = "57")
    @JsonProperty("countryCode")
    private String countryCode;

    @ApiModelProperty(value = "Address Code", required = true, example = "1431065734")
    @JsonProperty("addressCode")
    private String addressCode;

    @ApiModelProperty(value = "Neighborhood Name", required = true, example = "Barrio Los Angeles")
    @JsonProperty("neighborhoodName")
    private String neighborhoodName;

    @ApiModelProperty(value = "Gis code * + * normalized address * +  * Urbana|Rural * Natural address * + neighborhoodName", required = false, example = "1431065734 * KR 43 A # 53 D - 46 SUR IN 1609 * Rural *  CR 43 A  # 53 D SUR - 46, INTERIOR 1609 Los Arias")
    @JsonProperty("descriptionAddress")
    private String descriptionAddress;

    @ApiModelProperty(value = "Core Installation Address Id", required = false, example = "null")
    @JsonProperty("coreInstallationAddressId")
    private BigInteger coreInstallationAddressId;


    //added
    @JsonProperty("province")
    private String province;

    @JsonProperty("district")
    private String district;

    @JsonProperty("subDistrict")
    private String subDistrict;

    @JsonProperty("neighborhood")
    private String neighborhood;

    @JsonProperty("street")
    private String street;

    @JsonProperty("house")
    private String house;
    
    @JsonProperty("provinceCode")
    private String provinceCode;

    @JsonProperty("districtCode")
    private String districtCode;

    @JsonProperty("subDistrictCode")
    private String subDistrictCode;

    @JsonProperty("neighborhoodCode")
    private String neighborhoodCode;

    @JsonProperty("streetCode")
    private String streetCode;

    @JsonProperty("houseCode")
    private String houseCode;

    @JsonProperty("technologiesAvailable")
    private List<String> technologiesAvailable;
    
    
}
