package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.contact;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AcquisitionPermissionGroupListRequest {

    @ApiModelProperty(value = "If the contact allow to be contacted by third parties.", required = true)
    private Boolean allowThirdParty;

    @ApiModelProperty(value = "If the contact has marketing profiling enabled.", required = true)
    private Boolean hasMarketingProfiling;

    private List<AcquisitionPermissionGroupRequest> permissionGroups;
}
