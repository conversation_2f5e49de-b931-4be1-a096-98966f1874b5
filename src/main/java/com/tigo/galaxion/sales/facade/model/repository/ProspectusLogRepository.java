package com.tigo.galaxion.sales.facade.model.repository;

import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogEntity;
import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogId;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ProspectusLogRepository extends JpaRepository<ProspectusLogEntity, ProspectusLogId> {

    Optional<ProspectusLogEntity> findByProspectusLogId_Reference(String reference);
    
    List<ProspectusLogEntity> findByProspectusLogId_ReferenceOrderByCreatedAtDesc(String reference);
}
