package com.tigo.galaxion.sales.facade.connector.customer.search;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.tigo.galaxion.sales.facade.config.FeignRetryConfig;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomerFullDetail;

@FeignClient(value = "external-customer-search-client", url = "${environment.url.external-customer-search-service}", configuration = {
		FeignRetryConfig.class, ExternalCustomerSearchErrorDecoder.class })
public interface ExternalCustomerSearchClient {

  @GetMapping("/recognize")
  ExternalCustomerFullDetail searchCustomerByExternalId(
      @RequestParam("id") String id,
      @RequestParam("typeId") String typeId);
}
