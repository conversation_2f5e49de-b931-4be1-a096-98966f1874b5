package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoTariffPlanDiscountResponse {

    @ApiModelProperty(value = "The invoice description of the discount associated with the tariff plan", example = "12-month FREE MBB promotion")
    private String description;

    @ApiModelProperty(value = "Vat included amount of the discount associated with the tariff plan", example = "5000")
    private Long amountVatIncluded;

    @ApiModelProperty(value = "Vat excluded amount of the discount associated with the tariff plan", example = "4500")
    private Long amountVatExcluded;

    @ApiModelProperty(value = "The discount code", example = "QWERTY", required = true)
    private String catalogCode;

    @ApiModelProperty(value = "The associated billing type", example = "RECURRING", required = true)
    private String billingType;

    @ApiModelProperty(value = "The occurrence of the discount", example = "7", required = true)
    private Integer occurrence;

    @ApiModelProperty(value = "Item type of the discount", example = "TARIFF_PLAN", required = true)
    private String discountItemType;
}
