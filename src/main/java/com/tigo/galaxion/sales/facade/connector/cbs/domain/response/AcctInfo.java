package com.tigo.galaxion.sales.facade.connector.cbs.domain.response;

import java.util.List;
import lombok.Data;

@Data
public class AcctInfo {
    private String acctCode;
    private String userCustomerKey;
    private UserCustomer userCustomer;
    private String rootAcctKey;
    private AcctBasicInfo acctBasicInfo;
    private String billCycleType;
    private String acctType;
    private String paymentType;
    private String acctClass;
    private Number currencyId;
    private String acctPayMethod;
    private List<AddressInfo> addressInfo;
    private String billCycleOpenDate;
    private String billCycleEndDate;
}
