package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoPermissionResponse {

    @ApiModelProperty(value = "The permission of the contact.", example = "ALLOW_EMAIL_CONTACT", required = true)
    private String permission;

    @ApiModelProperty(value = "The human readable name of the contact permission.", example = "Email", required = true)
    private String name;

    @ApiModelProperty(value = "If the permission is enabled or not.", example = "true", required = true)
    private boolean enabled;
}
