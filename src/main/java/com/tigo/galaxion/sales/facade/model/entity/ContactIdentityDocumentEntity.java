package com.tigo.galaxion.sales.facade.model.entity;

import com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "contact_identity_document")
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContactIdentityDocumentEntity {

    @Id
    @Column(name = "reference", nullable = false)
    private String reference;

    @Column(name = "document_identifier", nullable = false)
    private String documentIdentifier;

    @Enumerated(EnumType.STRING)
    @Column(name = "document_type", nullable = false)
    private IdDocumentTypeEnum documentType;

    @Column(name = "nationality", nullable = false)
    private String nationality;
}
