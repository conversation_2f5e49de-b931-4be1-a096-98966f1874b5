package com.tigo.galaxion.sales.facade.connector.evident.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.Identification;
import com.tigo.galaxion.sales.facade.connector.evident.domain.QuestionnaireData;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentInitializeRequestBody {

    @JsonProperty("identification")
    private Identification Identification;

    @JsonProperty("questionnaireData")
    private QuestionnaireData questionnaireData;
    
}
