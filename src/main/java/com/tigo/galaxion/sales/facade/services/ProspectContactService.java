package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.address.AddressClient;
import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoProspectContactRequest;
import com.tigo.galaxion.sales.facade.domain.response.TigoContactResponse;
import com.tigo.galaxion.sales.facade.model.entity.ContactAddressEntity;
import com.tigo.galaxion.sales.facade.model.repository.ContactAddressRepository;
import com.tigo.galaxion.sales.facade.services.retrieval.ContactIdentityDocumentRetrievalService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.tigo.galaxion.sales.facade.mapper.dto.AddressRequestMapper.buildAddressRequest;
import static com.tigo.galaxion.sales.facade.mapper.entity.ContactAddressEntityMapper.buildContactAddressEntity;

@Service
@RequiredArgsConstructor
public class ProspectContactService {

    private final AcquisitionProspectClient acquisitionProspectClient;
    private final AddressClient addressClient;
    private final ContactAddressRepository contactAddressRepository;
    private final ContactIdentityDocumentRetrievalService contactIdentityDocumentRetrievalService;

    @Transactional
    public TigoContactResponse updateContact(String reference,
            TigoProspectContactRequest request) {
        var addressEntities = contactAddressRepository.findAllByContactAddressId_Reference(reference);
        if (addressEntities.isEmpty()) {
            createAddress(request.getDeliveryAddress(), reference, AddressTypeEnum.DELIVERY);
            createAddress(request.getBillingAddress(), reference, AddressTypeEnum.BILLING);
        } else {
            updateAddress(request.getDeliveryAddress(), addressEntities, AddressTypeEnum.DELIVERY);
            updateAddress(request.getBillingAddress(), addressEntities, AddressTypeEnum.BILLING);
        }
        setDocumentIdentityInformation(reference, request);
        return acquisitionProspectClient.createOrUpdateContact(reference, request);
    }

    @Transactional
    public ContactAddressEntity createInstallationAddress(String reference, AddOfferAddressRequest request) {

        var addressEntities = contactAddressRepository
                .findByContactAddressId_ReferenceAndContactAddressId_Type(reference, AddressTypeEnum.INSTALLATION);

        if (addressEntities.isEmpty()) {
            return createInstallationAddress(request, reference, AddressTypeEnum.INSTALLATION);
        } else {
            return updateInstallationAddress(request, addressEntities.get());
        }
    }

    public AddressResponse getInstallationAddress(String reference) {

        var addressEntities = contactAddressRepository
                .findByContactAddressId_ReferenceAndContactAddressId_Type(reference, AddressTypeEnum.INSTALLATION);

        if (addressEntities.isPresent()) {
            return addressClient.getAddress(addressEntities.get().getAddressId());
        } else {
            return null;
        }
    }

    private void setDocumentIdentityInformation(String reference, TigoProspectContactRequest request) {
        var contactIdentityDocumentEntity = contactIdentityDocumentRetrievalService
                .getContactIdentityDocumentEntity(reference);
        request.setDocumentIdentifier(contactIdentityDocumentEntity.getDocumentIdentifier());
        request.setDocumentType(contactIdentityDocumentEntity.getDocumentType() != null
                ? contactIdentityDocumentEntity.getDocumentType().getName()
                : null);
        request.setNationality(contactIdentityDocumentEntity.getNationality());
    }

    private void createAddress(TigoAddressRequest request, String reference, AddressTypeEnum addressType) {
        var addressRequest = buildAddressRequest(request);
        var addressId = addressClient.createAddress(addressRequest);
        var contactAddressEntity = buildContactAddressEntity(reference, addressType, addressId);
        contactAddressRepository.save(contactAddressEntity);
    }

    private void updateAddress(TigoAddressRequest request,
            List<ContactAddressEntity> addressEntities,
            AddressTypeEnum addressType) {
        var optionalAddressId = addressEntities.stream().filter(
                contactAddressEntity -> addressType.equals(contactAddressEntity.getContactAddressId().getType()))
                .findFirst();
        optionalAddressId.ifPresent(contactAddressEntity -> addressClient
                .updateAddress(contactAddressEntity.getAddressId(), buildAddressRequest(request)));
    }

    private ContactAddressEntity createInstallationAddress(AddOfferAddressRequest request, String reference,
            AddressTypeEnum addressType) {
        var addressRequest = buildAddressRequest(request);
        var addressId = addressClient.createAddress(addressRequest);
        var contactAddressEntity = buildContactAddressEntity(reference, addressType, addressId);
        return contactAddressRepository.save(contactAddressEntity);
    }

    private ContactAddressEntity updateInstallationAddress(AddOfferAddressRequest request, ContactAddressEntity addressEntity) {

        addressClient.updateAddress(addressEntity.getAddressId(), buildAddressRequest(request));
        return addressEntity;
    }

}
