package com.tigo.galaxion.sales.facade.connector.prospectlead.domain.request;

import java.util.ArrayList;

import javax.validation.Valid;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Address;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.ChannelNotification;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Customer;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.CustomerReference;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Scoring;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;

@Builder
public record ProspectLeadRequest(
    @ApiModelProperty(value = "The address list")
    ArrayList<Address> address,
    @ApiModelProperty(value = "The customer")
    Customer customer,
    @ApiModelProperty(value = "The customer reference list")
    ArrayList<CustomerReference> customerReference,
    @ApiModelProperty(value = "The scoring")
    Scoring scoring,
    @ApiModelProperty(value = "The channel notification list")
    ArrayList<ChannelNotification> channelNotification
) {
} 
