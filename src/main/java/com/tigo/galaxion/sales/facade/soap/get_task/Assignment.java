
package com.tigo.galaxion.sales.facade.soap.get_task;

import javax.xml.bind.annotation.*;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for Assignment complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Assignment"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Key" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Revision" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Stamp" type="{http://crmsaleforce.resourcemanager.millicom.com/gettasksoap}Stamp"/&gt;
 *         &lt;element name="Task" type="{http://crmsaleforce.resourcemanager.millicom.com/gettasksoap}TaskAssigment"/&gt;
 *         &lt;element name="Start" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Finish" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Engineers" type="{http://crmsaleforce.resourcemanager.millicom.com/gettasksoap}Engineer"/&gt;
 *         &lt;element name="Comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Location" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BinaryData" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Latitude" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Longitude" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="GISDataSource" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="AssignedEngineers" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LogicPolicy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsCrewAssignment" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="CountryID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Street" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="City" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="State" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Postcode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="NonAvailabilityType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IgnoreInRoster" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ContractorIndex" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="StateSubdivision" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CitySubdivision" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="WorkAgreementID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ExternalRefID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsBreakIncluded" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="IncludedBreakDuration" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="AbsenceRequest" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ExternalComment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="AssignmentSource" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="PredictiveArea" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ArchiveStatus" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="StartTimeGMT" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="FinishTimeGMT" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="MobileKey" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Unit" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TaskModifiedTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="TaskCallID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TaskNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="TaskTypeCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CurrentTaskAppointmentTime" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="NewAssignmentStartDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="CurrentAssignmentStartDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Assignment", propOrder = {
    "key",
    "revision",
    "stamp",
    "task",
    "start",
    "finish",
    "engineers",
    "comment",
    "location",
    "binaryData",
    "latitude",
    "longitude",
    "gisDataSource",
    "assignedEngineers",
    "logicPolicy",
    "isCrewAssignment",
    "countryID",
    "street",
    "city",
    "state",
    "postcode",
    "nonAvailabilityType",
    "ignoreInRoster",
    "contractorIndex",
    "stateSubdivision",
    "citySubdivision",
    "id",
    "workAgreementID",
    "externalRefID",
    "isBreakIncluded",
    "includedBreakDuration",
    "absenceRequest",
    "externalComment",
    "assignmentSource",
    "predictiveArea",
    "archiveStatus",
    "startTimeGMT",
    "finishTimeGMT",
    "mobileKey",
    "unit",
    "taskModifiedTime",
    "taskCallID",
    "taskNumber",
    "taskTypeCategory",
    "currentTaskAppointmentTime",
    "newAssignmentStartDate",
    "currentAssignmentStartDate"
})
public class Assignment {

    @XmlElement(name = "Key")
    protected int key;
    @XmlElement(name = "Revision")
    protected int revision;
    @XmlElement(name = "Stamp", required = true)
    protected Stamp stamp;
    @XmlElement(name = "Task", required = true)
    protected TaskAssigment task;
    @XmlElement(name = "Start", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar start;
    @XmlElement(name = "Finish", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar finish;
    @XmlElement(name = "Engineers", required = true)
    protected Engineer engineers;
    @XmlElement(name = "Comment", required = true)
    protected String comment;
    @XmlElement(name = "Location", required = true)
    protected String location;
    @XmlElement(name = "BinaryData", required = true)
    protected String binaryData;
    @XmlElement(name = "Latitude")
    protected int latitude;
    @XmlElement(name = "Longitude")
    protected int longitude;
    @XmlElement(name = "GISDataSource")
    protected int gisDataSource;
    @XmlElement(name = "AssignedEngineers", required = true)
    protected String assignedEngineers;
    @XmlElement(name = "LogicPolicy", required = true)
    protected String logicPolicy;
    @XmlElement(name = "IsCrewAssignment")
    protected int isCrewAssignment;
    @XmlElement(name = "CountryID", required = true)
    protected String countryID;
    @XmlElement(name = "Street", required = true)
    protected String street;
    @XmlElement(name = "City", required = true)
    protected String city;
    @XmlElement(name = "State", required = true)
    protected String state;
    @XmlElement(name = "Postcode", required = true)
    protected String postcode;
    @XmlElement(name = "NonAvailabilityType", required = true)
    protected String nonAvailabilityType;
    @XmlElement(name = "IgnoreInRoster")
    protected int ignoreInRoster;
    @XmlElement(name = "ContractorIndex")
    protected int contractorIndex;
    @XmlElement(name = "StateSubdivision", required = true)
    protected String stateSubdivision;
    @XmlElement(name = "CitySubdivision", required = true)
    protected String citySubdivision;
    @XmlElement(name = "ID")
    protected int id;
    @XmlElement(name = "WorkAgreementID", required = true)
    protected String workAgreementID;
    @XmlElement(name = "ExternalRefID", required = true)
    protected String externalRefID;
    @XmlElement(name = "IsBreakIncluded")
    protected int isBreakIncluded;
    @XmlElement(name = "IncludedBreakDuration")
    protected int includedBreakDuration;
    @XmlElement(name = "AbsenceRequest", required = true)
    protected String absenceRequest;
    @XmlElement(name = "ExternalComment", required = true)
    protected String externalComment;
    @XmlElement(name = "AssignmentSource")
    protected int assignmentSource;
    @XmlElement(name = "PredictiveArea")
    protected int predictiveArea;
    @XmlElement(name = "ArchiveStatus", required = true)
    protected String archiveStatus;
    @XmlElement(name = "StartTimeGMT", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar startTimeGMT;
    @XmlElement(name = "FinishTimeGMT", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar finishTimeGMT;
    @XmlElement(name = "MobileKey", required = true)
    protected String mobileKey;
    @XmlElement(name = "Unit", required = true)
    protected String unit;
    @XmlElement(name = "TaskModifiedTime", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar taskModifiedTime;
    @XmlElement(name = "TaskCallID", required = true)
    protected String taskCallID;
    @XmlElement(name = "TaskNumber")
    protected int taskNumber;
    @XmlElement(name = "TaskTypeCategory", required = true)
    protected String taskTypeCategory;
    @XmlElement(name = "CurrentTaskAppointmentTime", required = true)
    protected String currentTaskAppointmentTime;
    @XmlElement(name = "NewAssignmentStartDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar newAssignmentStartDate;
    @XmlElement(name = "CurrentAssignmentStartDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar currentAssignmentStartDate;

    /**
     * Gets the value of the key property.
     * 
     */
    public int getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     */
    public void setKey(int value) {
        this.key = value;
    }

    /**
     * Gets the value of the revision property.
     * 
     */
    public int getRevision() {
        return revision;
    }

    /**
     * Sets the value of the revision property.
     * 
     */
    public void setRevision(int value) {
        this.revision = value;
    }

    /**
     * Gets the value of the stamp property.
     * 
     * @return
     *     possible object is
     *     {@link Stamp }
     *     
     */
    public Stamp getStamp() {
        return stamp;
    }

    /**
     * Sets the value of the stamp property.
     * 
     * @param value
     *     allowed object is
     *     {@link Stamp }
     *     
     */
    public void setStamp(Stamp value) {
        this.stamp = value;
    }

    /**
     * Gets the value of the task property.
     * 
     * @return
     *     possible object is
     *     {@link TaskAssigment }
     *     
     */
    public TaskAssigment getTask() {
        return task;
    }

    /**
     * Sets the value of the task property.
     * 
     * @param value
     *     allowed object is
     *     {@link TaskAssigment }
     *     
     */
    public void setTask(TaskAssigment value) {
        this.task = value;
    }

    /**
     * Gets the value of the start property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getStart() {
        return start;
    }

    /**
     * Sets the value of the start property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setStart(XMLGregorianCalendar value) {
        this.start = value;
    }

    /**
     * Gets the value of the finish property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFinish() {
        return finish;
    }

    /**
     * Sets the value of the finish property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFinish(XMLGregorianCalendar value) {
        this.finish = value;
    }

    /**
     * Gets the value of the engineers property.
     * 
     * @return
     *     possible object is
     *     {@link Engineer }
     *     
     */
    public Engineer getEngineers() {
        return engineers;
    }

    /**
     * Sets the value of the engineers property.
     * 
     * @param value
     *     allowed object is
     *     {@link Engineer }
     *     
     */
    public void setEngineers(Engineer value) {
        this.engineers = value;
    }

    /**
     * Gets the value of the comment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the location property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLocation() {
        return location;
    }

    /**
     * Sets the value of the location property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLocation(String value) {
        this.location = value;
    }

    /**
     * Gets the value of the binaryData property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBinaryData() {
        return binaryData;
    }

    /**
     * Sets the value of the binaryData property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBinaryData(String value) {
        this.binaryData = value;
    }

    /**
     * Gets the value of the latitude property.
     * 
     */
    public int getLatitude() {
        return latitude;
    }

    /**
     * Sets the value of the latitude property.
     * 
     */
    public void setLatitude(int value) {
        this.latitude = value;
    }

    /**
     * Gets the value of the longitude property.
     * 
     */
    public int getLongitude() {
        return longitude;
    }

    /**
     * Sets the value of the longitude property.
     * 
     */
    public void setLongitude(int value) {
        this.longitude = value;
    }

    /**
     * Gets the value of the gisDataSource property.
     * 
     */
    public int getGISDataSource() {
        return gisDataSource;
    }

    /**
     * Sets the value of the gisDataSource property.
     * 
     */
    public void setGISDataSource(int value) {
        this.gisDataSource = value;
    }

    /**
     * Gets the value of the assignedEngineers property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAssignedEngineers() {
        return assignedEngineers;
    }

    /**
     * Sets the value of the assignedEngineers property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAssignedEngineers(String value) {
        this.assignedEngineers = value;
    }

    /**
     * Gets the value of the logicPolicy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLogicPolicy() {
        return logicPolicy;
    }

    /**
     * Sets the value of the logicPolicy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLogicPolicy(String value) {
        this.logicPolicy = value;
    }

    /**
     * Gets the value of the isCrewAssignment property.
     * 
     */
    public int getIsCrewAssignment() {
        return isCrewAssignment;
    }

    /**
     * Sets the value of the isCrewAssignment property.
     * 
     */
    public void setIsCrewAssignment(int value) {
        this.isCrewAssignment = value;
    }

    /**
     * Gets the value of the countryID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCountryID() {
        return countryID;
    }

    /**
     * Sets the value of the countryID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCountryID(String value) {
        this.countryID = value;
    }

    /**
     * Gets the value of the street property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStreet() {
        return street;
    }

    /**
     * Sets the value of the street property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStreet(String value) {
        this.street = value;
    }

    /**
     * Gets the value of the city property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the city property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * Gets the value of the state property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getState() {
        return state;
    }

    /**
     * Sets the value of the state property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setState(String value) {
        this.state = value;
    }

    /**
     * Gets the value of the postcode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPostcode() {
        return postcode;
    }

    /**
     * Sets the value of the postcode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPostcode(String value) {
        this.postcode = value;
    }

    /**
     * Gets the value of the nonAvailabilityType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNonAvailabilityType() {
        return nonAvailabilityType;
    }

    /**
     * Sets the value of the nonAvailabilityType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNonAvailabilityType(String value) {
        this.nonAvailabilityType = value;
    }

    /**
     * Gets the value of the ignoreInRoster property.
     * 
     */
    public int getIgnoreInRoster() {
        return ignoreInRoster;
    }

    /**
     * Sets the value of the ignoreInRoster property.
     * 
     */
    public void setIgnoreInRoster(int value) {
        this.ignoreInRoster = value;
    }

    /**
     * Gets the value of the contractorIndex property.
     * 
     */
    public int getContractorIndex() {
        return contractorIndex;
    }

    /**
     * Sets the value of the contractorIndex property.
     * 
     */
    public void setContractorIndex(int value) {
        this.contractorIndex = value;
    }

    /**
     * Gets the value of the stateSubdivision property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStateSubdivision() {
        return stateSubdivision;
    }

    /**
     * Sets the value of the stateSubdivision property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStateSubdivision(String value) {
        this.stateSubdivision = value;
    }

    /**
     * Gets the value of the citySubdivision property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCitySubdivision() {
        return citySubdivision;
    }

    /**
     * Sets the value of the citySubdivision property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCitySubdivision(String value) {
        this.citySubdivision = value;
    }

    /**
     * Gets the value of the id property.
     * 
     */
    public int getID() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setID(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the workAgreementID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkAgreementID() {
        return workAgreementID;
    }

    /**
     * Sets the value of the workAgreementID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkAgreementID(String value) {
        this.workAgreementID = value;
    }

    /**
     * Gets the value of the externalRefID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExternalRefID() {
        return externalRefID;
    }

    /**
     * Sets the value of the externalRefID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExternalRefID(String value) {
        this.externalRefID = value;
    }

    /**
     * Gets the value of the isBreakIncluded property.
     * 
     */
    public int getIsBreakIncluded() {
        return isBreakIncluded;
    }

    /**
     * Sets the value of the isBreakIncluded property.
     * 
     */
    public void setIsBreakIncluded(int value) {
        this.isBreakIncluded = value;
    }

    /**
     * Gets the value of the includedBreakDuration property.
     * 
     */
    public int getIncludedBreakDuration() {
        return includedBreakDuration;
    }

    /**
     * Sets the value of the includedBreakDuration property.
     * 
     */
    public void setIncludedBreakDuration(int value) {
        this.includedBreakDuration = value;
    }

    /**
     * Gets the value of the absenceRequest property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAbsenceRequest() {
        return absenceRequest;
    }

    /**
     * Sets the value of the absenceRequest property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAbsenceRequest(String value) {
        this.absenceRequest = value;
    }

    /**
     * Gets the value of the externalComment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExternalComment() {
        return externalComment;
    }

    /**
     * Sets the value of the externalComment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExternalComment(String value) {
        this.externalComment = value;
    }

    /**
     * Gets the value of the assignmentSource property.
     * 
     */
    public int getAssignmentSource() {
        return assignmentSource;
    }

    /**
     * Sets the value of the assignmentSource property.
     * 
     */
    public void setAssignmentSource(int value) {
        this.assignmentSource = value;
    }

    /**
     * Gets the value of the predictiveArea property.
     * 
     */
    public int getPredictiveArea() {
        return predictiveArea;
    }

    /**
     * Sets the value of the predictiveArea property.
     * 
     */
    public void setPredictiveArea(int value) {
        this.predictiveArea = value;
    }

    /**
     * Gets the value of the archiveStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArchiveStatus() {
        return archiveStatus;
    }

    /**
     * Sets the value of the archiveStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArchiveStatus(String value) {
        this.archiveStatus = value;
    }

    /**
     * Gets the value of the startTimeGMT property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getStartTimeGMT() {
        return startTimeGMT;
    }

    /**
     * Sets the value of the startTimeGMT property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setStartTimeGMT(XMLGregorianCalendar value) {
        this.startTimeGMT = value;
    }

    /**
     * Gets the value of the finishTimeGMT property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFinishTimeGMT() {
        return finishTimeGMT;
    }

    /**
     * Sets the value of the finishTimeGMT property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFinishTimeGMT(XMLGregorianCalendar value) {
        this.finishTimeGMT = value;
    }

    /**
     * Gets the value of the mobileKey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMobileKey() {
        return mobileKey;
    }

    /**
     * Sets the value of the mobileKey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMobileKey(String value) {
        this.mobileKey = value;
    }

    /**
     * Gets the value of the unit property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnit() {
        return unit;
    }

    /**
     * Sets the value of the unit property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnit(String value) {
        this.unit = value;
    }

    /**
     * Gets the value of the taskModifiedTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTaskModifiedTime() {
        return taskModifiedTime;
    }

    /**
     * Sets the value of the taskModifiedTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTaskModifiedTime(XMLGregorianCalendar value) {
        this.taskModifiedTime = value;
    }

    /**
     * Gets the value of the taskCallID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskCallID() {
        return taskCallID;
    }

    /**
     * Sets the value of the taskCallID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskCallID(String value) {
        this.taskCallID = value;
    }

    /**
     * Gets the value of the taskNumber property.
     * 
     */
    public int getTaskNumber() {
        return taskNumber;
    }

    /**
     * Sets the value of the taskNumber property.
     * 
     */
    public void setTaskNumber(int value) {
        this.taskNumber = value;
    }

    /**
     * Gets the value of the taskTypeCategory property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskTypeCategory() {
        return taskTypeCategory;
    }

    /**
     * Sets the value of the taskTypeCategory property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskTypeCategory(String value) {
        this.taskTypeCategory = value;
    }

    /**
     * Gets the value of the currentTaskAppointmentTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCurrentTaskAppointmentTime() {
        return currentTaskAppointmentTime;
    }

    /**
     * Sets the value of the currentTaskAppointmentTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCurrentTaskAppointmentTime(String value) {
        this.currentTaskAppointmentTime = value;
    }

    /**
     * Gets the value of the newAssignmentStartDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getNewAssignmentStartDate() {
        return newAssignmentStartDate;
    }

    /**
     * Sets the value of the newAssignmentStartDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setNewAssignmentStartDate(XMLGregorianCalendar value) {
        this.newAssignmentStartDate = value;
    }

    /**
     * Gets the value of the currentAssignmentStartDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getCurrentAssignmentStartDate() {
        return currentAssignmentStartDate;
    }

    /**
     * Sets the value of the currentAssignmentStartDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setCurrentAssignmentStartDate(XMLGregorianCalendar value) {
        this.currentAssignmentStartDate = value;
    }

}
