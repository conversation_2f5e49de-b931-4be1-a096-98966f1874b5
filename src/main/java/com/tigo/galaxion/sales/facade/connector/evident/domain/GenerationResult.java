package com.tigo.galaxion.sales.facade.connector.evident.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class GenerationResult {
    @JsonProperty("codeResultOTP")
    private String codeResultOTP; 

    @JsonProperty("transactionIDOTP")
    private String transactionIDOTP; 

    @JsonProperty("resultOTP")
    private String resultOTP;

    @JsonProperty("codeResultOTPDescription")
    private String codeResultOTPDescription;

}
