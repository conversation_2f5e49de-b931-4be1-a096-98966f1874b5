package com.tigo.galaxion.sales.facade.connector.evident.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentIdentificationVerifyResponse {

    @JsonProperty("result")
    private String result;

    @JsonProperty("approval")
    private String approval;

    @JsonProperty("completeQuestions")
    private String completeQuestions;

    @JsonProperty("score")
    private String score;

    @JsonProperty("securityCode")
    private String securityCode;

    @JsonProperty("idQuestionary")
    private String idQuestionary;

    @JsonProperty("regQuestionary")
    private String regQuestionary;

    @JsonProperty("approved100PercentOK")
    private String approved100PercentOK;

    @JsonProperty("approvalDescription")
    private String approvalDescription;

}