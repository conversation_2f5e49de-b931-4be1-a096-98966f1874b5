package com.tigo.galaxion.sales.facade.domain.request;

import com.tigo.galaxion.sales.facade.domain.enumeration.PortInTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AddOfferToCrossSellCartRequest {

    @NotBlank
    @ApiModelProperty(value = "The code of the offer to add in the cart", required = true, example = "GOMO")
    private String catalogOfferCode;

    @NotBlank
    @ApiModelProperty(value = "The code of the tariff plan to add in the cart", required = true, example = "BASIC-24")
    private String catalogTariffPlanCode;

    @ApiModelProperty(value = "The port-in type [new port-in field]", example = "PREPAY_EXTERNAL")
    private PortInTypeEnum portInType;

    @ApiModelProperty(value = "Installation Address Id", example = "42")
    private Long installationAddressId;

    @ApiModelProperty(value = "The parent offer id", example = "567")
    private Long parentOfferId;

    @ApiModelProperty(value = "The parent subscription id", example = "567")
    private Long parentSubscriptionId;
}
