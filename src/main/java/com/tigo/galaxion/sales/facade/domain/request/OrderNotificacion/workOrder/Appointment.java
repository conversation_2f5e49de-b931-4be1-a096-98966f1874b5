package com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.workOrder;

import lombok.Data;

import java.util.List;

@Data
class Appointment {
    private List<Engineer> engineers;
    private String appointmentFinish;
    private String appointmentStart;
    private String assigmentFinish;
    private String assigmentStart;
    private String dueDate;
    private Integer duration;
    private String earlyStart;
    private String appointmentConfirmationStatus;
    private String appointmentConfirmationDate;
    private String appointmentComment;
}