//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:48:22 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.process_task_ex;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for Stamp complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Stamp"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="CreatedBy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TimeCreated" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="CreatingProcess" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ModifiedBy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TimeModified" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="ModifyingProcess" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Stamp", propOrder = {
    "createdBy",
    "timeCreated",
    "creatingProcess",
    "modifiedBy",
    "timeModified",
    "modifyingProcess"
})
public class Stamp {

    @XmlElement(name = "CreatedBy", required = true)
    protected String createdBy;
    @XmlElement(name = "TimeCreated", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar timeCreated;
    @XmlElement(name = "CreatingProcess")
    protected int creatingProcess;
    @XmlElement(name = "ModifiedBy", required = true)
    protected String modifiedBy;
    @XmlElement(name = "TimeModified", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar timeModified;
    @XmlElement(name = "ModifyingProcess")
    protected int modifyingProcess;

    /**
     * Gets the value of the createdBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreatedBy(String value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the timeCreated property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTimeCreated() {
        return timeCreated;
    }

    /**
     * Sets the value of the timeCreated property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTimeCreated(XMLGregorianCalendar value) {
        this.timeCreated = value;
    }

    /**
     * Gets the value of the creatingProcess property.
     * 
     */
    public int getCreatingProcess() {
        return creatingProcess;
    }

    /**
     * Sets the value of the creatingProcess property.
     * 
     */
    public void setCreatingProcess(int value) {
        this.creatingProcess = value;
    }

    /**
     * Gets the value of the modifiedBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * Sets the value of the modifiedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModifiedBy(String value) {
        this.modifiedBy = value;
    }

    /**
     * Gets the value of the timeModified property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTimeModified() {
        return timeModified;
    }

    /**
     * Sets the value of the timeModified property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTimeModified(XMLGregorianCalendar value) {
        this.timeModified = value;
    }

    /**
     * Gets the value of the modifyingProcess property.
     * 
     */
    public int getModifyingProcess() {
        return modifyingProcess;
    }

    /**
     * Sets the value of the modifyingProcess property.
     * 
     */
    public void setModifyingProcess(int value) {
        this.modifyingProcess = value;
    }

}
