package com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FieldServiceConfirmAppointmentWFE {
    private String addressId;
    private LocalDateTime at;
    private String contactUuid;
    private Long correlationId;
    private Long duration;
    private String reasonCode;
    private String source;
    private String timeSlotCode;
}