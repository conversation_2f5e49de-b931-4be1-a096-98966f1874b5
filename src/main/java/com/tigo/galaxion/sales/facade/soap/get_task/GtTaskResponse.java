
package com.tigo.galaxion.sales.facade.soap.get_task;

import javax.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Task" type="{http://crmsaleforce.resourcemanager.millicom.com/gettasksoap}TaskResponse"/&gt;
 *         &lt;element name="MessageError" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "task",
    "messageError"
})
@XmlRootElement(name = "GtTaskResponse")
public class GtTaskResponse {

    @XmlElement(name = "Task", required = true)
    protected TaskResponse task;
    @XmlElement(name = "MessageError", required = true)
    protected String messageError;

    /**
     * Gets the value of the task property.
     * 
     * @return
     *     possible object is
     *     {@link TaskResponse }
     *     
     */
    public TaskResponse getTask() {
        return task;
    }

    /**
     * Sets the value of the task property.
     * 
     * @param value
     *     allowed object is
     *     {@link TaskResponse }
     *     
     */
    public void setTask(TaskResponse value) {
        this.task = value;
    }

    /**
     * Gets the value of the messageError property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMessageError() {
        return messageError;
    }

    /**
     * Sets the value of the messageError property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMessageError(String value) {
        this.messageError = value;
    }

}
