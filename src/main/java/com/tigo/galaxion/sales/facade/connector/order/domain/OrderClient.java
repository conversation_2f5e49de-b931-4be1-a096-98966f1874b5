package com.tigo.galaxion.sales.facade.connector.order.domain;

import com.tigo.galaxion.sales.facade.connector.order.domain.response.OrderResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "tigo-orders-service",
        url = "${environment.url.tigo-orders-service}"
)
public interface OrderClient {

    @GetMapping("/productOrder")
    List<OrderResponse> getOrder(@RequestHeader("partyRole") String partyRole,
                                 @RequestParam("msisdn") Long msisdn, @RequestParam("user") String user,
                                 @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate,
                                 @RequestParam("page") Integer page, @RequestParam("size") Integer size);
}
