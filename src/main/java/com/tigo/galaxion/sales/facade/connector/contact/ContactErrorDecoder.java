package com.tigo.galaxion.sales.facade.connector.contact;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;
import com.tigo.galaxion.sales.facade.connector.contact.domain.problem.ContactProblem;
import org.zalando.problem.ThrowableProblem;

public class ContactErrorDecoder extends BaseProblemErrorDecoder {

    public ContactErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "contacts-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        return new ContactProblem(throwableProblem);
    }
}
