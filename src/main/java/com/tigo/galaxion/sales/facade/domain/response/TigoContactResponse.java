package com.tigo.galaxion.sales.facade.domain.response;

import com.tigo.galaxion.sales.facade.domain.enumeration.BillDeliveryTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Locale;

@Builder(toBuilder = true)
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoContactResponse {

    @ApiModelProperty(value = "First name of the person.", example = "Guido", required = true)
    private String firstName;

    @ApiModelProperty(value = "Last name of the person.", example = "van Rossum", required = true)
    private String lastName;

    @ApiModelProperty(value = "Birth date of the person.", example = "1951-05-04", required = true)
    private LocalDate birthDate;

    @ApiModelProperty(value = "Email of the customer", example = "<EMAIL>", required = true)
    private String email;

    @ApiModelProperty(value = "Primary phone number of the customer", example = "0812345678", required = true)
    private String primaryPhoneNumber;

    @ApiModelProperty(value = "Primary phone number of the customer", example = "0812345678", required = true)
    private String secondaryPhoneNumber;

    @ApiModelProperty(value = "Nationality of the customer", example = "CO")
    private String nationality;

    @ApiModelProperty(value = "Preferred language code", example = "en_MT", required = true)
    private Locale preferredLanguageCode;

    @ApiModelProperty(value = "Bill delivery type", example = "ONLINE", required = true)
    private BillDeliveryTypeEnum billDeliveryType;

    @Setter
    private ContactIdentityDocumentResponse identityDocument;

    private TigoPermissionGroupListResponse permissions;

    @Setter
    private TigoAddressResponse billingAddress;

    @Setter
    private TigoAddressResponse deliveryAddress;
}
