package com.tigo.galaxion.sales.facade.connector.account;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.tigo.galaxion.sales.facade.connector.account.domain.request.CreateAddOnToService;
import com.tigo.galaxion.sales.facade.connector.account.domain.request.CreateInclusiveEquipmentRequest;
import com.tigo.galaxion.sales.facade.connector.account.domain.request.SearchAccountContactV3Request;
import com.tigo.galaxion.sales.facade.connector.account.domain.response.AccountsCategoriesResponseBody;
import com.tigo.galaxion.sales.facade.connector.account.domain.response.ContactV3Response;
import com.tigo.galaxion.sales.facade.connector.account.domain.response.EquipmentWithoutAddonV3Response;

@FeignClient(value = "accounts-service", url = "${environment.url.accounts-service}", configuration = AccountErrorDecoder.class)
public interface AccountClient {

    @GetMapping("/api/v3/accounts/contacts")
    List<ContactV3Response> searchAccountContact(@SpringQueryMap SearchAccountContactV3Request searchAccountContactV3Request);

    @PostMapping("/api/v2/services/{service_id}/add-ons")
    Integer addAddOnToService(@PathVariable("service_id") String serviceId, @RequestBody CreateAddOnToService addOnId);

    @PostMapping("/api/v3/subscriptions/{subscription_id}/inclusive-equipments")
    List<EquipmentWithoutAddonV3Response> createInclusiveEquipment(@PathVariable("subscription_id") Integer subscriptionId, @RequestBody List<CreateInclusiveEquipmentRequest> createInclusiveEquipmentRequest);

    @GetMapping("api/v3/accounts/categories")
    List<AccountsCategoriesResponseBody> getAccountsCategories();
}
