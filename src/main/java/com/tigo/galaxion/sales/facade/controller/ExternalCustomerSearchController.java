package com.tigo.galaxion.sales.facade.controller;

import java.time.LocalDate;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomer;
import com.tigo.galaxion.sales.facade.services.ExternalCustomerSearchService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

@Api
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1")
public class ExternalCustomerSearchController {

  private final ExternalCustomerSearchService externalCustomerSearchService;

  @GetMapping("/recognize/{idType}/id/{idNumber}/{expeditionDate}")
  @ApiOperation("Get Recognize.")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "expeditionDate", value = "Date in format yyyy-MM-dd", example = "2024-09-16", required = false, dataType = "string", paramType = "path")
  })
  @ApiResponses(value = {
      @ApiResponse(code = 200, message = "Recognize details."),
      @ApiResponse(code = 404, message = "Recognize not found (Problem title = 'recognize-not-found')")
  })
  public ExternalCustomer getProspect(@PathVariable("idType") String idType, @PathVariable("idNumber") String idNumber,
      @PathVariable("expeditionDate") String expeditionDate) throws Exception {

    return externalCustomerSearchService.searchCustomerByExternalId(idType, idNumber, expeditionDate);
  }
}
