package com.tigo.galaxion.sales.facade.connector.contact.domain.request;

import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Builder
public class PatchAddressRequest {

    private AddressTypeEnum type;
    private String addressLine1;
    private String addressLine2;
    private String area;
    private String town;
    private String street;
    private String streetNumber;
    private String code;

}
