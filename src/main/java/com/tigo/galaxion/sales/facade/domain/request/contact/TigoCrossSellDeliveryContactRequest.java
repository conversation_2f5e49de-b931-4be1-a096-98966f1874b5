package com.tigo.galaxion.sales.facade.domain.request.contact;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoCrossSellDeliveryContactRequest {

    @NotBlank
    @ApiModelProperty(value = "The first name of the contact", example = "John", required = true)
    private String firstName;

    @NotBlank
    @ApiModelProperty(value = "The last name of the contact", example = "Doe", required = true)
    private String lastName;

    @NotBlank
    @Email
    @ApiModelProperty(value = "The email of the contact", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank
    @ApiModelProperty(value = "The mobile number of the contact", example = "0881275643", required = true)
    private String phoneNumber;

    @NotNull
    @ApiModelProperty(value = "The delivery address", required = true)
    @Valid
    private TigoAddressRequest deliveryAddress;
}
