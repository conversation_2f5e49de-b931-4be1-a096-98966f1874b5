package com.tigo.galaxion.sales.facade.controller;

import javax.validation.Valid;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.v2.ServiceActivationV2Request;
import com.tigo.galaxion.sales.facade.domain.response.order_notification.ErrorResponse;
import com.tigo.galaxion.sales.facade.domain.response.order_notification.ServiceActivationResponse;
import com.tigo.galaxion.sales.facade.services.order_notification.ServiceActivationV2Service;

import feign.FeignException;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v2/orders")
public class OrderNotificationV2Controller {

    private final ServiceActivationV2Service serviceActivationV2Service;

    @PostMapping("/field-service/{orderId}/services/{serviceId}")
    public ResponseEntity<?> serviceActivation(@PathVariable("orderId") String orderId,
            @PathVariable("serviceId") String serviceId,
            @Valid @RequestBody ServiceActivationV2Request request) {

        try {
            var trackingNumber = serviceActivationV2Service.sendNotification(orderId, serviceId, request);

            return new ResponseEntity<ServiceActivationResponse>(
                    ServiceActivationResponse
                            .builder()
                            .trackingNumber(trackingNumber)
                            .resultMessage("Transaction in progress.")
                            .build(),
                    HttpStatus.OK);
        } catch (FeignException.BadRequest ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "Error al procesar la señal: el proceso " + serviceId
                            + " no existe o no está esperando una señal.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
        } catch (FeignException ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    "Error al intentar contactar el servicio de Workflow Engine.");
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    e.getMessage());
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}