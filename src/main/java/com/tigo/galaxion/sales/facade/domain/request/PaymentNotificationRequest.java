package com.tigo.galaxion.sales.facade.domain.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Builder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PaymentNotificationRequest {
    @NotBlank
    @ApiModelProperty(value = "Notification Name.", required = true, example = "COMPLETED")
    @Size(max = 60)
    @JsonProperty("paymentStatus")
    private String paymentStatus;

    @NotBlank
    @ApiModelProperty(value = "Payment Date.", required = true, example = "2024-08-27T19:05:12")
    @Size(max = 60)
    @JsonProperty("paymentDate")
    private String paymentDate;
}
