package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.contact;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AcquisitionPermissionGroupRequest {

    @ApiModelProperty(value = "The code of the group.", example = "ACTIVE_CUSTOMER", required = true)
    private String permissionGroup;

    private List<AcquisitionPermissionRequest> permissions;
}
