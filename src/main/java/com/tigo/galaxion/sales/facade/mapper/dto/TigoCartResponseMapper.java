package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AdditionalAllowedOfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoAdditionalAllowedOfferResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.tigo.galaxion.sales.facade.mapper.dto.TigoOfferResponseMapper.buildTigoRecurringAmountByOccurrenceResponses;

@Service
@RequiredArgsConstructor
public class TigoCartResponseMapper {

    private final TigoOfferResponseMapper tigoOfferResponseMapper;

    public TigoCartResponse buildTigoCartResponse(CartResponse cartResponse) {
        return TigoCartResponse.builder()
                               .uuid(cartResponse.getUuid())
                               .creditScore(cartResponse.getCreditScore())
                               .agreedTermsAndConditions(cartResponse.getAgreedTermsAndConditions())
                               .channelGroup(cartResponse.getChannelGroup())
                               .amountVatIncluded(TigoOfferResponseMapper.buildTigoAmountResponse(cartResponse.getAmountVatIncluded()))
                               .amountVatExcluded(TigoOfferResponseMapper.buildTigoAmountResponse(cartResponse.getAmountVatExcluded()))
                               .offers(tigoOfferResponseMapper.buildTigoOfferResponses(cartResponse.getOffers()))
                               .numberAdditionalAllowedOffers(buildTigoAdditionalAllowedOfferResponses(cartResponse.getNumberAdditionalAllowedOffers()))
                               .recurringAmountsByOccurrence(buildTigoRecurringAmountByOccurrenceResponses(cartResponse.getRecurringAmountsByOccurrence()))
                               .build();
    }

    private static TigoAdditionalAllowedOfferResponse buildTigoAdditionalAllowedOfferResponse(AdditionalAllowedOfferResponse response) {
        return TigoAdditionalAllowedOfferResponse.builder()
                                                 .serviceGroup(response.getServiceGroup())
                                                 .number(response.getNumber())
                                                 .build();
    }

    private static List<TigoAdditionalAllowedOfferResponse> buildTigoAdditionalAllowedOfferResponses(List<AdditionalAllowedOfferResponse> responses) {
        return responses.stream().map(TigoCartResponseMapper::buildTigoAdditionalAllowedOfferResponse).toList();
    }
}
