package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.domain.problem.OfferNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.model.entity.OfferEntity;
import com.tigo.galaxion.sales.facade.model.repository.OfferRepository;
import com.tigo.galaxion.sales.facade.services.retrieval.OfferRetrievalService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class OfferSimDeliveryChoiceService {

    private final CartService cartService;
    private final OfferRepository offerRepository;
    private final OfferRetrievalService offerRetrievalService;
    private final ContractSignatureOptionService contractSignatureOptionService;

    @Transactional
    public TigoCartResponse updateForProspect(String reference, Long offerId, String deliveryType) {
        var tigoCartResponse = cartService.getCartForProspect(reference);
        return update(reference, tigoCartResponse, offerId, deliveryType);
    }

    @Transactional
    public TigoCartResponse updateForCrossSell(String reference, Long offerId, String deliveryType) {
        var tigoCartResponse = cartService.getCartForCrossSell(reference);
        return update(reference, tigoCartResponse, offerId, deliveryType);
    }

    private TigoCartResponse update(String prospectReference, TigoCartResponse tigoCartResponse, Long offerId, String deliveryType) {
        setIsShipping(offerId, deliveryType, tigoCartResponse);
        saveDeliveryType(offerId, deliveryType);
        contractSignatureOptionService.updateContractSignatureOption(prospectReference, null);
        return tigoCartResponse;
    }

    private static void setIsShipping(Long offerId, String deliveryType, TigoCartResponse tigoCartResponse) {
        var offer = tigoCartResponse.getOffers().stream().filter(tigoOfferResponse -> tigoOfferResponse.getId().equals(offerId)).findFirst();
        if (offer.isEmpty()) {
            throw new OfferNotFoundProblem(offerId);
        }
        offer.get().setSimDeliveryType(deliveryType);
    }

    private void saveDeliveryType(Long offerId, String deliveryType) {
        var offerMobileShipSimEntity = OfferEntity
                .builder()
                .offerId(offerId)
                .simDeliveryType(deliveryType)
                .build();
        offerRepository.save(offerMobileShipSimEntity);
    }

    @Transactional
    public void delete(Long offerId) {
        var offer = offerRetrievalService.get(offerId);
        offer.ifPresent(offerRepository::delete);
    }
}
