//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:50:36 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.multiple_operations;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.tigo.galaxion.sales.facade.soap.multiple_operations package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.tigo.galaxion.sales.facade.soap.multiple_operations
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ExecuteMultipleOperations }
     * 
     */
    public ExecuteMultipleOperations createExecuteMultipleOperations() {
        return new ExecuteMultipleOperations();
    }

    /**
     * Create an instance of {@link StandardOperations }
     * 
     */
    public StandardOperations createStandardOperations() {
        return new StandardOperations();
    }

    /**
     * Create an instance of {@link ExecuteMultipleOperationsResponse }
     * 
     */
    public ExecuteMultipleOperationsResponse createExecuteMultipleOperationsResponse() {
        return new ExecuteMultipleOperationsResponse();
    }

    /**
     * Create an instance of {@link OperationsResults }
     * 
     */
    public OperationsResults createOperationsResults() {
        return new OperationsResults();
    }

    /**
     * Create an instance of {@link StandardOperation }
     * 
     */
    public StandardOperation createStandardOperation() {
        return new StandardOperation();
    }

    /**
     * Create an instance of {@link BaseObjectWrapper }
     * 
     */
    public BaseObjectWrapper createBaseObjectWrapper() {
        return new BaseObjectWrapper();
    }

    /**
     * Create an instance of {@link BaseObject }
     * 
     */
    public BaseObject createBaseObject() {
        return new BaseObject();
    }

    /**
     * Create an instance of {@link MCServices }
     * 
     */
    public MCServices createMCServices() {
        return new MCServices();
    }

    /**
     * Create an instance of {@link MCServicesExternalRef }
     * 
     */
    public MCServicesExternalRef createMCServicesExternalRef() {
        return new MCServicesExternalRef();
    }

    /**
     * Create an instance of {@link MCCustomerAsset }
     * 
     */
    public MCCustomerAsset createMCCustomerAsset() {
        return new MCCustomerAsset();
    }

    /**
     * Create an instance of {@link MCCustomerAssetExternalRef }
     * 
     */
    public MCCustomerAssetExternalRef createMCCustomerAssetExternalRef() {
        return new MCCustomerAssetExternalRef();
    }

    /**
     * Create an instance of {@link Error }
     * 
     */
    public Error createError() {
        return new Error();
    }

    /**
     * Create an instance of {@link BusinessObject }
     * 
     */
    public BusinessObject createBusinessObject() {
        return new BusinessObject();
    }

    /**
     * Create an instance of {@link W6RequestedProperties }
     * 
     */
    public W6RequestedProperties createW6RequestedProperties() {
        return new W6RequestedProperties();
    }

    /**
     * Create an instance of {@link Stamp }
     * 
     */
    public Stamp createStamp() {
        return new Stamp();
    }

    /**
     * Create an instance of {@link MCServiceStatusReference }
     * 
     */
    public MCServiceStatusReference createMCServiceStatusReference() {
        return new MCServiceStatusReference();
    }

    /**
     * Create an instance of {@link MCServiceCompletionReasonReference }
     * 
     */
    public MCServiceCompletionReasonReference createMCServiceCompletionReasonReference() {
        return new MCServiceCompletionReasonReference();
    }

    /**
     * Create an instance of {@link MCMaterialTypeCategoryReference }
     * 
     */
    public MCMaterialTypeCategoryReference createMCMaterialTypeCategoryReference() {
        return new MCMaterialTypeCategoryReference();
    }

    /**
     * Create an instance of {@link MCMaterialTypeReference }
     * 
     */
    public MCMaterialTypeReference createMCMaterialTypeReference() {
        return new MCMaterialTypeReference();
    }

    /**
     * Create an instance of {@link MCPartTypeReference }
     * 
     */
    public MCPartTypeReference createMCPartTypeReference() {
        return new MCPartTypeReference();
    }

    /**
     * Create an instance of {@link MCProvisioningStatusesReference }
     * 
     */
    public MCProvisioningStatusesReference createMCProvisioningStatusesReference() {
        return new MCProvisioningStatusesReference();
    }

    /**
     * Create an instance of {@link MCServicesReference }
     * 
     */
    public MCServicesReference createMCServicesReference() {
        return new MCServicesReference();
    }

    /**
     * Create an instance of {@link AreaReference }
     * 
     */
    public AreaReference createAreaReference() {
        return new AreaReference();
    }

    /**
     * Create an instance of {@link AggregateValue }
     * 
     */
    public AggregateValue createAggregateValue() {
        return new AggregateValue();
    }

    /**
     * Create an instance of {@link ObjectReference }
     * 
     */
    public ObjectReference createObjectReference() {
        return new ObjectReference();
    }

    /**
     * Create an instance of {@link SucceededGetObjectOperation }
     * 
     */
    public SucceededGetObjectOperation createSucceededGetObjectOperation() {
        return new SucceededGetObjectOperation();
    }

    /**
     * Create an instance of {@link FailedOperation }
     * 
     */
    public FailedOperation createFailedOperation() {
        return new FailedOperation();
    }

    /**
     * Create an instance of {@link SucceededOperation }
     * 
     */
    public SucceededOperation createSucceededOperation() {
        return new SucceededOperation();
    }

    /**
     * Create an instance of {@link ObjectReferenceWrapper }
     * 
     */
    public ObjectReferenceWrapper createObjectReferenceWrapper() {
        return new ObjectReferenceWrapper();
    }

    /**
     * Create an instance of {@link OperationResult }
     * 
     */
    public OperationResult createOperationResult() {
        return new OperationResult();
    }

    /**
     * Create an instance of {@link MCPartsInStock }
     * 
     */
    public MCPartsInStock createMCPartsInStock() {
        return new MCPartsInStock();
    }

    /**
     * Create an instance of {@link AggregateStamp }
     * 
     */
    public AggregateStamp createAggregateStamp() {
        return new AggregateStamp();
    }

}
