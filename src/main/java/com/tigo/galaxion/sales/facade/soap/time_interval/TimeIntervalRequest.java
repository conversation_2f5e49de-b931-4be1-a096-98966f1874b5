//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.06.17 at 02:45:10 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.time_interval;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ExtendedGetAppointment" type="{http://crmsaleforce.resourcemanager.millicom.com/timeintervalsoap}ExtendedGetAppointment"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "extendedGetAppointment"
})
@XmlRootElement(name = "TimeIntervalRequest")
public class TimeIntervalRequest {

    @XmlElement(name = "ExtendedGetAppointment", required = true)
    protected ExtendedGetAppointment extendedGetAppointment;

    /**
     * Gets the value of the extendedGetAppointment property.
     * 
     * @return
     *     possible object is
     *     {@link ExtendedGetAppointment }
     *     
     */
    public ExtendedGetAppointment getExtendedGetAppointment() {
        return extendedGetAppointment;
    }

    /**
     * Sets the value of the extendedGetAppointment property.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendedGetAppointment }
     *     
     */
    public void setExtendedGetAppointment(ExtendedGetAppointment value) {
        this.extendedGetAppointment = value;
    }

}
