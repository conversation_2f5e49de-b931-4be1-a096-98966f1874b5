package com.tigo.galaxion.sales.facade.connector.coverage.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class CoverageResponse {

    @JsonProperty("response")
    private String response;

    @JsonProperty("message")
    private String message;

    @JsonProperty("successful")
    private boolean successful;

    @JsonProperty("tecnologia")
    private String tecnologia;

    @JsonProperty("coordenadaX")
    private String coordenadaX;

    @JsonProperty("coordenadaY")
    private String coordenadaY;

    @JsonProperty("segmento")
    private String segmento;

    @JsonProperty("zona")
    private String zona;
}