package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.CommitmentDurationEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class BaseOfferResponse {

    private String catalogCode;

    private String serviceGroup;

    private List<String> serviceDomains;

    private String description;

    private String comment;

    private String catalogTariffPlanCode;

    @Builder.Default
    private List<TariffPlanDiscountResponse> tariffPlanDiscounts = new ArrayList<>();

    private Boolean simOnly;

    private String tariffPlanDescription;

    private CommitmentDurationEnum commitmentDuration;

    private AmountResponse amountVatIncluded;

    private AmountResponse amountVatExcluded;

    private Long displayOrder;

    @Builder.Default
    private List<UsageResponse> usages = new ArrayList<>();

    @Builder.Default
    private List<ChargeResponse> charges = new ArrayList<>();

    private String broadbandTechnology;

    @Builder.Default
    private List<RecurringAmountByOccurrenceResponse> recurringAmountsByOccurrence = new ArrayList<>();
}
