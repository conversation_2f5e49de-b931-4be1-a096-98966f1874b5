package com.tigo.galaxion.sales.facade.domain.response;

import com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ContactIdentityDocumentResponse {

    @ApiModelProperty(value = "The document identifier", example = "0123456789", required = true)
    private String documentIdentifier;

    @ApiModelProperty(value = "The document type", example = "NIT", required = true)
    private IdDocumentTypeEnum documentType;

    @ApiModelProperty(value = "The nationality", example = "COLOMBIAN", required = true)
    private String nationality;
}
