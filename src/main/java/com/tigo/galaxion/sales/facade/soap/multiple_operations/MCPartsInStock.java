//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:50:36 PM CST 
//

package com.tigo.galaxion.sales.facade.soap.multiple_operations;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * Java class for MCPartsInStock complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="MCPartsInStock"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Stamp" type="{http://www.clicksoftware.com}AggregateStamp" minOccurs="0"/&gt;
 *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="Description" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="Serial" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="EngineerID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MaterialTypeCategory" type="{http://www.clicksoftware.com}MCMaterialTypeCategoryReference" minOccurs="0"/&gt;
 *         &lt;element name="MaterialType" type="{http://www.clicksoftware.com}MCMaterialTypeReference" minOccurs="0"/&gt;
 *         &lt;element name="PartType" type="{http://www.clicksoftware.com}MCPartTypeReference" minOccurs="0"/&gt;
 *         &lt;element name="Unit" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="QuantityInStock" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="Status" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TaskCallID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TaskNumber" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="DeletionRequestDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="IsUsed" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ExternalRefID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MobileKey" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="StatusChanged" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="Area" type="{http://www.clicksoftware.com}AreaReference" minOccurs="0"/&gt;
 *         &lt;element name="Service" type="{http://www.clicksoftware.com}MCServicesReference" minOccurs="0"/&gt;
 *         &lt;element name="ProvisioningComment" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ProvisioningAction" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ProvisioningMsgTrigger" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ProvisioningStatus" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ServiceName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MCPartsInStock", propOrder = {
        "stamp",
        "id",
        "description",
        "serial",
        "engineerID",
        "materialTypeCategory",
        "materialType",
        "partType",
        "unit",
        "quantityInStock",
        "status",
        "taskCallID",
        "taskNumber",
        "deletionRequestDate",
        "isUsed",
        "externalRefID",
        "mobileKey",
        "statusChanged",
        "area",
        "service",
        "provisioningComment",
        "provisioningAction",
        "provisioningMsgTrigger",
        "provisioningStatus",
        "serviceName"
})
@Data
@NoArgsConstructor
public class MCPartsInStock {

    @XmlElement(name = "Stamp")
    protected AggregateStamp stamp;
    @XmlElement(name = "ID")
    protected String id;
    @XmlElement(name = "Description")
    protected String description;
    @XmlElement(name = "Serial")
    protected String serial;
    @XmlElement(name = "EngineerID")
    protected String engineerID;
    @XmlElement(name = "MaterialTypeCategory")
    protected MCMaterialTypeCategoryReference materialTypeCategory;
    @XmlElement(name = "MaterialType")
    protected MCMaterialTypeReference materialType;
    @XmlElement(name = "PartType")
    protected MCPartTypeReference partType;
    @XmlElement(name = "Unit")
    protected String unit;
    @XmlElement(name = "QuantityInStock")
    protected Integer quantityInStock;
    @XmlElement(name = "Status")
    protected String status;
    @XmlElement(name = "TaskCallID")
    protected String taskCallID;
    @XmlElement(name = "TaskNumber")
    protected Integer taskNumber;
    @XmlElement(name = "DeletionRequestDate")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar deletionRequestDate;
    @XmlElement(name = "IsUsed")
    protected Boolean isUsed;
    @XmlElement(name = "ExternalRefID")
    protected String externalRefID;
    @XmlElement(name = "MobileKey")
    protected String mobileKey;
    @XmlElement(name = "StatusChanged")
    protected Boolean statusChanged;
    @XmlElement(name = "Area")
    protected AreaReference area;
    @XmlElement(name = "Service")
    protected MCServicesReference service;
    @XmlElement(name = "ProvisioningComment")
    protected String provisioningComment;
    @XmlElement(name = "ProvisioningAction")
    protected String provisioningAction;
    @XmlElement(name = "ProvisioningMsgTrigger")
    protected Boolean provisioningMsgTrigger;
    @XmlElement(name = "ProvisioningStatus")
    protected String provisioningStatus;
    @XmlElement(name = "ServiceName")
    protected String serviceName;
}
