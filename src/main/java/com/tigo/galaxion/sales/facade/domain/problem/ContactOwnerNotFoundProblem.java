package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class ContactOwnerNotFoundProblem extends AbstractThrowableProblem {

    public ContactOwnerNotFoundProblem(String accountId) {
        super(null,
              "contact-owner-not-found",
              Status.NOT_FOUND,
              String.format("Contact owner not found for account #%s", accountId));
    }
}
