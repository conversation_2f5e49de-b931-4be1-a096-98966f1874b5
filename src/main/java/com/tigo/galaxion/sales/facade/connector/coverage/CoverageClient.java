package com.tigo.galaxion.sales.facade.connector.coverage;

import com.tigo.galaxion.sales.facade.connector.coverage.domain.response.CoverageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "coverageClient", url = "${environment.url.service-coverage}")
public interface CoverageClient {

    @GetMapping("/{id_barrio}/{id_calle}/{id_casa}")
    CoverageResponse getCoverage(
            @PathVariable("id_barrio") String idBarrio,
            @PathVariable("id_calle") String idCalle,
            @PathVariable("id_casa") String idCasa
    );
}
