package com.tigo.galaxion.sales.facade.controller;


import com.github.fge.jsonpatch.mergepatch.JsonMergePatch;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart.CartAddOnRequest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart.CartEquipmentRequest;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.services.CartService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Api
@RestController
@RequiredArgsConstructor
public class CartController {

    private final CartService cartService;

    @GetMapping("/api/v1/prospects/{prospect_reference}/carts")
    public TigoCartResponse getCartForProspect(
            @PathVariable("prospect_reference") String prospectReference) {
        return cartService.getCartForProspect(prospectReference);
    }

    @GetMapping("/cross-sells/{prospect_reference}/carts")
    public TigoCartResponse getCartForCrossSell(
            @PathVariable("prospect_reference") String crossSellReference) {
        return cartService.getCartForCrossSell(crossSellReference);
    }

    @PatchMapping("/api/v1/prospects/{prospect_reference}/carts/offers/{offer_id}")
    public TigoCartResponse updateOfferInCart (
        @ApiParam(value = "The prospect reference", required = true, example = "53ODP1O7") @PathVariable("prospect_reference") String prospectReference,
        @ApiParam(value = "The id of the offer in the cart", required = true, example = "5") @PathVariable("offer_id") long offerId,
        @ApiParam(value = """
                    A JsonMergePatch object as defined in https://tools.ietf.org/html/rfc7396
                    Example:
                    {
                      "directoryPreference": "UNLISTED",
                      "topUpAmount" : 50,
                      "subscriberBirthDate": "1951-03-25"
                    }""")
        @RequestBody JsonMergePatch patchRequest
    ){
        return cartService.updateOffer(prospectReference, offerId, patchRequest);
    }

    @PostMapping("/api/v1/prospects/{prospect_reference}/carts/offers/{offer_id}/add-ons")
    public TigoCartResponse addAddOnToCart(
        @ApiParam(value = "The prospect reference", required = true, example = "53ODP1O7") @PathVariable("prospect_reference") String prospectReference,
        @ApiParam(value = "The id of the offer in the cart", required = true, example = "5") @PathVariable("offer_id") Long offerId,
        @Valid @RequestBody CartAddOnRequest body
    ){
        return cartService.addAddOnToCart(prospectReference, offerId, body);
    }

    @DeleteMapping("/api/v1/prospects/{prospect_reference}/carts/offers/{offer_id}/add-ons/{addOn_id}")
    public TigoCartResponse deleteAddOnFromCart(
        @ApiParam(value = "The prospect reference", required = true, example = "53ODP1O7") @PathVariable("prospect_reference") String prospectReference,
        @ApiParam(value = "The id of the offer in the cart", required = true, example = "5") @PathVariable("offer_id") Long offerId,
        @ApiParam(value = "The id of the offer in the cart", required = true, example = "5") @PathVariable("addOn_id") Long addOnId
    ){
        return cartService.deleteAddOnFromCart(prospectReference, offerId, addOnId);
    }

    @PostMapping("/api/v1/prospects/{prospect_reference}/carts/offers/{offer_id}/equipments")
    public TigoCartResponse addEquipmentToCart(
        @ApiParam(value = "The prospect reference", required = true, example = "53ODP1O7") @PathVariable("prospect_reference") String prospectReference,
        @ApiParam(value = "The id of the offer in the cart", required = true, example = "5") @PathVariable("offer_id") Long offerId,
        @Valid @RequestBody CartEquipmentRequest body
    ){
        return cartService.addEquipmentToCart(prospectReference, offerId, body);
    }

    @DeleteMapping("/api/v1/prospects/{prospect_reference}/carts/offers/{offer_id}/equipments/{equipment_id}")
    public TigoCartResponse deleteEquipmentFromCart(
        @ApiParam(value = "The prospect reference", required = true, example = "53ODP1O7") @PathVariable("prospect_reference") String prospectReference,
        @ApiParam(value = "The id of the offer in the cart", required = true, example = "5") @PathVariable("offer_id") Long offerId,
        @ApiParam(value = "The id of the offer in the cart", required = true, example = "5") @PathVariable("equipment_id") Long equipmentId
    ){
        return cartService.deleteEquipmentFromCart(prospectReference, offerId, equipmentId);
    }
}
