package com.tigo.galaxion.sales.facade.connector.account.domain.response;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.tigo.galaxion.sales.facade.connector.account.domain.enums.Status;

import lombok.Data;

@Data
public class EquipmentWithoutAddonV3Response {
	private String activatedAt;
	private Integer addonId;
	@NotBlank
	private String catalogCode;
	private Boolean deviceEnrolled;
	private EquipmentFinancingV3Response equipmentFinancing;
	@NotNull
	private Integer id;
	@NotNull
	private Boolean inclusive;
	private String orderReference;
	private Integer overriddenPrice;
	private String partNumber;
	private String pricePlanCatalogCode;
	private Boolean primary;
	private String serialNumber;
	private Integer serviceId;
	private List<Integer> serviceIds;
	private Status status;
	private Integer subscriptionId;
	private String terminatedAt;
	private String type;
}
