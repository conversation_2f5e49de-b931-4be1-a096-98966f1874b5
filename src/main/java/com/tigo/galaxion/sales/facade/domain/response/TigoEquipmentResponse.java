package com.tigo.galaxion.sales.facade.domain.response;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.EquipmentFinancingResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.SubsidyResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "type", visible = true, defaultImpl = TigoEquipmentResponse.class)
@JsonSubTypes({
        @JsonSubTypes.Type(value = TigoHandsetResponse.class, name = EquipmentTypeEnum.Constant.HANDSET),
        @JsonSubTypes.Type(value = TigoMobileBroadbandModemResponse.class, name = EquipmentTypeEnum.Constant.MOBILE_BROADBAND_MODEM),
        @JsonSubTypes.Type(value = TigoMobileBroadbandModemResponse.class, name = EquipmentTypeEnum.Constant.ACCESSORY),
})
public class TigoEquipmentResponse {

    @ApiModelProperty(value = "The ID of the equipment in the offer", example = "789", required = true)
    private Long id;

    @ApiModelProperty(value = "The handset code", example = "GALC1066S", required = true)
    private String catalogCode;

    @ApiModelProperty(value = "The equipment's IMEI", example = "990000862471854")
    private String imei;

    @ApiModelProperty(value = "The amount to be paid vat included", required = true)
    private TigoAmountResponse amountVatIncluded;

    @ApiModelProperty(value = "The amount to be paid vat excluded", required = true)
    private TigoAmountResponse amountVatExcluded;

    @ApiModelProperty(value = "The Dongle color", required = true, example = "BLACK")
    private String color;

    @ApiModelProperty(value = "The Dongle color code", example = "#000000", required = true)
    private String colorCode;

    @ApiModelProperty(value = "The equipment description", example = "My equipment", required = true)
    private String description;

    @ApiModelProperty(value = "The equipment's manufacturer")
    private String manufacturer;

    @ApiModelProperty(value = "The equipment's model", example = "MyPhone", required = true)
    private String model;

    @ApiModelProperty(value = "The equipment's inventory code", example = "BAPMY", required = true)
    private String inventoryCode;

    @ApiModelProperty(value = "The equipment's charge", required = true)
    private TigoChargeResponse charge;

    @ApiModelProperty(value = "The insurance add-on for this equipment")
    private TigoAddonResponse addOn;

    @ApiModelProperty(value = "The equipment's type", example = "HANDSET", required = true)
    private EquipmentTypeEnum type;

    @ApiModelProperty(value = "Equipment's subsidy")
    private SubsidyResponse subsidy;

    @ApiModelProperty(value = "Subsidy Application status")
    private boolean subsidyApplied;

    @ApiModelProperty(value = "The equipment financing")
    private EquipmentFinancingResponse equipmentFinancing;
}
