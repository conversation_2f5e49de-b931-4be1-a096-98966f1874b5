package com.tigo.galaxion.sales.facade.connector.workflow_query;

import com.tigo.galaxion.sales.facade.connector.workflow_query.config.FeignWFQueryClientConfiguration;
import com.tigo.galaxion.sales.facade.connector.workflow_query.config.WorkflowQueryErrorDecoder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import com.tigo.galaxion.sales.facade.connector.workflow_query.domain.response.BasicOrderInformacion;

@FeignClient(value = "workflow-query-service", url = "${environment.url.workflow-query-service}", configuration = {WorkflowQueryErrorDecoder.class, FeignWFQueryClientConfiguration.class})
public interface WorkflowQueryClient {

    @GetMapping("/private/auth/diagnostics/{orderId}?wfq=true&activity=false&executions=false")
    BasicOrderInformacion getStatus(@PathVariable("orderId") String orderId);

    @PostMapping("/private/auth/orders/{orderId}/cancel?stop=true")
    void cancelOrder(@PathVariable("orderId") String orderId);
    
    
}
