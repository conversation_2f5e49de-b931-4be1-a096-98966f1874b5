
package com.tigo.galaxion.sales.facade.connector.catalog.domain.response;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "activationFees"
})
@Generated("jsonschema2pojo")
public class TariffPlansResponse {

    @JsonProperty("activationFees")
    private List<ActivationFee> activationFees;

    @JsonProperty("terminationEccCharge")
    private ActivationFee terminationEccCharge;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new LinkedHashMap<String, Object>();

    @JsonProperty("terminationEccCharge")
    public ActivationFee getTerminationEccCharge() {
        return terminationEccCharge;
    }

    @JsonProperty("terminationEccCharge")
    public void setTerminationEccCharge(ActivationFee terminationEccCharge) {
        this.terminationEccCharge = terminationEccCharge;
    }

    @JsonProperty("activationFees")
    public List<ActivationFee> getActivationFees() {
        return activationFees;
    }

    @JsonProperty("activationFees")
    public void setActivationFees(List<ActivationFee> activationFees) {
        this.activationFees = activationFees;
    }

    public TariffPlansResponse withActivationFees(List<ActivationFee> activationFees) {
        this.activationFees = activationFees;
        return this;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    public TariffPlansResponse withAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
        return this;
    }

}
