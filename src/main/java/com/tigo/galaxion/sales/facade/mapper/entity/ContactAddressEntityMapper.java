package com.tigo.galaxion.sales.facade.mapper.entity;

import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import com.tigo.galaxion.sales.facade.model.entity.ContactAddressEntity;
import com.tigo.galaxion.sales.facade.model.entity.ContactAddressId;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public final class ContactAddressEntityMapper {

    public static ContactAddressEntity buildContactAddressEntity(String prospectReference,
                                                                 AddressTypeEnum addressTypeEnum,
                                                                 Long addressId) {
        var contactAddressId = ContactAddressId.builder()
                                               .reference(prospectReference)
                                               .type(addressTypeEnum)
                                               .build();
        return ContactAddressEntity.builder()
                                   .contactAddressId(contactAddressId)
                                   .addressId(addressId)
                                   .build();
    }
}
