package com.tigo.galaxion.sales.facade.connector.alfresco.config;

import org.apache.http.HttpHost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import feign.Client;
import feign.httpclient.ApacheHttpClient;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FeignProxyConfiguration {

    @Value("${alfresco.proxy.use-proxy}")
    private Boolean useProxy;

    @Value("${alfresco.proxy.host}")
    private String proxyHost;

    @Value("${alfresco.proxy.port}")
    private Integer proxyPort;

    @Bean
    public Client feignClient() {
        log.info("Alfresco-useProxy: {}", useProxy);
        HttpClientBuilder builder = HttpClientBuilder.create();
        if (useProxy) {
            HttpHost proxy = new HttpHost(proxyHost, proxyPort);
            builder.setProxy(proxy);
        }
        CloseableHttpClient httpClient = builder.build();

        return new ApacheHttpClient(httpClient);
    }
}
