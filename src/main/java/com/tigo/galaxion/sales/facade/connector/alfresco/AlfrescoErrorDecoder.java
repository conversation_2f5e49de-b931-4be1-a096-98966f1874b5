package com.tigo.galaxion.sales.facade.connector.alfresco;

import java.io.IOException;
import java.io.InputStream;
import org.apache.commons.lang3.StringUtils;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;
import org.zalando.problem.ThrowableProblem;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.alfresco.problem.AlfrescoProblem;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoErrorResponse;

import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class AlfrescoErrorDecoder implements ErrorDecoder{
    
    private static final String TITLE = "alfresco-service-error";

    @Override
    public Exception decode(String methodKey, Response response) {
        throw new AlfrescoProblem(getThrowableProblem(response));
    }

    private ThrowableProblem getThrowableProblem(Response response) {
        if (StringUtils.isNotBlank(response.body().toString())) {
            log.error(response.body().toString());
        }

        AlfrescoErrorResponse message = null;
        try (InputStream bodyIs = response.body().asInputStream()) {
            ObjectMapper mapper = new ObjectMapper();
            message = mapper.readValue(bodyIs, AlfrescoErrorResponse.class);

            var problemBuilder = Problem
                    .builder()
                    .withDetail(message.getError())
                    .withStatus(Status.valueOf(response.status()))
                    .withTitle(TITLE);
            return problemBuilder.build();

        } catch (IOException e) {
            var problemBuilder = Problem
                    .builder()
                    .withDetail(e.getMessage())
                    .withStatus(Status.valueOf(response.status()))
                    .withTitle(TITLE);
            return problemBuilder.build();
        }
    }
}
