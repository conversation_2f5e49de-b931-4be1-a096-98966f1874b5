package com.tigo.galaxion.sales.facade.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.Builder.Default;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AddressMSResponse {

    @ApiModelProperty(value = "Department Name", required = true, example = "ANTIOQUIA")
    @JsonProperty("department")
    private String department;  
    
    @ApiModelProperty(value = "Municipality Name", required = true, example = "SABANETA")
    @JsonProperty("municipality")
    private String municipality;

    @ApiModelProperty(value = "Latitude", required = true, example = "6.157284259796143")
    @JsonProperty("Latitude")
    private String latitude;

    @ApiModelProperty(value = "Longitude", required = true, example = "-75.60497283935547")
    @JsonProperty("Longitude")
    private String longitude;
    
    @ApiModelProperty(value = "Stratum", required = true, example = "2")
    @JsonProperty("stratum")
    private String stratum;

    @ApiModelProperty(value = "Country Code", required = true, example = "57")
    @JsonProperty("CountryCode")
    private String countryCode;

    @ApiModelProperty(value = "Address", required = true, example = "KR 43 A # 53 D - 46 SUR IN 1609")
    @JsonProperty("address")    
    private String address;

    @ApiModelProperty(value = "Micro Zone", required = false, example = "CALDAS")
    @JsonProperty("microzone")
    private String microzone;

    @ApiModelProperty(value = "Address Code", required = true, example = "1431065734")
    @JsonProperty("addressCode")
    private String addressCode;

    @ApiModelProperty(value = "Country", required = true, example = "Colombia")
    @Default
    private String country = "Colombia";
}
