package com.tigo.galaxion.sales.facade.connector.prospectlead.domain;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Customer {
    @ApiModelProperty(value = "Document Type", required = true, example = "CC")
    @JsonProperty("documentType")
    private String documentType;

    @ApiModelProperty(value = "Document ID", required = true, example = "12321113")
    @JsonProperty("documentId")
    private String documentId;

    @ApiModelProperty(value = "Expedition Document Date", required = true, example = "2023-07-28T08:00:00")
    @JsonProperty("expeditionDate")
    private String expeditionDate;

    @ApiModelProperty(value = "Contact First Name ", required = true, example = "Juan")
    @JsonProperty("names")
    private String names;

    @ApiModelProperty(value = "Contact Last Name ", required = true, example = "perez")
    @JsonProperty("lastName")
    private String lastName;

    @ApiModelProperty(value = "Contact Email", required = false, example = "<EMAIL>")
    @JsonProperty("contactEmail")
    private String contactEmail;

    @ApiModelProperty(value = "Contact Phone", required = false, example = "57 ************")
    @JsonProperty("contactPhone")
    private String contactPhone;

    @ApiModelProperty(value = "Additional Phone1", required = false, example = "57 ************")
    @JsonProperty("additionalPhone1")
    private String additionalPhone1;

    @ApiModelProperty(value = "Additional Phone2", required = false, example = "57 ************")
    @JsonProperty("additionalPhone2")
    private String additionalPhone2;

    @ApiModelProperty(value = "Additional Email1", required = false, example = "<EMAIL>")
    @JsonProperty("additionalEmail1")
    private String additionalEmail1;

    @ApiModelProperty(value = "Additional Email2", required = false, example = "<EMAIL>")
    @JsonProperty("additionalEmail2")
    private String additionalEmail2;

    @ApiModelProperty(value = "Habeas Data", required = true, example = "true")
    @JsonProperty("habeasdata")
    private Integer habeasdata;

    @ApiModelProperty(value = "Invoice Delivery", required = false, example = "P")
    @JsonProperty("invoiceDelivery")
    private String invoiceDelivery;

    @ApiModelProperty(value = "Appointment Start Date", required = false, example = "2023-07-28T08:00:00")
    @JsonProperty("appointmentStartDate")
    private String appointmentStartDate;

    @ApiModelProperty(value = "Appointment End Date", required = false, example = "2023-07-28T08:00:00")
    @JsonProperty("appointmentEndDate")
    private String appointmentEndDate;

    @ApiModelProperty(value = "Rating", required = false, example = "0")
    @JsonProperty("rating")
    private int rating;

    @ApiModelProperty(value = "Contract Number", required = false, example = "null")
    @JsonProperty("contractNumber")
    private String contractNumber;

    @ApiModelProperty(value = "Call Id", required = false, example = "null")
    @JsonProperty("callId")
    private String callId;

    @ApiModelProperty(value = "Appointment Time Slot", required = false, example = "null")
    @JsonProperty("appointmentTimeSlot")
    private String appointmentTimeSlot;

    @ApiModelProperty(value = "Habeas sms", required = false, example = "null")
    @JsonProperty("habeasSms")
    private Boolean habeasSms;


    @ApiModelProperty(value = "Habeas Email", required = false, example = "null")
    @JsonProperty("habeasEmail")
    private Boolean habeasEmail;


    @ApiModelProperty(value = "Habeas Telemercado", required = false, example = "null")
    @JsonProperty("habeasTelemercado")
    private Boolean habeasTelemercado;

    @ApiModelProperty(value = "Habeas Authorization", required = false, example = "null")
    @JsonProperty("habeasDataAuthorization")
    private Boolean habeasDataAuthorization;

    //added
    @ApiModelProperty(value = " taxPayer  ", required = false, example = "null")
    @JsonProperty("taxPayer")
    private String taxPayer ;

    @ApiModelProperty(value = " taxVerificationDigit  ", required = false, example = "null")
    @JsonProperty("taxVerificationDigit")
    private String taxVerificationDigit;
    

    @ApiModelProperty(value = " contractDate  ", required = false, example = "null")
    @JsonProperty("contractDate")
    private String contractDate ; 

    @ApiModelProperty(value = "contractState", required = false, example = "null")
    @JsonProperty("contractState")
    private String contractState ;

    @ApiModelProperty(value = "contractSended", required = false, example = "null")
    @JsonProperty("contractSent")
    private boolean contractSent ;

    @ApiModelProperty(value = "contractSendedDate", required = false, example = "null")
    @JsonProperty("contractSentDate")
    private String contractSentDate; 

    @ApiModelProperty(value = "payment", required = false, example = "null")
    @JsonProperty("payment")
    private Float payment;

}
