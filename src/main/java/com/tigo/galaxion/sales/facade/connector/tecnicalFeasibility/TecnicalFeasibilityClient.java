package com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import com.tigo.galaxion.sales.facade.config.FeignRetryConfig;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.request.TecnicalFeasibilityRequestBody;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.response.TecnicalFeasibilityResponseBody;

import feign.Headers;
import io.swagger.v3.oas.annotations.parameters.RequestBody;

@FeignClient(value = "tigo-tecnical-feasibility",
    url = "${environment.url.service-coverage}",
    configuration = { FeignRetryConfig.class, TecnicalFeasibilityErrorDecoder.class }
)

public interface TecnicalFeasibilityClient {
    @PostMapping("/v1/technical-feasibility")
    @Headers("Content-Type: application/json")
    TecnicalFeasibilityResponseBody createTecnical(@RequestBody TecnicalFeasibilityRequestBody request);
}