package com.tigo.galaxion.sales.facade.domain.request;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ProspectusLogRequest {
  @NotNull
  @ApiModelProperty(value = "The reference data. It is a requirement for the postpaid sale reference identifier, length of 8 caracters alphabetic.", example = "5047EY04", required = true)
  private String reference;

  @NotNull
  @ApiModelProperty(value = "Customer credential type, length of 8 alphanumeric, length of 5 caracters alphabetic.", example = "CC, CE, NIT", required = true)
  private String customer_id_type;

  @NotNull
  @ApiModelProperty(value = "Number associated with the type of client credential, maximum length of 24 caracters alphabetic.", example = "9874563210", required = true)
  private String customer_id_number;

  @NotNull
  @ApiModelProperty(value = "User id entering postpaid management, maximum length of 100 caracters alphabetic.", example = "mamayer", required = true)
  private String user_id;

  @NotNull
  @ApiModelProperty(value = "User name entering postpaid management, maximum length of 255 caracters alphabetic.", example = "María Alejandra Meyer", required = true)
  private String user_name;

  @NotNull
  @ApiModelProperty(value = "IP of user who has entered postpaid management, maximum length of 25 caracters alphabetic.", example = "***********", required = true)
  private String ip_equipment;

  @NotNull
  @ApiModelProperty(value = "Sales channel, maximum length of 100 caracters alphabetic.", example = "TELESALES", required = true)
  private String sales_channel;

  @NotNull
  @ApiModelProperty(value = "Customer authentication type, maximum length of 100 caracters alphabetic.", example = "OTP, Questions, NA", required = true)
  private String authentication_type;

  @NotNull
  @ApiModelProperty(value = "Type of transaction I know you are making, maximum length of 150 caracters alphabetic.", example = "purchase, new subscription, acquisition", required = true)
  private String transaction;
}
