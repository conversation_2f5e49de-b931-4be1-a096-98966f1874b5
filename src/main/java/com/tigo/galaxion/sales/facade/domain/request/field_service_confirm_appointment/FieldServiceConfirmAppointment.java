package com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment;

import java.util.List;

import com.tigo.galaxion.sales.facade.soap.process_task_ex.MCAsset;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.MCService;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FieldServiceConfirmAppointment {
    private String callId;
    private String status;
    private Integer number;
    private Integer priority;
    private Integer duration;
    private String earlyStart;
    private String lateStart;
    private String dueDate;
    private String openDate;
    private String area;
    private String region;
    private String district;
    private String street;
    private String city;
    private String mcState;
    private String countryId;
    private Boolean tdRequired;
    private Integer requiredCrewSize;
    private Integer numberOfRequiredEngineers;
    private String taskTypeCategory;
    private String taskType;
    private String mcComment;
    private String mcCrmComment;
    private String mcContactEmail;
    private String mcCustomerCode;
    private Long mcCustomerPhoneNumber;
    private String mcStatusFCVToken;
    private String customer;
    private Long contactPhoneNumber;
    private String mcWorkPackageDescription;
    private String mcConnectionData;
    private String mcBillingAccountInfo;
    private String appointmentStart;
    private String appointmentFinish;
    private String latitude;
    private String longitude;
    private List<MCService> mcMvServices;
    private List<MCAsset> mcMvAssets;
    private String contactName;
    private String mcInfoCustomerSite;
    private String mcOpeningReason;
    private String contractType;
    private String mcCustomerClass;
    private Integer mcServicePaid;
    private String mcCustomerIdentityNumber;
    private String mcConfirmationStatus;
    private String mcCustomsSeal;
    private String mcRecurrentClient;
    private String mcTap;
    private String mcBoca;
    private String mcCRMCancellationReasonC;
}
