package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.payment_method.PaymentMethodResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentSettingsResponse {

    private String payerReference;

    @Builder.Default
    private List<PaymentMethodResponse> paymentMethods = new ArrayList<>();
}
