package com.tigo.galaxion.sales.facade.connector.contact.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ContactIdentityDocumentV2Response {

    private boolean mainIdentityDocument;
    private String identifier;
    private String type;
    private String nationality;
}
