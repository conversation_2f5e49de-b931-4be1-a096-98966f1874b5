package com.tigo.galaxion.sales.facade.domain.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSearchRequest {
    @NotNull
    @ApiModelProperty(value = "Customer credential type, length of 255 caracters alphabetic.", example = "Pasaporte", required = true)
    private String type;

    @NotNull
    @ApiModelProperty(value = "Number associated with the type of customer credential, maximum length of 25 caracters alphabetic.", example = "2177258210202", required = true)
    private String identifier;
}
