package com.tigo.galaxion.sales.facade.services.order_notification;

import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.app.dto.VariableUpdateDTO;
import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;
import org.springframework.beans.factory.annotation.Value;
import mc.monacotelecom.workflow.base.enumeration.ProcessType;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class EndOfVisitService {
    @Value("${wfe.signal.SP_WAIT_END_OF_VISIT}")
    private String signalName;

    private final WorkflowEngineClient workflowEngineClient;

    public String sendNotification(String callId, String serviceId, FieldServiceConfirmAppointment request)
            throws Exception {
        log.info("Create tecrep equipment: {}", callId);
        workflowEngineClient.updateConnectorData(callId,
                VariableUpdateDTO.builder()
                        .type(ProcessType.COM)
                        .data(request)
                        .variable("fieldservice")
                        .build());

        log.info("send signal from: {} to serviceId: {}; signalName: {}", callId, serviceId, signalName);

        var trackingNumber = UUID.randomUUID().toString();

        workflowEngineClient.processSignal(WorkflowSignalRequestDTO.builder()
                .name(signalName)
                .orderId(serviceId)
                .context(Map.of("callId", request.getCallId(),
                        "customer", request.getCustomer()))
                .build());

        return trackingNumber;
    }
}
