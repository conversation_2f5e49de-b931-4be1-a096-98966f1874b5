package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.contact;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AcquisitionPermissionResponse {

    private String permission;

    private String name;

    private boolean enabled;
}
