
package com.tigo.galaxion.sales.facade.soap.get_task;

import javax.xml.bind.annotation.*;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for CustomerAccount complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="CustomerAccount"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsDeleted" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="MasterRecordId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LastName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="FirstName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Salutation" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MiddleName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Suffix" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="RecordTypeId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BillingStreet" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BillingCity" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BillingState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BillingPostalCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BillingCountry" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BillingLatitude" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *         &lt;element name="BillingLongitude" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *         &lt;element name="BillingGeocodeAccuracy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BillingAddress" type="{http://crmsaleforce.resourcemanager.millicom.com/gettasksoap}BillingAdress"/&gt;
 *         &lt;element name="ShippingStreet" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ShippingCity" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ShippingState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ShippingPostalCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ShippingCountry" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ShippingLatitude" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *         &lt;element name="ShippingLongitude" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *         &lt;element name="ShippingGeocodeAccuracy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ShippingAddress" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Phone" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Website" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PhotoUrl" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Industry" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="NumberOfEmployees" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Description" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OwnerId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CreatedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="CreatedById" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LastModifiedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="LastModifiedById" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SystemModstamp" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="LastActivityDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="LastViewedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="LastReferencedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="IsPartner" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="IsCustomerPortal" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="PersonContactId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsPersonAccount" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="ChannelProgramName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ChannelProgramLevelName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonMailingStreet" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonMailingCity" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonMailingState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonMailingPostalCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonMailingCountry" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonMailingLatitude" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *         &lt;element name="PersonMailingLongitude" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *         &lt;element name="PersonMailingGeocodeAccuracy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonMailingAddress" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonMobilePhone" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="PersonEmail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonTitle" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonDepartment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonLastCURequestDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="PersonLastCUUpdateDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="PersonEmailBouncedReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PersonEmailBouncedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Jigsaw" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="JigsawCompanyId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="AccountSource" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SicDesc" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OperatingHoursId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCAreaOperativa__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCClientCode__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCustomerClassType__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCustomerType__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCExternalRefID__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCOtherPhones__c" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCPersonIdentNo__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCBusinessIdentNo__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCEmail__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCountry__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCContactType__pc" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCLocation__pc" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCUser__pc" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CustomerAccount", propOrder = {
    "id",
    "isDeleted",
    "masterRecordId",
    "name",
    "lastName",
    "firstName",
    "salutation",
    "middleName",
    "suffix",
    "type",
    "recordTypeId",
    "billingStreet",
    "billingCity",
    "billingState",
    "billingPostalCode",
    "billingCountry",
    "billingLatitude",
    "billingLongitude",
    "billingGeocodeAccuracy",
    "billingAddress",
    "shippingStreet",
    "shippingCity",
    "shippingState",
    "shippingPostalCode",
    "shippingCountry",
    "shippingLatitude",
    "shippingLongitude",
    "shippingGeocodeAccuracy",
    "shippingAddress",
    "phone",
    "website",
    "photoUrl",
    "industry",
    "numberOfEmployees",
    "description",
    "ownerId",
    "createdDate",
    "createdById",
    "lastModifiedDate",
    "lastModifiedById",
    "systemModstamp",
    "lastActivityDate",
    "lastViewedDate",
    "lastReferencedDate",
    "isPartner",
    "isCustomerPortal",
    "personContactId",
    "isPersonAccount",
    "channelProgramName",
    "channelProgramLevelName",
    "personMailingStreet",
    "personMailingCity",
    "personMailingState",
    "personMailingPostalCode",
    "personMailingCountry",
    "personMailingLatitude",
    "personMailingLongitude",
    "personMailingGeocodeAccuracy",
    "personMailingAddress",
    "personMobilePhone",
    "personEmail",
    "personTitle",
    "personDepartment",
    "personLastCURequestDate",
    "personLastCUUpdateDate",
    "personEmailBouncedReason",
    "personEmailBouncedDate",
    "jigsaw",
    "jigsawCompanyId",
    "accountSource",
    "sicDesc",
    "operatingHoursId",
    "mcAreaOperativaC",
    "mcClientCodeC",
    "mcCustomerClassTypeC",
    "mcCustomerTypeC",
    "mcExternalRefIDC",
    "mcOtherPhonesC",
    "mcPersonIdentNoC",
    "mcBusinessIdentNoC",
    "mcEmailC",
    "mcCountryC",
    "mcContactTypePc",
    "mcLocationPc",
    "mcUserPc"
})
public class CustomerAccount {

    @XmlElement(name = "Id", required = true)
    protected String id;
    @XmlElement(name = "IsDeleted")
    protected boolean isDeleted;
    @XmlElement(name = "MasterRecordId", required = true)
    protected String masterRecordId;
    @XmlElement(name = "Name", required = true)
    protected String name;
    @XmlElement(name = "LastName", required = true)
    protected String lastName;
    @XmlElement(name = "FirstName", required = true)
    protected String firstName;
    @XmlElement(name = "Salutation", required = true)
    protected String salutation;
    @XmlElement(name = "MiddleName", required = true)
    protected String middleName;
    @XmlElement(name = "Suffix", required = true)
    protected String suffix;
    @XmlElement(name = "Type", required = true)
    protected String type;
    @XmlElement(name = "RecordTypeId", required = true)
    protected String recordTypeId;
    @XmlElement(name = "BillingStreet", required = true)
    protected String billingStreet;
    @XmlElement(name = "BillingCity", required = true)
    protected String billingCity;
    @XmlElement(name = "BillingState", required = true)
    protected String billingState;
    @XmlElement(name = "BillingPostalCode", required = true)
    protected String billingPostalCode;
    @XmlElement(name = "BillingCountry", required = true)
    protected String billingCountry;
    @XmlElement(name = "BillingLatitude")
    protected double billingLatitude;
    @XmlElement(name = "BillingLongitude")
    protected double billingLongitude;
    @XmlElement(name = "BillingGeocodeAccuracy", required = true)
    protected String billingGeocodeAccuracy;
    @XmlElement(name = "BillingAddress", required = true)
    protected BillingAdress billingAddress;
    @XmlElement(name = "ShippingStreet", required = true)
    protected String shippingStreet;
    @XmlElement(name = "ShippingCity", required = true)
    protected String shippingCity;
    @XmlElement(name = "ShippingState", required = true)
    protected String shippingState;
    @XmlElement(name = "ShippingPostalCode", required = true)
    protected String shippingPostalCode;
    @XmlElement(name = "ShippingCountry", required = true)
    protected String shippingCountry;
    @XmlElement(name = "ShippingLatitude")
    protected double shippingLatitude;
    @XmlElement(name = "ShippingLongitude")
    protected double shippingLongitude;
    @XmlElement(name = "ShippingGeocodeAccuracy", required = true)
    protected String shippingGeocodeAccuracy;
    @XmlElement(name = "ShippingAddress", required = true)
    protected String shippingAddress;
    @XmlElement(name = "Phone")
    protected int phone;
    @XmlElement(name = "Website", required = true)
    protected String website;
    @XmlElement(name = "PhotoUrl", required = true)
    protected String photoUrl;
    @XmlElement(name = "Industry", required = true)
    protected String industry;
    @XmlElement(name = "NumberOfEmployees")
    protected int numberOfEmployees;
    @XmlElement(name = "Description", required = true)
    protected String description;
    @XmlElement(name = "OwnerId", required = true)
    protected String ownerId;
    @XmlElement(name = "CreatedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar createdDate;
    @XmlElement(name = "CreatedById", required = true)
    protected String createdById;
    @XmlElement(name = "LastModifiedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastModifiedDate;
    @XmlElement(name = "LastModifiedById", required = true)
    protected String lastModifiedById;
    @XmlElement(name = "SystemModstamp", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar systemModstamp;
    @XmlElement(name = "LastActivityDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastActivityDate;
    @XmlElement(name = "LastViewedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastViewedDate;
    @XmlElement(name = "LastReferencedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastReferencedDate;
    @XmlElement(name = "IsPartner")
    protected boolean isPartner;
    @XmlElement(name = "IsCustomerPortal")
    protected boolean isCustomerPortal;
    @XmlElement(name = "PersonContactId", required = true)
    protected String personContactId;
    @XmlElement(name = "IsPersonAccount")
    protected boolean isPersonAccount;
    @XmlElement(name = "ChannelProgramName", required = true)
    protected String channelProgramName;
    @XmlElement(name = "ChannelProgramLevelName", required = true)
    protected String channelProgramLevelName;
    @XmlElement(name = "PersonMailingStreet", required = true)
    protected String personMailingStreet;
    @XmlElement(name = "PersonMailingCity", required = true)
    protected String personMailingCity;
    @XmlElement(name = "PersonMailingState", required = true)
    protected String personMailingState;
    @XmlElement(name = "PersonMailingPostalCode", required = true)
    protected String personMailingPostalCode;
    @XmlElement(name = "PersonMailingCountry", required = true)
    protected String personMailingCountry;
    @XmlElement(name = "PersonMailingLatitude")
    protected double personMailingLatitude;
    @XmlElement(name = "PersonMailingLongitude")
    protected double personMailingLongitude;
    @XmlElement(name = "PersonMailingGeocodeAccuracy", required = true)
    protected String personMailingGeocodeAccuracy;
    @XmlElement(name = "PersonMailingAddress", required = true)
    protected String personMailingAddress;
    @XmlElement(name = "PersonMobilePhone")
    protected int personMobilePhone;
    @XmlElement(name = "PersonEmail", required = true)
    protected String personEmail;
    @XmlElement(name = "PersonTitle", required = true)
    protected String personTitle;
    @XmlElement(name = "PersonDepartment", required = true)
    protected String personDepartment;
    @XmlElement(name = "PersonLastCURequestDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar personLastCURequestDate;
    @XmlElement(name = "PersonLastCUUpdateDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar personLastCUUpdateDate;
    @XmlElement(name = "PersonEmailBouncedReason", required = true)
    protected String personEmailBouncedReason;
    @XmlElement(name = "PersonEmailBouncedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar personEmailBouncedDate;
    @XmlElement(name = "Jigsaw", required = true)
    protected String jigsaw;
    @XmlElement(name = "JigsawCompanyId", required = true)
    protected String jigsawCompanyId;
    @XmlElement(name = "AccountSource", required = true)
    protected String accountSource;
    @XmlElement(name = "SicDesc", required = true)
    protected String sicDesc;
    @XmlElement(name = "OperatingHoursId", required = true)
    protected String operatingHoursId;
    @XmlElement(name = "MCAreaOperativa__c", required = true)
    protected String mcAreaOperativaC;
    @XmlElement(name = "MCClientCode__c", required = true)
    protected String mcClientCodeC;
    @XmlElement(name = "MCCustomerClassType__c", required = true)
    protected String mcCustomerClassTypeC;
    @XmlElement(name = "MCCustomerType__c", required = true)
    protected String mcCustomerTypeC;
    @XmlElement(name = "MCExternalRefID__c", required = true)
    protected String mcExternalRefIDC;
    @XmlElement(name = "MCOtherPhones__c")
    protected int mcOtherPhonesC;
    @XmlElement(name = "MCPersonIdentNo__c", required = true)
    protected String mcPersonIdentNoC;
    @XmlElement(name = "MCBusinessIdentNo__c", required = true)
    protected String mcBusinessIdentNoC;
    @XmlElement(name = "MCEmail__c", required = true)
    protected String mcEmailC;
    @XmlElement(name = "MCCountry__c", required = true)
    protected String mcCountryC;
    @XmlElement(name = "MCContactType__pc", required = true)
    protected String mcContactTypePc;
    @XmlElement(name = "MCLocation__pc", required = true)
    protected String mcLocationPc;
    @XmlElement(name = "MCUser__pc", required = true)
    protected String mcUserPc;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setId(String value) {
        this.id = value;
    }

    /**
     * Gets the value of the isDeleted property.
     * 
     */
    public boolean isIsDeleted() {
        return isDeleted;
    }

    /**
     * Sets the value of the isDeleted property.
     * 
     */
    public void setIsDeleted(boolean value) {
        this.isDeleted = value;
    }

    /**
     * Gets the value of the masterRecordId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMasterRecordId() {
        return masterRecordId;
    }

    /**
     * Sets the value of the masterRecordId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMasterRecordId(String value) {
        this.masterRecordId = value;
    }

    /**
     * Gets the value of the name property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the lastName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * Sets the value of the lastName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastName(String value) {
        this.lastName = value;
    }

    /**
     * Gets the value of the firstName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * Sets the value of the firstName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFirstName(String value) {
        this.firstName = value;
    }

    /**
     * Gets the value of the salutation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSalutation() {
        return salutation;
    }

    /**
     * Sets the value of the salutation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSalutation(String value) {
        this.salutation = value;
    }

    /**
     * Gets the value of the middleName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMiddleName() {
        return middleName;
    }

    /**
     * Sets the value of the middleName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMiddleName(String value) {
        this.middleName = value;
    }

    /**
     * Gets the value of the suffix property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSuffix() {
        return suffix;
    }

    /**
     * Sets the value of the suffix property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSuffix(String value) {
        this.suffix = value;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the recordTypeId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRecordTypeId() {
        return recordTypeId;
    }

    /**
     * Sets the value of the recordTypeId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRecordTypeId(String value) {
        this.recordTypeId = value;
    }

    /**
     * Gets the value of the billingStreet property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillingStreet() {
        return billingStreet;
    }

    /**
     * Sets the value of the billingStreet property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillingStreet(String value) {
        this.billingStreet = value;
    }

    /**
     * Gets the value of the billingCity property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillingCity() {
        return billingCity;
    }

    /**
     * Sets the value of the billingCity property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillingCity(String value) {
        this.billingCity = value;
    }

    /**
     * Gets the value of the billingState property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillingState() {
        return billingState;
    }

    /**
     * Sets the value of the billingState property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillingState(String value) {
        this.billingState = value;
    }

    /**
     * Gets the value of the billingPostalCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillingPostalCode() {
        return billingPostalCode;
    }

    /**
     * Sets the value of the billingPostalCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillingPostalCode(String value) {
        this.billingPostalCode = value;
    }

    /**
     * Gets the value of the billingCountry property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillingCountry() {
        return billingCountry;
    }

    /**
     * Sets the value of the billingCountry property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillingCountry(String value) {
        this.billingCountry = value;
    }

    /**
     * Gets the value of the billingLatitude property.
     * 
     */
    public double getBillingLatitude() {
        return billingLatitude;
    }

    /**
     * Sets the value of the billingLatitude property.
     * 
     */
    public void setBillingLatitude(double value) {
        this.billingLatitude = value;
    }

    /**
     * Gets the value of the billingLongitude property.
     * 
     */
    public double getBillingLongitude() {
        return billingLongitude;
    }

    /**
     * Sets the value of the billingLongitude property.
     * 
     */
    public void setBillingLongitude(double value) {
        this.billingLongitude = value;
    }

    /**
     * Gets the value of the billingGeocodeAccuracy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillingGeocodeAccuracy() {
        return billingGeocodeAccuracy;
    }

    /**
     * Sets the value of the billingGeocodeAccuracy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillingGeocodeAccuracy(String value) {
        this.billingGeocodeAccuracy = value;
    }

    /**
     * Gets the value of the billingAddress property.
     * 
     * @return
     *     possible object is
     *     {@link BillingAdress }
     *     
     */
    public BillingAdress getBillingAddress() {
        return billingAddress;
    }

    /**
     * Sets the value of the billingAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link BillingAdress }
     *     
     */
    public void setBillingAddress(BillingAdress value) {
        this.billingAddress = value;
    }

    /**
     * Gets the value of the shippingStreet property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingStreet() {
        return shippingStreet;
    }

    /**
     * Sets the value of the shippingStreet property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingStreet(String value) {
        this.shippingStreet = value;
    }

    /**
     * Gets the value of the shippingCity property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingCity() {
        return shippingCity;
    }

    /**
     * Sets the value of the shippingCity property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingCity(String value) {
        this.shippingCity = value;
    }

    /**
     * Gets the value of the shippingState property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingState() {
        return shippingState;
    }

    /**
     * Sets the value of the shippingState property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingState(String value) {
        this.shippingState = value;
    }

    /**
     * Gets the value of the shippingPostalCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingPostalCode() {
        return shippingPostalCode;
    }

    /**
     * Sets the value of the shippingPostalCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingPostalCode(String value) {
        this.shippingPostalCode = value;
    }

    /**
     * Gets the value of the shippingCountry property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingCountry() {
        return shippingCountry;
    }

    /**
     * Sets the value of the shippingCountry property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingCountry(String value) {
        this.shippingCountry = value;
    }

    /**
     * Gets the value of the shippingLatitude property.
     * 
     */
    public double getShippingLatitude() {
        return shippingLatitude;
    }

    /**
     * Sets the value of the shippingLatitude property.
     * 
     */
    public void setShippingLatitude(double value) {
        this.shippingLatitude = value;
    }

    /**
     * Gets the value of the shippingLongitude property.
     * 
     */
    public double getShippingLongitude() {
        return shippingLongitude;
    }

    /**
     * Sets the value of the shippingLongitude property.
     * 
     */
    public void setShippingLongitude(double value) {
        this.shippingLongitude = value;
    }

    /**
     * Gets the value of the shippingGeocodeAccuracy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingGeocodeAccuracy() {
        return shippingGeocodeAccuracy;
    }

    /**
     * Sets the value of the shippingGeocodeAccuracy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingGeocodeAccuracy(String value) {
        this.shippingGeocodeAccuracy = value;
    }

    /**
     * Gets the value of the shippingAddress property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingAddress() {
        return shippingAddress;
    }

    /**
     * Sets the value of the shippingAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingAddress(String value) {
        this.shippingAddress = value;
    }

    /**
     * Gets the value of the phone property.
     * 
     */
    public int getPhone() {
        return phone;
    }

    /**
     * Sets the value of the phone property.
     * 
     */
    public void setPhone(int value) {
        this.phone = value;
    }

    /**
     * Gets the value of the website property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWebsite() {
        return website;
    }

    /**
     * Sets the value of the website property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWebsite(String value) {
        this.website = value;
    }

    /**
     * Gets the value of the photoUrl property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPhotoUrl() {
        return photoUrl;
    }

    /**
     * Sets the value of the photoUrl property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPhotoUrl(String value) {
        this.photoUrl = value;
    }

    /**
     * Gets the value of the industry property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndustry() {
        return industry;
    }

    /**
     * Sets the value of the industry property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndustry(String value) {
        this.industry = value;
    }

    /**
     * Gets the value of the numberOfEmployees property.
     * 
     */
    public int getNumberOfEmployees() {
        return numberOfEmployees;
    }

    /**
     * Sets the value of the numberOfEmployees property.
     * 
     */
    public void setNumberOfEmployees(int value) {
        this.numberOfEmployees = value;
    }

    /**
     * Gets the value of the description property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the ownerId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOwnerId() {
        return ownerId;
    }

    /**
     * Sets the value of the ownerId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOwnerId(String value) {
        this.ownerId = value;
    }

    /**
     * Gets the value of the createdDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getCreatedDate() {
        return createdDate;
    }

    /**
     * Sets the value of the createdDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setCreatedDate(XMLGregorianCalendar value) {
        this.createdDate = value;
    }

    /**
     * Gets the value of the createdById property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCreatedById() {
        return createdById;
    }

    /**
     * Sets the value of the createdById property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreatedById(String value) {
        this.createdById = value;
    }

    /**
     * Gets the value of the lastModifiedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastModifiedDate() {
        return lastModifiedDate;
    }

    /**
     * Sets the value of the lastModifiedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastModifiedDate(XMLGregorianCalendar value) {
        this.lastModifiedDate = value;
    }

    /**
     * Gets the value of the lastModifiedById property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLastModifiedById() {
        return lastModifiedById;
    }

    /**
     * Sets the value of the lastModifiedById property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastModifiedById(String value) {
        this.lastModifiedById = value;
    }

    /**
     * Gets the value of the systemModstamp property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getSystemModstamp() {
        return systemModstamp;
    }

    /**
     * Sets the value of the systemModstamp property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setSystemModstamp(XMLGregorianCalendar value) {
        this.systemModstamp = value;
    }

    /**
     * Gets the value of the lastActivityDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastActivityDate() {
        return lastActivityDate;
    }

    /**
     * Sets the value of the lastActivityDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastActivityDate(XMLGregorianCalendar value) {
        this.lastActivityDate = value;
    }

    /**
     * Gets the value of the lastViewedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastViewedDate() {
        return lastViewedDate;
    }

    /**
     * Sets the value of the lastViewedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastViewedDate(XMLGregorianCalendar value) {
        this.lastViewedDate = value;
    }

    /**
     * Gets the value of the lastReferencedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastReferencedDate() {
        return lastReferencedDate;
    }

    /**
     * Sets the value of the lastReferencedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastReferencedDate(XMLGregorianCalendar value) {
        this.lastReferencedDate = value;
    }

    /**
     * Gets the value of the isPartner property.
     * 
     */
    public boolean isIsPartner() {
        return isPartner;
    }

    /**
     * Sets the value of the isPartner property.
     * 
     */
    public void setIsPartner(boolean value) {
        this.isPartner = value;
    }

    /**
     * Gets the value of the isCustomerPortal property.
     * 
     */
    public boolean isIsCustomerPortal() {
        return isCustomerPortal;
    }

    /**
     * Sets the value of the isCustomerPortal property.
     * 
     */
    public void setIsCustomerPortal(boolean value) {
        this.isCustomerPortal = value;
    }

    /**
     * Gets the value of the personContactId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonContactId() {
        return personContactId;
    }

    /**
     * Sets the value of the personContactId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonContactId(String value) {
        this.personContactId = value;
    }

    /**
     * Gets the value of the isPersonAccount property.
     * 
     */
    public boolean isIsPersonAccount() {
        return isPersonAccount;
    }

    /**
     * Sets the value of the isPersonAccount property.
     * 
     */
    public void setIsPersonAccount(boolean value) {
        this.isPersonAccount = value;
    }

    /**
     * Gets the value of the channelProgramName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChannelProgramName() {
        return channelProgramName;
    }

    /**
     * Sets the value of the channelProgramName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChannelProgramName(String value) {
        this.channelProgramName = value;
    }

    /**
     * Gets the value of the channelProgramLevelName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChannelProgramLevelName() {
        return channelProgramLevelName;
    }

    /**
     * Sets the value of the channelProgramLevelName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChannelProgramLevelName(String value) {
        this.channelProgramLevelName = value;
    }

    /**
     * Gets the value of the personMailingStreet property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonMailingStreet() {
        return personMailingStreet;
    }

    /**
     * Sets the value of the personMailingStreet property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonMailingStreet(String value) {
        this.personMailingStreet = value;
    }

    /**
     * Gets the value of the personMailingCity property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonMailingCity() {
        return personMailingCity;
    }

    /**
     * Sets the value of the personMailingCity property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonMailingCity(String value) {
        this.personMailingCity = value;
    }

    /**
     * Gets the value of the personMailingState property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonMailingState() {
        return personMailingState;
    }

    /**
     * Sets the value of the personMailingState property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonMailingState(String value) {
        this.personMailingState = value;
    }

    /**
     * Gets the value of the personMailingPostalCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonMailingPostalCode() {
        return personMailingPostalCode;
    }

    /**
     * Sets the value of the personMailingPostalCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonMailingPostalCode(String value) {
        this.personMailingPostalCode = value;
    }

    /**
     * Gets the value of the personMailingCountry property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonMailingCountry() {
        return personMailingCountry;
    }

    /**
     * Sets the value of the personMailingCountry property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonMailingCountry(String value) {
        this.personMailingCountry = value;
    }

    /**
     * Gets the value of the personMailingLatitude property.
     * 
     */
    public double getPersonMailingLatitude() {
        return personMailingLatitude;
    }

    /**
     * Sets the value of the personMailingLatitude property.
     * 
     */
    public void setPersonMailingLatitude(double value) {
        this.personMailingLatitude = value;
    }

    /**
     * Gets the value of the personMailingLongitude property.
     * 
     */
    public double getPersonMailingLongitude() {
        return personMailingLongitude;
    }

    /**
     * Sets the value of the personMailingLongitude property.
     * 
     */
    public void setPersonMailingLongitude(double value) {
        this.personMailingLongitude = value;
    }

    /**
     * Gets the value of the personMailingGeocodeAccuracy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonMailingGeocodeAccuracy() {
        return personMailingGeocodeAccuracy;
    }

    /**
     * Sets the value of the personMailingGeocodeAccuracy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonMailingGeocodeAccuracy(String value) {
        this.personMailingGeocodeAccuracy = value;
    }

    /**
     * Gets the value of the personMailingAddress property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonMailingAddress() {
        return personMailingAddress;
    }

    /**
     * Sets the value of the personMailingAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonMailingAddress(String value) {
        this.personMailingAddress = value;
    }

    /**
     * Gets the value of the personMobilePhone property.
     * 
     */
    public int getPersonMobilePhone() {
        return personMobilePhone;
    }

    /**
     * Sets the value of the personMobilePhone property.
     * 
     */
    public void setPersonMobilePhone(int value) {
        this.personMobilePhone = value;
    }

    /**
     * Gets the value of the personEmail property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonEmail() {
        return personEmail;
    }

    /**
     * Sets the value of the personEmail property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonEmail(String value) {
        this.personEmail = value;
    }

    /**
     * Gets the value of the personTitle property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonTitle() {
        return personTitle;
    }

    /**
     * Sets the value of the personTitle property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonTitle(String value) {
        this.personTitle = value;
    }

    /**
     * Gets the value of the personDepartment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonDepartment() {
        return personDepartment;
    }

    /**
     * Sets the value of the personDepartment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonDepartment(String value) {
        this.personDepartment = value;
    }

    /**
     * Gets the value of the personLastCURequestDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getPersonLastCURequestDate() {
        return personLastCURequestDate;
    }

    /**
     * Sets the value of the personLastCURequestDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setPersonLastCURequestDate(XMLGregorianCalendar value) {
        this.personLastCURequestDate = value;
    }

    /**
     * Gets the value of the personLastCUUpdateDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getPersonLastCUUpdateDate() {
        return personLastCUUpdateDate;
    }

    /**
     * Sets the value of the personLastCUUpdateDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setPersonLastCUUpdateDate(XMLGregorianCalendar value) {
        this.personLastCUUpdateDate = value;
    }

    /**
     * Gets the value of the personEmailBouncedReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonEmailBouncedReason() {
        return personEmailBouncedReason;
    }

    /**
     * Sets the value of the personEmailBouncedReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonEmailBouncedReason(String value) {
        this.personEmailBouncedReason = value;
    }

    /**
     * Gets the value of the personEmailBouncedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getPersonEmailBouncedDate() {
        return personEmailBouncedDate;
    }

    /**
     * Sets the value of the personEmailBouncedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setPersonEmailBouncedDate(XMLGregorianCalendar value) {
        this.personEmailBouncedDate = value;
    }

    /**
     * Gets the value of the jigsaw property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJigsaw() {
        return jigsaw;
    }

    /**
     * Sets the value of the jigsaw property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJigsaw(String value) {
        this.jigsaw = value;
    }

    /**
     * Gets the value of the jigsawCompanyId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJigsawCompanyId() {
        return jigsawCompanyId;
    }

    /**
     * Sets the value of the jigsawCompanyId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJigsawCompanyId(String value) {
        this.jigsawCompanyId = value;
    }

    /**
     * Gets the value of the accountSource property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAccountSource() {
        return accountSource;
    }

    /**
     * Sets the value of the accountSource property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAccountSource(String value) {
        this.accountSource = value;
    }

    /**
     * Gets the value of the sicDesc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSicDesc() {
        return sicDesc;
    }

    /**
     * Sets the value of the sicDesc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSicDesc(String value) {
        this.sicDesc = value;
    }

    /**
     * Gets the value of the operatingHoursId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOperatingHoursId() {
        return operatingHoursId;
    }

    /**
     * Sets the value of the operatingHoursId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOperatingHoursId(String value) {
        this.operatingHoursId = value;
    }

    /**
     * Gets the value of the mcAreaOperativaC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCAreaOperativaC() {
        return mcAreaOperativaC;
    }

    /**
     * Sets the value of the mcAreaOperativaC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCAreaOperativaC(String value) {
        this.mcAreaOperativaC = value;
    }

    /**
     * Gets the value of the mcClientCodeC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCClientCodeC() {
        return mcClientCodeC;
    }

    /**
     * Sets the value of the mcClientCodeC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCClientCodeC(String value) {
        this.mcClientCodeC = value;
    }

    /**
     * Gets the value of the mcCustomerClassTypeC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomerClassTypeC() {
        return mcCustomerClassTypeC;
    }

    /**
     * Sets the value of the mcCustomerClassTypeC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomerClassTypeC(String value) {
        this.mcCustomerClassTypeC = value;
    }

    /**
     * Gets the value of the mcCustomerTypeC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomerTypeC() {
        return mcCustomerTypeC;
    }

    /**
     * Sets the value of the mcCustomerTypeC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomerTypeC(String value) {
        this.mcCustomerTypeC = value;
    }

    /**
     * Gets the value of the mcExternalRefIDC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCExternalRefIDC() {
        return mcExternalRefIDC;
    }

    /**
     * Sets the value of the mcExternalRefIDC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCExternalRefIDC(String value) {
        this.mcExternalRefIDC = value;
    }

    /**
     * Gets the value of the mcOtherPhonesC property.
     * 
     */
    public int getMCOtherPhonesC() {
        return mcOtherPhonesC;
    }

    /**
     * Sets the value of the mcOtherPhonesC property.
     * 
     */
    public void setMCOtherPhonesC(int value) {
        this.mcOtherPhonesC = value;
    }

    /**
     * Gets the value of the mcPersonIdentNoC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCPersonIdentNoC() {
        return mcPersonIdentNoC;
    }

    /**
     * Sets the value of the mcPersonIdentNoC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCPersonIdentNoC(String value) {
        this.mcPersonIdentNoC = value;
    }

    /**
     * Gets the value of the mcBusinessIdentNoC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCBusinessIdentNoC() {
        return mcBusinessIdentNoC;
    }

    /**
     * Sets the value of the mcBusinessIdentNoC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCBusinessIdentNoC(String value) {
        this.mcBusinessIdentNoC = value;
    }

    /**
     * Gets the value of the mcEmailC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCEmailC() {
        return mcEmailC;
    }

    /**
     * Sets the value of the mcEmailC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCEmailC(String value) {
        this.mcEmailC = value;
    }

    /**
     * Gets the value of the mcCountryC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCountryC() {
        return mcCountryC;
    }

    /**
     * Sets the value of the mcCountryC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCountryC(String value) {
        this.mcCountryC = value;
    }

    /**
     * Gets the value of the mcContactTypePc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCContactTypePc() {
        return mcContactTypePc;
    }

    /**
     * Sets the value of the mcContactTypePc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCContactTypePc(String value) {
        this.mcContactTypePc = value;
    }

    /**
     * Gets the value of the mcLocationPc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCLocationPc() {
        return mcLocationPc;
    }

    /**
     * Sets the value of the mcLocationPc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCLocationPc(String value) {
        this.mcLocationPc = value;
    }

    /**
     * Gets the value of the mcUserPc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCUserPc() {
        return mcUserPc;
    }

    /**
     * Sets the value of the mcUserPc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCUserPc(String value) {
        this.mcUserPc = value;
    }

}
