package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.problem;

import com.tigo.galaxion.sales.facade.connector.config.BaseProblem;
import org.zalando.problem.ThrowableProblem;

public class AcquisitionProspectContactNotFoundProblem extends BaseProblem {

    public AcquisitionProspectContactNotFoundProblem(ThrowableProblem throwableProblem) {
        super(throwableProblem);
    }

}
