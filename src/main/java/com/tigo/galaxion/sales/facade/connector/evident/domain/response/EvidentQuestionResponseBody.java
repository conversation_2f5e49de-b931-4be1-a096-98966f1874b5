package com.tigo.galaxion.sales.facade.connector.evident.domain.response;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.Question;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentQuestionResponseBody {

    @JsonProperty("question")
    List<Question> question;

    @JsonProperty("alerts")
    private String alerts;

    @JsonProperty("codeAlert")
    private String codeAlert;

    @JsonProperty("excludeCustomer")
    private String excludeCustomer;

    @JsonProperty("id")
    private String id;

    @JsonProperty("record")
    private String record;

    @JsonProperty("responseAlert")
    private String responseAlert;

    @JsonProperty("result")
    private String result;

    @JsonProperty("resultDescription")
    private String resultDescription;

}
