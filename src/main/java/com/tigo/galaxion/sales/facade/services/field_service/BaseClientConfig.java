package com.tigo.galaxion.sales.facade.services.field_service;

import org.apache.http.HttpHeaders;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

@Configuration
public abstract class BaseClientConfig {

    @Value("${environment.field-service.use-proxy}")
    private Boolean useProxy;

    @Value("${environment.field-service.proxy.host}")
    private String proxyHost;

    @Value("${environment.field-service.proxy.port}")
    private Integer proxyPort;

    protected WebServiceTemplate createWebServiceTemplate(String url, Jaxb2Marshaller marshaller) {
        try {
            HttpClientBuilder builder = HttpClientBuilder.create();
            builder.addInterceptorFirst(new HttpComponentsMessageSender.RemoveSoapHeadersInterceptor());
            if (useProxy) {
                HttpHost proxy = new HttpHost(proxyHost, proxyPort);
                builder.setProxy(proxy);
            }

            CloseableHttpClient httpClient = builder.build();

            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender(httpClient);

            WebServiceTemplate webServiceTemplate = new WebServiceTemplate();
            webServiceTemplate.setMessageSender(messageSender);
            webServiceTemplate.setDefaultUri(url);
            webServiceTemplate.setUnmarshaller(marshaller);
            webServiceTemplate.setMarshaller(marshaller);

            return webServiceTemplate;
        } catch (Exception e) {
            throw new RuntimeException("Error creating template", e);
        }
    }
}