package com.tigo.galaxion.sales.facade.connector.riskassessment.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Score {
    private Integer reasonCodes;
    private Integer type;
    private Integer rating;
    private String classification;
}
