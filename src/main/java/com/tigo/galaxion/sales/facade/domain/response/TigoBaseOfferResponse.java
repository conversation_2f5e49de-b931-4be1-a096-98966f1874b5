package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoBaseOfferResponse {

    @ApiModelProperty(value = "The code of the offer in the catalog", example = "GOMO", required = true)
    private String catalogCode;

    @ApiModelProperty(value = "The offer service group", required = true)
    private String serviceGroup;

    @ApiModelProperty(value = "The associated service domains", example = "[\"MOBILE\"]")
    private List<String> serviceDomains;

    @ApiModelProperty(value = "The description of the offer", example = "GoMo", required = true)
    private String description;

    @ApiModelProperty(value = "The comment of the offer", example = "Go more 2")
    private String comment;

    @ApiModelProperty(value = "The code of the tariff plan to add in the cart", required = true, example = "BASIC-24")
    private String catalogTariffPlanCode;

    @ApiModelProperty(value = "The discounts associated with the tariff plan")
    private List<TigoTariffPlanDiscountResponse> tariffPlanDiscounts = new ArrayList<>();

    @ApiModelProperty(value = "If the tariff plan is sim only or not", example = "BASIC-24")
    private Boolean simOnly;

    @ApiModelProperty(value = "The description of the tariff plan", example = "Basic 24 months", required = true)
    private String tariffPlanDescription;

    @ApiModelProperty(value = "Commitment duration", example = "ONE", required = true)
    private String commitmentDuration;

    @ApiModelProperty(value = "The amount to be paid vat included", required = true)
    private TigoAmountResponse amountVatIncluded;

    @ApiModelProperty(value = "The amount to be paid vat excluded", required = true)
    private TigoAmountResponse amountVatExcluded;

    @ApiModelProperty(value = "The offer display order", example = "1", required = true)
    private Long displayOrder;

    @Builder.Default
    @ApiModelProperty(value = "The list of usages of the offer", required = true)
    private List<TigoUsageResponse> usages = new ArrayList<>();

    @Builder.Default
    @ApiModelProperty(value = "The list of charges of the offer", required = true)
    private List<TigoChargeResponse> charges = new ArrayList<>();

    @ApiModelProperty(value = "The broadband technology")
    private String broadbandTechnology;

    @ApiModelProperty(value = "Recurring amounts by occurrence")
    @Builder.Default
    private List<TigoRecurringAmountByOccurrenceResponse> recurringAmountsByOccurrence = new ArrayList<>();
}
