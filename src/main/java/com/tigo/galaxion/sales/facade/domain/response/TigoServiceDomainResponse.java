package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
@ToString
public class TigoServiceDomainResponse {

    @ApiModelProperty(value = "The service domain code", required = true, example = "MOBILE")
    private String code;

    @ApiModelProperty(value = "The service domain name", required = true, example = "Mobile")
    private String name;

    @ApiModelProperty(value = "The service domain display order in the invoice", required = true, example = "1")
    private Long displayOrder;

}
