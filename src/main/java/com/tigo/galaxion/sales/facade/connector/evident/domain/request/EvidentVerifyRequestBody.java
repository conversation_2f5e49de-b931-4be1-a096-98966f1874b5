package com.tigo.galaxion.sales.facade.connector.evident.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.Identification;
import com.tigo.galaxion.sales.facade.connector.evident.domain.QuestionnaireData;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentVerifyRequestBody {
    
    @JsonProperty("identification")
    private Identification identificacion;

    @JsonProperty("questionnaireData")
    private QuestionnaireData questionnaireData; 

    @JsonProperty("codeOTP")
    private String codeOTP;

    @JsonProperty("transactionIDOTP")
    private String transactionIDOTP;

}
