package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
public class DiscountResponse {

    private Long id;

    private String catalogCode;

    private String description;

    private Long value;

    private Long vatExcludedAmount;

    private String type;

    private String frequency;

    private Integer occurrence;

    private String billingType;

    private String reason;

    private String discountItemType;
}
