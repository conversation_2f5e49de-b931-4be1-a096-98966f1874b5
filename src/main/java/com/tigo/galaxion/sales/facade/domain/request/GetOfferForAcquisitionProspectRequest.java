package com.tigo.galaxion.sales.facade.domain.request;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@Builder
@ToString
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel
public class GetOfferForAcquisitionProspectRequest {

    @ApiModelProperty(value = "The parent offer id")
    private Long parentOfferId;

    @NotBlank
    @ApiModelProperty(value = "Offer type service group code", required = true, example = "HFC")
    private String serviceGroup;

}
