package com.tigo.galaxion.sales.facade.model.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "account_contact_view")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AccountContactView {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "account_id")
    private String accountId;

    @Column(name = "contact_first_name")
    private String contactFirstName;

    @Column(name = "contact_last_name")
    private String contactLastName;

    @Column(name = "identity_document_identifier")
    private String identityDocumentIdentifier;

    @Column(name = "identity_document_type")
    private String identityDocumentType;

    @Column(name = "identity_document_expiration_date")
    private Date identityDocumentExpirationDate;

    @Column(name = "contact_mobile_phone_number")
    private String contactMobilePhoneNumber;

    @Column(name = "contact_main_email")
    private String contactMainEmail;
}
