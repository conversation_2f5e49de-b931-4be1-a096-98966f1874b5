package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.domain.request.CreateCrossSellRequest;
import com.tigo.galaxion.sales.facade.domain.response.EligibilityResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoCrossSellResponse;
import com.tigo.galaxion.sales.facade.domain.response.OfferTypeEligibilityResponse;
import com.tigo.galaxion.sales.facade.services.ContractSignatureOptionService;
import com.tigo.galaxion.sales.facade.services.CreateCrossSellService;
import com.tigo.galaxion.sales.facade.services.CrossSellService;
import com.tigo.galaxion.sales.facade.services.retrieval.CrossSellRetrievalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.NO_CONTENT;

@Api
@RequiredArgsConstructor
@RestController
public class CrossSellController {

    private final CrossSellService crossSellService;
    private final CreateCrossSellService createCrossSell;
    private final ContractSignatureOptionService contractSignatureOptionService;
    private final CrossSellRetrievalService crossSellRetrievalService;

    @GetMapping("/accounts/{account_id}/cross-sells/offer-types/eligibilities")
    public List<OfferTypeEligibilityResponse> getOfferTypesEligibility(@PathVariable("account_id") String accountId) {
        return crossSellService.getOfferTypesEligibility(accountId);
    }

    @PostMapping("/accounts/{account_id}/cross-sells")
    @ResponseStatus(CREATED)
    public String createCrossSell(
            @PathVariable("account_id") String accountId,
            @Valid @RequestBody CreateCrossSellRequest request) {
        return createCrossSell.createCrossSell(accountId, request);
    }

    @GetMapping("/accounts/{account_id}/cross-sells/service-groups/eligibilities")
    public List<EligibilityResponse> getServiceGroupsEligibility(
            @PathVariable("account_id") String accountId,
            @RequestParam(value = "subscriptionId", required = false) Long subscriptionId,
            @RequestParam(value = "crossSellReference", required = false) String crossSellReference) {
        return crossSellService.getServiceGroupsEligibility(accountId, subscriptionId, crossSellReference);
    }

    @PutMapping("/cross-sells/{reference}/contract-signature-option")
    @ApiOperation("Update contract signature option.")
    @ApiResponses(value = {
            @ApiResponse(code = 204, message = "Contract signature option updated."),
    })
    @ResponseStatus(NO_CONTENT)
    public void updateContractSignatureOption(
            @PathVariable("reference") String reference,
            @RequestParam String contractSignatureOption) {
        contractSignatureOptionService.updateContractSignatureOption(reference, contractSignatureOption);
    }

    @GetMapping("/cross-sells/{reference}")
    @ApiOperation("Get cross sell.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Cross sell details."),
            @ApiResponse(code = 404, message = "Cross sell not found (Problem title = 'cross-sell-not-found')")
    })
    public TigoCrossSellResponse getCrossSellProspect(@PathVariable("reference") String reference) {
        return crossSellRetrievalService.getCrossSell(reference);
    }
}
