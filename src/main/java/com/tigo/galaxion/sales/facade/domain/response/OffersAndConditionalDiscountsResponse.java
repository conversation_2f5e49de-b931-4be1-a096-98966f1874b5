package com.tigo.galaxion.sales.facade.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.HashSet;
import java.util.Set;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OffersAndConditionalDiscountsResponse {

    @Builder.Default
    private Set<ProspectOfferResponse> offers = new HashSet<>();

    @Builder.Default
    private Set<TigoDiscountResponse> offerDiscounts = new HashSet<>();

}
