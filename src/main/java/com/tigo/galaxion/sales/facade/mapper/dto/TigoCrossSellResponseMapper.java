package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoCrossSellResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TigoCrossSellResponseMapper {

    public static TigoCrossSellResponse buildTigoCrossSellResponse(CrossSellResponse crossSellResponse) {
        return TigoCrossSellResponse.builder()
                                    .reference(crossSellResponse.getReference())
                                    .cartUuid(crossSellResponse.getCartUuid())
                                    .status(crossSellResponse.getStatus())
                                    .channelCode(crossSellResponse.getChannelCode())
                                    .channelGroup(crossSellResponse.getChannelGroup())
                                    .brand(crossSellResponse.getBrand())
                                    .offerType(crossSellResponse.getOfferType())
                                    .customerType(crossSellResponse.getCustomerType())
                                    .creditScore(crossSellResponse.getCreditScore())
                                    .accountId(crossSellResponse.getAccountId())
                                    .paymentSettings(crossSellResponse.getPaymentSettings())
                                    .build();
    }

}
