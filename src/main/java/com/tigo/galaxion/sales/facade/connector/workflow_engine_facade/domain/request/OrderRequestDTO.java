package com.tigo.galaxion.sales.facade.connector.workflow_engine_facade.domain.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrderRequestDTO {
    private String requestorUsername;
    private String useCase;
    private String prospectReference;
}
