package com.tigo.galaxion.sales.facade.connector.alfresco.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlfrescoDocumentResponse {
    @JsonProperty("idDocumento")
    private String idDocumento;
    
    @JsonProperty("estado")
    private String estado;
    
    @JsonProperty("observacion")
    private String observacion;
}
