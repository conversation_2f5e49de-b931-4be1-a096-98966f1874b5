package com.tigo.galaxion.sales.facade.connector.contact.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ContactPermissionGroupV2Response {

    private String name;

    private String permissionGroup;

    @Builder.Default
    private List<ContactPermissionV2Response> permissions = new ArrayList<>();
}
