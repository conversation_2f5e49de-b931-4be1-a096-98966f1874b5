package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.equipments;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Set;

@ApiModel
@SuperBuilder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class InclusiveHandsetResponse extends InclusiveEquipmentResponse {

    private String capacity;

    private String networkCompatibility;

    private Set<String> simCardTypes;

}
