package com.tigo.galaxion.sales.facade.connector.evident;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.tigo.galaxion.sales.facade.config.FeignRetryConfig;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentGenerateRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentIdentificationVerifyRequest;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentInitializeRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentValidationRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentVerifyRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentGenerateResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentIdentificationVerifyResponse;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentInitializeResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentQuestionResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentValidationResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentVerifyResponseBody;

import feign.Headers;
import io.swagger.v3.oas.annotations.parameters.RequestBody;

@FeignClient(value = "evident-client", url = "${environment.url.evident-service}", configuration = { FeignRetryConfig.class, EvidentErrorDecoder.class })
public interface EvidentClient {

    @PostMapping("/evident/v1/identification/validation")
    @Headers("Content-Type: application/json")
    EvidentValidationResponseBody validateIdentification(@RequestBody EvidentValidationRequestBody request);
    
    @PostMapping("/evident/v1/otp/initialize")
    @Headers("Content-Type: application/json")
    EvidentInitializeResponseBody initializeOtp(@RequestBody EvidentInitializeRequestBody request);

    @PostMapping("/evident/v1/otp/generate")
    @Headers("Content-Type: application/json")
    EvidentGenerateResponseBody generateOtp(@RequestBody EvidentGenerateRequestBody request);

    @PostMapping("/evident/v1/otp/verify")
    @Headers("Content-Type: application/json")
    EvidentVerifyResponseBody verifyOtp(@RequestBody EvidentVerifyRequestBody request);

    @GetMapping("/evident/v1/identification/question")
    @Headers("Content-Type: application/json")
    EvidentQuestionResponseBody questionIdentification(@RequestParam String identification,
    @RequestParam String validationRegister,
    @RequestParam String typeIdentification,
    @RequestParam String onlyQuestionnaire);

    @PostMapping("/evident/v1/identification/verify")
    @Headers("Content-Type: application/json")
    EvidentIdentificationVerifyResponse verifyIdentification(@RequestBody EvidentIdentificationVerifyRequest request);

}
