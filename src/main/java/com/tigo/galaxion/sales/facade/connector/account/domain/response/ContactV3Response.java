package com.tigo.galaxion.sales.facade.connector.account.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ContactV3Response {

    private Long id;

    private String uuid;

    private LocalDateTime endedAt;

    private String type;

    private String typeDescription;

    private boolean canRead;

    private boolean canWrite;

    private String accountId;

}
