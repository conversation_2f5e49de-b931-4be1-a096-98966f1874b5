package com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.frauds;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.enumeration.IdentificationTypeEnum;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Customer {

    @NotBlank
    @Size(max = 4)
    private IdentificationTypeEnum typeIdentification;

    @NotBlank
    @Size(max = 24)
    private String id;

    @NotBlank
    private String address;
}
