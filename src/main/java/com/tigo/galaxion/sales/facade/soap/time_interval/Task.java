//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.06.17 at 02:45:10 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.time_interval;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for Task complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Task"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="CallID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Number" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="OpenDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="EarlyStart" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="DueDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Area" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Region" type="{http://crmsaleforce.resourcemanager.millicom.com/timeintervalsoap}Region"/&gt;
 *         &lt;element name="District" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Priority" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="TaskTypeCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TaskType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Duration" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="NumberOfRequiredEngineers" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCBillingAccountInfo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="City" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CountryID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Latitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Longitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LateStart" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Street" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Task", propOrder = {
    "callID",
    "number",
    "openDate",
    "earlyStart",
    "dueDate",
    "area",
    "region",
    "district",
    "priority",
    "taskTypeCategory",
    "taskType",
    "duration",
    "numberOfRequiredEngineers",
    "mcBillingAccountInfo",
    "city",
    "countryID",
    "latitude",
    "longitude",
    "lateStart",
    "street"
})
public class Task {

    @XmlElement(name = "CallID", required = true)
    protected String callID;
    @XmlElement(name = "Number")
    protected int number;
    @XmlElement(name = "OpenDate", required = true)
    @XmlSchemaType(name = "date")
    protected String openDate;
    @XmlElement(name = "EarlyStart", required = true)
    @XmlSchemaType(name = "date")
    protected String earlyStart;
    @XmlElement(name = "DueDate", required = true)
    @XmlSchemaType(name = "date")
    protected String dueDate;
    @XmlElement(name = "Area", required = true)
    protected String area;
    @XmlElement(name = "Region", required = true)
    protected Region region;
    @XmlElement(name = "District", required = true)
    protected String district;
    @XmlElement(name = "Priority")
    protected int priority;
    @XmlElement(name = "TaskTypeCategory", required = true)
    protected String taskTypeCategory;
    @XmlElement(name = "TaskType", required = true)
    protected String taskType;
    @XmlElement(name = "Duration")
    protected int duration;
    @XmlElement(name = "NumberOfRequiredEngineers")
    protected int numberOfRequiredEngineers;
    @XmlElement(name = "MCBillingAccountInfo", required = true)
    protected String mcBillingAccountInfo;
    @XmlElement(name = "City", required = true)
    protected String city;
    @XmlElement(name = "CountryID", required = true)
    protected String countryID;
    @XmlElement(name = "Latitude", required = true)
    protected String latitude;
    @XmlElement(name = "Longitude", required = true)
    protected String longitude;
    @XmlElement(name = "LateStart", required = true)
    @XmlSchemaType(name = "date")
    protected String lateStart;
    @XmlElement(name = "Street", required = true)
    protected String street;

    /**
     * Gets the value of the callID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCallID() {
        return callID;
    }

    /**
     * Sets the value of the callID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCallID(String value) {
        this.callID = value;
    }

    /**
     * Gets the value of the number property.
     * 
     */
    public int getNumber() {
        return number;
    }

    /**
     * Sets the value of the number property.
     * 
     */
    public void setNumber(int value) {
        this.number = value;
    }

    /**
     * Gets the value of the openDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOpenDate() {
        return openDate;
    }

    /**
     * Sets the value of the openDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOpenDate(String value) {
        this.openDate = value;
    }

    /**
     * Gets the value of the earlyStart property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEarlyStart() {
        return earlyStart;
    }

    /**
     * Sets the value of the earlyStart property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEarlyStart(String value) {
        this.earlyStart = value;
    }

    /**
     * Gets the value of the dueDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDueDate() {
        return dueDate;
    }

    /**
     * Sets the value of the dueDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDueDate(String value) {
        this.dueDate = value;
    }

    /**
     * Gets the value of the area property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArea() {
        return area;
    }

    /**
     * Sets the value of the area property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArea(String value) {
        this.area = value;
    }

    /**
     * Gets the value of the region property.
     * 
     * @return
     *     possible object is
     *     {@link Region }
     *     
     */
    public Region getRegion() {
        return region;
    }

    /**
     * Sets the value of the region property.
     * 
     * @param value
     *     allowed object is
     *     {@link Region }
     *     
     */
    public void setRegion(Region value) {
        this.region = value;
    }

    /**
     * Gets the value of the district property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDistrict() {
        return district;
    }

    /**
     * Sets the value of the district property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDistrict(String value) {
        this.district = value;
    }

    /**
     * Gets the value of the priority property.
     * 
     */
    public int getPriority() {
        return priority;
    }

    /**
     * Sets the value of the priority property.
     * 
     */
    public void setPriority(int value) {
        this.priority = value;
    }

    /**
     * Gets the value of the taskTypeCategory property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskTypeCategory() {
        return taskTypeCategory;
    }

    /**
     * Sets the value of the taskTypeCategory property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskTypeCategory(String value) {
        this.taskTypeCategory = value;
    }

    /**
     * Gets the value of the taskType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskType() {
        return taskType;
    }

    /**
     * Sets the value of the taskType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskType(String value) {
        this.taskType = value;
    }

    /**
     * Gets the value of the duration property.
     * 
     */
    public int getDuration() {
        return duration;
    }

    /**
     * Sets the value of the duration property.
     * 
     */
    public void setDuration(int value) {
        this.duration = value;
    }

    /**
     * Gets the value of the numberOfRequiredEngineers property.
     * 
     */
    public int getNumberOfRequiredEngineers() {
        return numberOfRequiredEngineers;
    }

    /**
     * Sets the value of the numberOfRequiredEngineers property.
     * 
     */
    public void setNumberOfRequiredEngineers(int value) {
        this.numberOfRequiredEngineers = value;
    }

    /**
     * Gets the value of the mcBillingAccountInfo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCBillingAccountInfo() {
        return mcBillingAccountInfo;
    }

    /**
     * Sets the value of the mcBillingAccountInfo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCBillingAccountInfo(String value) {
        this.mcBillingAccountInfo = value;
    }

    /**
     * Gets the value of the city property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the city property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * Gets the value of the countryID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCountryID() {
        return countryID;
    }

    /**
     * Sets the value of the countryID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCountryID(String value) {
        this.countryID = value;
    }

    /**
     * Gets the value of the latitude property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLatitude() {
        return latitude;
    }

    /**
     * Sets the value of the latitude property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLatitude(String value) {
        this.latitude = value;
    }

    /**
     * Gets the value of the longitude property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLongitude() {
        return longitude;
    }

    /**
     * Sets the value of the longitude property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLongitude(String value) {
        this.longitude = value;
    }

    /**
     * Gets the value of the lateStart property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLateStart() {
        return lateStart;
    }

    /**
     * Sets the value of the lateStart property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLateStart(String value) {
        this.lateStart = value;
    }

    /**
     * Gets the value of the street property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStreet() {
        return street;
    }

    /**
     * Sets the value of the street property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStreet(String value) {
        this.street = value;
    }

}
