package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.domain.response.ProspectusLogResponse;
import com.tigo.galaxion.sales.facade.domain.request.ProspectusLogRequest;
import com.tigo.galaxion.sales.facade.services.ProspectusLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.CREATED;

import javax.servlet.http.HttpServletRequest;

@Api
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1")
public class ProspectusLogController {

    private final ProspectusLogService prospectusLogService;

    @PostMapping("/prospects")
    @ApiOperation("Create the log of the reference data into time.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Log Created."),
    })
    @ResponseStatus(CREATED)
    public ProspectusLogResponse prospectusLog(
            @ApiParam(value = "Prospectus Log Request", required = true) @RequestBody ProspectusLogRequest prospectusLogRequest,
            HttpServletRequest request) {

        String clientIp = getClientIp(request);
        return prospectusLogService.createProspectusLog(prospectusLogRequest, clientIp);
    }

    @GetMapping("/prospects/reference/{prospect_reference}")
    @ApiOperation("Get prospect.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Prospect details."),
            @ApiResponse(code = 404, message = "Prospect not found (Problem title = 'prospect-not-found')")
    })
    public ProspectusLogResponse getProspectReference(@PathVariable("prospect_reference") String prospectReference) {
        return prospectusLogService.getProspectReference(prospectReference);
    }

    private String getClientIp(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("X-Real-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        return ipAddress;
    }
}