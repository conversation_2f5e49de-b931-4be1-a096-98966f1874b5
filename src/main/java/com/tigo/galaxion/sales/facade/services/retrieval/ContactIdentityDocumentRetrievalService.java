package com.tigo.galaxion.sales.facade.services.retrieval;

import com.tigo.galaxion.sales.facade.domain.problem.ContactIdentityDocumentNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.response.ContactIdentityDocumentResponse;
import com.tigo.galaxion.sales.facade.model.entity.ContactIdentityDocumentEntity;
import com.tigo.galaxion.sales.facade.model.repository.ContactIdentityDocumentRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class ContactIdentityDocumentRetrievalService {

    private final ContactIdentityDocumentRepository contactIdentityDocumentRepository;

    @Transactional(readOnly = true)
    public ContactIdentityDocumentEntity getContactIdentityDocumentEntity(String reference) {
        return contactIdentityDocumentRepository
                .findById(reference)
                .orElseThrow(() -> new ContactIdentityDocumentNotFoundProblem(reference));

    }

    @Transactional(readOnly = true)
    public ContactIdentityDocumentResponse getContactIdentityDocumentResponse(String reference) {
        var contactIdentityDocumentEntity = getContactIdentityDocumentEntity((reference));
        return ContactIdentityDocumentResponse
                .builder()
                .documentIdentifier(contactIdentityDocumentEntity.getDocumentIdentifier())
                .documentType(contactIdentityDocumentEntity.getDocumentType())
                .nationality(contactIdentityDocumentEntity.getNationality())
                .build();
    }
}
