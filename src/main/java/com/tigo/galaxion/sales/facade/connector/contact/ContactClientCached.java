package com.tigo.galaxion.sales.facade.connector.contact;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.contact.AcquisitionPermissionGroupListResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.contact.AcquisitionPermissionGroupResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.contact.AcquisitionPermissionResponse;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactPermissionGroupV2Response;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactPermissionV2Response;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ContactClientCached {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContactClientCached.class);

    public static final String ALLOW_FOTS_CONTACT = "ALLOW_FOTS_CONTACT";
    public static final String NO_LONGER_CUSTOMER = "NO_LONGER_CUSTOMER";

    private final ContactClient contactClient;

    @Cacheable(value = "getPermissions")
    public AcquisitionPermissionGroupListResponse getPermissions() {
        var contactPermissionListV2Response = contactClient.getPermissions();
        var acquisitionPermissionResponses = contactPermissionListV2Response.getPermissions()
                                                                            .stream()
                                                                            .map(this::buildAcquisitionPermissionResponse)
                                                                            .filter(acquisitionPermissionResponse -> !ALLOW_FOTS_CONTACT.equals(acquisitionPermissionResponse.getPermission()))
                                                                            .collect(Collectors.toList());
        var acquisitionPermissionGroupResponses = contactPermissionListV2Response.getPermissionGroups()
                                                                                 .stream()
                                                                                 .map(contactPermissionGroupV2Response -> buildAcquisitionPermissionGroupResponse(acquisitionPermissionResponses,
                                                                                                                                                                  contactPermissionGroupV2Response))
                                                                                 .filter(acquisitionPermissionGroupResponse -> !NO_LONGER_CUSTOMER.equals(acquisitionPermissionGroupResponse.getPermissionGroup()))
                                                                                 .collect(Collectors.toList());
        return AcquisitionPermissionGroupListResponse.builder()
                                                     .allowThirdParty(false)
                                                     .permissionGroups(acquisitionPermissionGroupResponses)
                                                     .build();
    }

    private AcquisitionPermissionResponse buildAcquisitionPermissionResponse(ContactPermissionV2Response contactPermissionV2Response) {
        return AcquisitionPermissionResponse.builder()
                                            .permission(contactPermissionV2Response.getPermission())
                                            .name(contactPermissionV2Response.getName())
                                            .build();
    }

    private AcquisitionPermissionGroupResponse buildAcquisitionPermissionGroupResponse(List<AcquisitionPermissionResponse> acquisitionPermissionResponses, ContactPermissionGroupV2Response contactPermissionGroupV2Response) {
        return AcquisitionPermissionGroupResponse.builder()
                                                 .permissionGroup(contactPermissionGroupV2Response.getPermissionGroup())
                                                 .name(contactPermissionGroupV2Response.getName())
                                                 .permissions(acquisitionPermissionResponses)
                                                 .build();
    }

    @CacheEvict(allEntries = true, cacheNames = {
            "getPermissions",
    })
    @Scheduled(fixedDelayString = "${time-to-live.cache.contacts-service}")
    public void cacheEvict() {
        LOGGER.info("ContactClientCached.cacheEvict called.");
    }
}
