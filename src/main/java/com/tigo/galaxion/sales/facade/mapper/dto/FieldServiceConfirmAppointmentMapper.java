package com.tigo.galaxion.sales.facade.mapper.dto;

import lombok.NoArgsConstructor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.GregorianCalendar;
import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;

import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointment;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.MCMVAssets;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.MCServices;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.ProcessTaskExRequest;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.Region;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.Task;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.TaskRequest;

import lombok.AccessLevel;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class FieldServiceConfirmAppointmentMapper {

    /***
     * Default values come from requirement
     *
     * @param data
     * @return
     * @throws ParseException
     * @throws DatatypeConfigurationException
     */
    public static ProcessTaskExRequest buildProcessTaskExRequest(FieldServiceConfirmAppointment data)
            throws DatatypeConfigurationException, ParseException {
        ProcessTaskExRequest request = new ProcessTaskExRequest();

        Task task = new Task();
        TaskRequest taskRequest = new TaskRequest();
        if (data.getCallId() != null) {
            taskRequest.setCallID(data.getCallId());
        }

        if (data.getNumber() != null) {
            taskRequest.setNumber(data.getNumber());
        }

        if (data.getPriority() != null) {
            taskRequest.setPriority(data.getPriority());
        }

        if (data.getDuration() != null) {
            taskRequest.setDuration(data.getDuration());
        }

        if (data.getEarlyStart() != null) {
            taskRequest.setEarlyStart(data.getEarlyStart());
        }

        if (data.getLateStart() != null) {
            taskRequest.setLateStart(data.getLateStart());
        }

        if (data.getDueDate() != null) {
            taskRequest.setDueDate(data.getDueDate());
        }

        if (data.getOpenDate() != null) {
            taskRequest.setOpenDate(data.getOpenDate());
        }

        if (data.getArea() != null) {
            taskRequest.setArea(data.getArea());
        }

        if (data.getRegion() != null) {
            Region region = new Region();
            region.setName(data.getRegion());
            taskRequest.setRegion(region);
        }

        if (data.getDistrict() != null) {
            taskRequest.setDistrict(data.getDistrict());
        }
        if (data.getStreet() != null) {
            taskRequest.setStreet(data.getStreet());
        }
        if (data.getCity() != null) {
            taskRequest.setCity(data.getCity());
        }
        if (data.getMcState() != null) {
            taskRequest.setMCState(data.getMcState());
        }
        if (data.getCountryId() != null) {
            taskRequest.setCountryID(data.getCountryId());
        }
        if (data.getTdRequired() != null) {
            taskRequest.setTDRequired(data.getTdRequired());
        }
        if (data.getRequiredCrewSize() != null) {
            taskRequest.setRequiredCrewSize(data.getRequiredCrewSize());
        }
        if (data.getNumberOfRequiredEngineers() != null) {
            taskRequest.setNumberOfRequiredEngineers(data.getNumberOfRequiredEngineers());
        }
        if (data.getTaskTypeCategory() != null) {
            taskRequest.setTaskTypeCategory(data.getTaskTypeCategory());
        }
        if (data.getTaskType() != null) {
            taskRequest.setTaskType(data.getTaskType());
        }
        if (data.getMcComment() != null) {
            taskRequest.setMCComment(data.getMcComment());
        }
        if (data.getMcCrmComment() != null) {
            taskRequest.setMCCRMComment(data.getMcCrmComment());
        }

        if (data.getMcContactEmail() != null) {
            taskRequest.setMCContactEmail(data.getMcContactEmail());
        }
        if (data.getMcCustomerCode() != null) {
            taskRequest.setMCCustomerCode(data.getMcCustomerCode());
        }
        if (data.getMcCustomerPhoneNumber() != null) {
            taskRequest.setMCCustomerPhoneNumber(data.getMcCustomerPhoneNumber());
        }
        if (data.getMcStatusFCVToken() != null) {
            taskRequest.setMCStatusFCVToken(data.getMcStatusFCVToken());
        }
        if (data.getCustomer() != null) {
            taskRequest.setCustomer(data.getCustomer());
        }
        if (data.getContactPhoneNumber() != null) {
            taskRequest.setContactPhoneNumber(data.getContactPhoneNumber());
        }
        if (data.getMcWorkPackageDescription() != null) {
            taskRequest.setMCWorkPackageDescription(data.getMcWorkPackageDescription());
        }
        if (data.getMcConnectionData() != null) {
            taskRequest.setMCConnectionData(data.getMcConnectionData());
        }
        if (data.getMcBillingAccountInfo() != null) {
            taskRequest.setMCBillingAccountInfo(data.getMcBillingAccountInfo());
        }
        if (data.getAppointmentStart() != null) {
            taskRequest.setAppointmentStart(data.getAppointmentStart());
        }
        if (data.getAppointmentFinish() != null) {
            taskRequest.setAppointmentFinish(data.getAppointmentFinish());
        }
        if (data.getLatitude() != null && data.getLongitude() != null) {
            taskRequest.setLatitude(data.getLatitude());
            taskRequest.setLongitude(data.getLongitude());
        }

        MCServices mcServices = new MCServices();
        if (data.getMcMvServices() != null) {
            mcServices.setMCService(data.getMcMvServices());
        }
        taskRequest.setMCMVServices(mcServices);

        if (data.getMcMvAssets() != null) {
            MCMVAssets mcMvAssets = new MCMVAssets();
            mcMvAssets.setMCAsset(data.getMcMvAssets());
            taskRequest.setMCMVAssets(mcMvAssets);
        }

        if (data.getContactName() != null) {
            taskRequest.setContactName(data.getContactName());
        }

        if (data.getMcInfoCustomerSite() != null) {
            taskRequest.setMCInfoCustomerSite(data.getMcInfoCustomerSite());
        }

        if (data.getMcOpeningReason() != null) {
            taskRequest.setMCOpeningReason(data.getMcOpeningReason());
        }

        if (data.getContractType() != null) {
            taskRequest.setContractType(data.getContractType());
        }

        if (data.getMcCustomerClass() != null) {
            taskRequest.setMCCustomerClass(data.getMcCustomerClass());
        }

        if (data.getMcServicePaid() != null) {
            taskRequest.setMCServicePaid(data.getMcServicePaid());
        }

        if (data.getMcCustomerIdentityNumber() != null) {
            taskRequest.setMCCustomerIdentityNumber(data.getMcCustomerIdentityNumber());
        }

        if (data.getMcConfirmationStatus() != null) {
            taskRequest.setMCConfirmationStatus(data.getMcConfirmationStatus());
        }

        if (data.getMcCustomsSeal() != null) {
            taskRequest.setMCCustomsSeal(data.getMcCustomsSeal());
        }

        if (data.getMcRecurrentClient() != null) {
            taskRequest.setMCRecurrentClient(data.getMcRecurrentClient());
        }

        if (data.getMcTap() != null) {
            taskRequest.setMCTap(data.getMcTap());
        }

        if (data.getMcBoca() != null) {
            taskRequest.setMCBoca(data.getMcBoca());
        }

        if(data.getMcCRMCancellationReasonC() != null){
            task.setMCCRMCancellationReasonC(data.getMcCRMCancellationReasonC());
        }

        if(data.getStatus() != null){
            task.setStatus(data.getStatus());
        } else {
            task.setStatus("");
        }

        task.setTask(taskRequest);
        request.setProcessTaskEx(task);
        return request;
    }

    static XMLGregorianCalendar toXMLGregorianCalendar(String dateString)
            throws DatatypeConfigurationException, ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.sssX");
        Date date = dateFormat.parse(dateString);

        GregorianCalendar gregorianCalendar = new GregorianCalendar();
        gregorianCalendar.setTime(date);

        return DatatypeFactory.newInstance().newXMLGregorianCalendar(gregorianCalendar);
    }
}
