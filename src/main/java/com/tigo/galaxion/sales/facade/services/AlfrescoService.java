package com.tigo.galaxion.sales.facade.services;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.OfferResponse;
import com.tigo.galaxion.sales.facade.connector.alfresco.AlfrescoAuthServiceProperties;
import com.tigo.galaxion.sales.facade.connector.alfresco.AlfrescoClient;
import com.tigo.galaxion.sales.facade.connector.alfresco.MetadataFormato42;
import com.tigo.galaxion.sales.facade.connector.alfresco.MetadataFormato8;
import com.tigo.galaxion.sales.facade.connector.alfresco.MetadataFormato9;
import com.tigo.galaxion.sales.facade.connector.alfresco.MetadataProperties;
import com.tigo.galaxion.sales.facade.connector.alfresco.request.AlfrescoDocumentRequest;
import com.tigo.galaxion.sales.facade.connector.alfresco.request.AlfrescoGenerateTokenRequest;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoAuthResponse;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoDocumentResponse;
import com.tigo.galaxion.sales.facade.connector.catalog.CatalogClient;
import com.tigo.galaxion.sales.facade.connector.catalog.domain.response.PricePlan;
import com.tigo.galaxion.sales.facade.connector.catalog.domain.response.TariffPlansResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AlfrescoService {
    @Autowired
    private AlfrescoClient authClient;

    @Autowired
    private AlfrescoAuthServiceProperties authServiceProperties;

    @Autowired
    private ProspectLeadService prospectLeadService;

    @Autowired
    private CatalogClient catalogClient;

    @Autowired
    private final AcquisitionProspectClient acquisitionProspectClient;

    @Autowired
    private ObjectMapper objectMapper;

    private AlfrescoAuthResponse authResponse;
    private long tokenAcquiredTime;

    @Value("${alfresco.params.idTramite}")
    private Integer idTramite;
    @Value("${alfresco.params.idCanal}")
    private Integer idCanal;
    @Value("${alfresco.params.reqFirmaRemota}")
    private String reqFirmaRemota;
    @Value("${alfresco.params.reqFirmaManuscrita}")
    private String reqFirmaManuscrita;
    @Value("${alfresco.params.reqFirmaElectronica}")
    private String reqFirmaElectronica;
    @Value("${alfresco.params.urlRespuesta}")
    private String urlRespuesta;

    public AlfrescoAuthResponse getJsonAccessToken() {
        if (authResponse == null || isTokenExpired()) {
            var body = AlfrescoGenerateTokenRequest.builder().username(authServiceProperties.getUsername())
                    .password(authServiceProperties.getPassword()).grant_type("password").build();
            authResponse = authClient.getToken(authServiceProperties.getAuthorization(), body);
            tokenAcquiredTime = System.currentTimeMillis();
        }
        return authResponse;
    }

    public String getAccessToken() {
        return getJsonAccessToken().getAccessToken();
    }

    private boolean isTokenExpired() {
        int slackTime = 5 * 1000; // tiempo de 5 segundos de holgura para evitar problemas en la utilización del
                                  // token
        long currentTime = slackTime + System.currentTimeMillis();
        long expiresInMillis = authResponse.getExpiresIn() * 1000;
        return (currentTime - tokenAcquiredTime) >= expiresInMillis;
    }

    public AlfrescoDocumentResponse documentFileAlfresco(AlfrescoDocumentRequest request) {
        String token = "Bearer " + getAccessToken();
        return authClient.createFile(token, request);
    }

    public AlfrescoDocumentResponse documentFileAlfrescoByProspectId(String prospectId) {
        // Obtiene token
        String token = "Bearer " + getAccessToken();

        // Obtener información relacionada con el prospectId
        ProspectLeadResponse prospectLeadResponse = getProspectLeadInfo(prospectId);
        CartResponse cartResponse = getProspectCart(prospectId);
        AcquisitionProspectResponse acquisitionProspectResponse = getAcquisitionProspect(prospectId);

        // Crear la solicitud AlfrescoDocumentRequest
        AlfrescoDocumentRequest documentRequest = buildAlfrescoDocumentRequest(
                prospectLeadResponse, cartResponse, acquisitionProspectResponse);

        // Convertir la solicitud a JsonNode
        JsonNode jsonNode = objectMapper.valueToTree(documentRequest);

        // Enviar la solicitud a Alfresco
        return authClient.createFileByProspectId(token, jsonNode);
    }

    // Métodos auxiliares que encapsulan partes del proceso

    protected ProspectLeadResponse getProspectLeadInfo(String prospectId) {
        return prospectLeadService.getAllProspectInfo(prospectId);
    }

    protected CartResponse getProspectCart(String prospectId) {
        return acquisitionProspectClient.getProspectCart(prospectId);
    }

    protected AcquisitionProspectResponse getAcquisitionProspect(String prospectId) {
        return acquisitionProspectClient.getAcquisitionProspect(prospectId);
    }

    protected AlfrescoDocumentRequest buildAlfrescoDocumentRequest(
            ProspectLeadResponse prospectLeadResponse,
            CartResponse cartResponse,
            AcquisitionProspectResponse acquisitionProspectResponse) {

        List<String> penaltyCodes = Arrays.asList("CC_FIXED_PERMANENCE_PENALTY_TV", "CC_FIXED_PERMANENCE_PENALTY_HFC");
        List<String> feeCodes = Arrays.asList("PRICE_HFC_PARTIAL_ACTIVATION_FEE", "PRICE_TV_PARTIAL_ACTIVATION_FEE");

        var penalty = 0.0;
        var appliedDiscount = false;
        for (OfferResponse offer : cartResponse.getOffers()) {
            var response = catalogClient.getTariffPlans(offer.getBaseOffer().getCatalogTariffPlanCode());
            if (response != null) {
                penalty += response.getTerminationEccCharge().getPricePlans().stream()
                        .filter(pricePlan -> penaltyCodes.contains(pricePlan.getCode()))
                        .mapToInt(PricePlan::getMaxPrice)
                        .sum();

                if (!appliedDiscount) {
                    appliedDiscount = true;

                    penalty -= response.getTerminationEccCharge().getPricePlans().stream()
                            .filter(pricePlan -> feeCodes.contains(pricePlan.getCode()))
                            .mapToInt(PricePlan::getMaxPrice)
                            .sum();
                }
            }
        }

        String newReqFirmaManuscrita = reqFirmaManuscrita;
        if("TELESALES".equals(cartResponse.getChannelGroup())){
            newReqFirmaManuscrita = "false";
        }

        MetadataFormato9 formato9 = new MetadataFormato9(prospectLeadResponse);
        MetadataFormato42 formato42 = new MetadataFormato42(prospectLeadResponse, cartResponse, penalty);
        MetadataFormato8 formato8 = new MetadataFormato8(prospectLeadResponse);
        MetadataProperties properties = new MetadataProperties(prospectLeadResponse, acquisitionProspectResponse);

        return AlfrescoDocumentRequest.builder()
                .idTramite(idTramite)
                .idCanal(idCanal)
                .correo(prospectLeadResponse.getCustomer().getContactEmail())
                .reqFirmaRemota(reqFirmaRemota)
                .reqFirmaManuscrita(newReqFirmaManuscrita)
                .reqFirmaElectronica(reqFirmaElectronica)
                .urlRespuesta(urlRespuesta)
                .metadata(Arrays.asList(
                        formato9.toMetadataString(),
                        formato42.toMetadataString(),
                        formato8.toMetadataString()))
                .properties(properties.toMetadataString())
                .build();
    }
}
