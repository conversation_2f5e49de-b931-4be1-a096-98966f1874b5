package com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class FraudsResponseBody {

    /***
     * Error code in a response message. The value 100 indicates that the service
     * processing is successful; otherwise, it means an error.
     */
    private String code;

    /***
     * Error code description.
     */
    private String message;
}
