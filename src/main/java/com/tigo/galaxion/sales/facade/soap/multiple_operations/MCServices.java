//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:50:36 PM CST 
//

package com.tigo.galaxion.sales.facade.soap.multiple_operations;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * Java class for MCServices complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="MCServices"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Stamp" type="{http://www.clicksoftware.com}Stamp" minOccurs="0"/&gt;
 *         &lt;element name="ServiceName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ExternalRefID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MobileKey" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="IsUsed" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="Status" type="{http://www.clicksoftware.com}MCServiceStatusReference" minOccurs="0"/&gt;
 *         &lt;element name="AdditionalData" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="CompletionReason" type="{http://www.clicksoftware.com}MCServiceCompletionReasonReference" minOccurs="0"/&gt;
 *         &lt;element name="IsServiceInternet" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="Code" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MCServices", propOrder = {
        "stamp",
        "serviceName",
        "externalRefID",
        "mobileKey",
        "isUsed",
        "status",
        "additionalData",
        "completionReason",
        "isServiceInternet",
        "code"
})
@Data
@NoArgsConstructor
public class MCServices {

    @XmlElement(name = "Stamp")
    protected Stamp stamp;
    @XmlElement(name = "ServiceName")
    protected String serviceName;
    @XmlElement(name = "ExternalRefID")
    protected String externalRefID;
    @XmlElement(name = "MobileKey")
    protected String mobileKey;
    @XmlElement(name = "IsUsed")
    protected Boolean isUsed;
    @XmlElement(name = "Status")
    protected MCServiceStatusReference status;
    @XmlElement(name = "AdditionalData")
    protected String additionalData;
    @XmlElement(name = "CompletionReason")
    protected MCServiceCompletionReasonReference completionReason;
    @XmlElement(name = "IsServiceInternet")
    protected Boolean isServiceInternet;
    @XmlElement(name = "Code")
    protected String code;
}
