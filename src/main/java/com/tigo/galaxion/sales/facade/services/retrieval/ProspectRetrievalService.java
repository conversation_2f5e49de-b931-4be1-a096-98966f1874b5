package com.tigo.galaxion.sales.facade.services.retrieval;

import com.google.gson.Gson;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoProspectResponse;
import com.tigo.galaxion.sales.facade.mapper.dto.TigoProspectResponseMapper;
import com.tigo.galaxion.sales.facade.model.repository.ProspectRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class ProspectRetrievalService {

    private final AcquisitionProspectClient acquisitionProspectClient;
    private final ProspectRepository prospectRepository;

    @Transactional(readOnly = true)
    public TigoProspectResponse getProspect(String prospectReference) {
        AcquisitionProspectResponse acquisitionProspectResponse = acquisitionProspectClient.getAcquisitionProspect(prospectReference);
        var prospectResponse = TigoProspectResponseMapper.buildTigoProspectResponse(acquisitionProspectResponse);
        retrieveAndSetContractSignatureOption(prospectResponse, prospectReference);
        return prospectResponse;
    }

    private void retrieveAndSetContractSignatureOption(TigoProspectResponse prospectResponse, String prospectReference) {
        var prospectEntity = prospectRepository.findByReference(prospectReference);
        if (prospectEntity.isPresent()) {
            prospectResponse.setContractSignatureOption(prospectEntity.get().getContractSignatureOption());
        }
    }

}
