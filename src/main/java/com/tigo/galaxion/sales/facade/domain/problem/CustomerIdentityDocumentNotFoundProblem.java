package com.tigo.galaxion.sales.facade.domain.problem;
 
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;
 
public class CustomerIdentityDocumentNotFoundProblem extends AbstractThrowableProblem {
    public CustomerIdentityDocumentNotFoundProblem(String identifier, String type) {
        super(null,
              "customer-identity-document-not-found",
              Status.NOT_FOUND,
              String.format("The customer identity document with contact Identifier %s and Type %s not found.", identifier, type));
    }
}