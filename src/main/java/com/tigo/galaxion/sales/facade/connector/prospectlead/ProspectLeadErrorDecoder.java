package com.tigo.galaxion.sales.facade.connector.prospectlead;

import feign.Response;
import feign.codec.Decoder;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

import org.apache.commons.lang3.StringUtils;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;
import org.zalando.problem.ThrowableProblem;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonIOException;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.problem.ProspectLeadProblem;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadErrorResponse;

@Slf4j
@RequiredArgsConstructor
public class ProspectLeadErrorDecoder implements ErrorDecoder, Decoder {

    private static final String TITLE = "prospectlead-service-error";
    private final Gson gson = new Gson();

    @Override
    public Object decode(Response response, Type type) throws IOException {
        try {
            Reader reader = new InputStreamReader(response.body().asInputStream(), StandardCharsets.UTF_8);
            return gson.fromJson(reader, type);
        } catch (JsonIOException e) {
            if (e.getCause() != null &&
                    e.getCause() instanceof IOException) {
                throw IOException.class.cast(e.getCause());
            }
            throw e;
        }
    }

    @Override
    public Exception decode(String methodKey, Response response) {
        throw new ProspectLeadProblem(getThrowableProblem(response));
    }

    private ThrowableProblem getThrowableProblem(Response response) {
        if (StringUtils.isNotBlank(response.body().toString())) {
            log.error(response.body().toString());
        }

        ProspectLeadErrorResponse message = null;
        try (InputStream bodyIs = response.body().asInputStream()) {
            ObjectMapper mapper = new ObjectMapper();
            message = mapper.readValue(bodyIs, ProspectLeadErrorResponse.class);

            var problemBuilder = Problem
                    .builder()
                    .withDetail(message.getErrorMessage())
                    .withStatus(Status.valueOf(response.status()))
                    .withTitle(TITLE);
            return problemBuilder.build();

        } catch (IOException e) {
            var problemBuilder = Problem
                    .builder()
                    .withDetail(e.getMessage())
                    .withStatus(Status.valueOf(response.status()))
                    .withTitle(TITLE);
            return problemBuilder.build();
        }

    }
}