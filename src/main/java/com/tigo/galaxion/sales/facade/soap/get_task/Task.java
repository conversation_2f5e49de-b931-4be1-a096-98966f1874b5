
package com.tigo.galaxion.sales.facade.soap.get_task;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for Task complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Task"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Task" type="{http://crmsaleforce.resourcemanager.millicom.com/gettasksoap}TaskRequest"/&gt;
 *         &lt;element name="GetAssignment" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Task", propOrder = {
    "task",
    "getAssignment"
})
public class Task {

    @XmlElement(name = "Task", required = true)
    protected TaskRequest task;
    @XmlElement(name = "GetAssignment")
    protected Boolean getAssignment;

    /**
     * Gets the value of the task property.
     * 
     * @return
     *     possible object is
     *     {@link TaskRequest }
     *     
     */
    public TaskRequest getTask() {
        return task;
    }

    /**
     * Sets the value of the task property.
     * 
     * @param value
     *     allowed object is
     *     {@link TaskRequest }
     *     
     */
    public void setTask(TaskRequest value) {
        this.task = value;
    }

    /**
     * Gets the value of the getAssignment property.
     * 
     */
    public boolean isGetAssignment() {
        return getAssignment;
    }

    /**
     * Sets the value of the getAssignment property.
     * 
     */
    public void setGetAssignment(Boolean value) {
        this.getAssignment = value;
    }

}
