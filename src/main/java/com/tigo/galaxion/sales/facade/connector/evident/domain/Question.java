package com.tigo.galaxion.sales.facade.connector.evident.domain;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class Question {
    
    @JsonProperty("id")
    private String id; 

    @JsonProperty("text")
    private String text; 

    @JsonProperty("idCorrectAnswer")
    private String idCorrectAnswer; 

    @JsonProperty("order")
    private String order; 

    @JsonProperty("weight")
    private String weight;

    @JsonProperty("answer")
    private List<AnswerMapQuestion> answer; 

}
