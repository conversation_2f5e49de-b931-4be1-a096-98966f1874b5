package com.tigo.galaxion.sales.facade.config;

import java.util.List;
import java.util.Map;

import org.springframework.util.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.tigo.galaxion.sales.facade.connector.authenticator.AuthenticatorClient;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class FeignClientInterceptor implements RequestInterceptor {
  
  @Value("${environment.auth.name}")
  private String envAuthName;
  
  @Value("${environment.auth.enable}")
  private Boolean envAuthEnable;
  
  private final AuthenticatorClient authenticatorClient;

	
  private static final String AUTHORIZATION_HEADER = "Authorization";
  private List<String> insecureFeignClients = List.of(
      "tigo-alfresco-service", "tigo-credit-scores-service", "otp-management-token-service",
      "tigo-prospectlead-service", "equipment-service");
  
  private List<String> secureFeignClients = List.of("tigo-tecnical-feasibility", "tigo-riskassessment-service", 
		  "tigo-georeference-service", "tigo-fraud-management-service", "evident-client", "external-customer-search-client");

  public static String getBearerTokenHeader() {
    return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest()
        .getHeader(AUTHORIZATION_HEADER);
  }

  @Override
  public void apply(RequestTemplate requestTemplate) {
    String clientName = requestTemplate.feignTarget().name();
    
    if (!StringUtils.pathEquals(clientName, envAuthName)) {
      if (envAuthEnable && secureFeignClients.contains(clientName)) {
	   	var attributes = RequestContextHolder.getRequestAttributes();
	   	var evictAtrr = attributes != null ? attributes.getAttribute("evict", RequestAttributes.SCOPE_REQUEST) : null;
	   	Boolean evict = evictAtrr != null  ? (Boolean) evictAtrr : false;
	       
	  	final var authResponse = authenticatorClient.authentication(Map.of("evict-cache", evict.toString()));
	  	final var token = authResponse.getTokenType().concat(" ").concat(authResponse.getAccessToken());
	  	requestTemplate.header(AUTHORIZATION_HEADER, token);
	  	
	  } else if (!insecureFeignClients.contains(clientName)) {
	    requestTemplate.header(AUTHORIZATION_HEADER, getBearerTokenHeader());    
	  }
    }
  }
}
