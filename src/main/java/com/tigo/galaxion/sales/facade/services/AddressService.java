package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.address.domain.request.AddressRequest;
import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import com.tigo.galaxion.sales.facade.mapper.dto.AddressRequestMapper;
import com.tigo.galaxion.sales.facade.connector.address.AddressClient;
import com.tigo.galaxion.sales.facade.domain.response.AddressMSResponse;

import com.tigo.galaxion.sales.facade.domain.response.AddressNormalizeResponseBody;
import com.tigo.galaxion.sales.facade.mapper.dto.AddressNormalizeMapper;
import com.tigo.galaxion.sales.facade.connector.georeference.GeoreferenceClient;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.response.GeoreferenceResponseBody;
import com.tigo.galaxion.sales.facade.domain.request.AddressNormalizeRequestBody;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;

@Service
@RequiredArgsConstructor
public class AddressService {

    private final GeoreferenceClient georeferenceClient;
    private final AddressClient addressClient;

    @Value("${normalize.default.stratum}")
    private String stratum;

    public AddressNormalizeResponseBody normalizeAddress(AddressNormalizeRequestBody request) {

        var georeferenceRequest = AddressNormalizeMapper.buildAddressRequest(request);

        GeoreferenceResponseBody response = georeferenceClient.normalize(georeferenceRequest);

        if (response.getGeorreferenceResponse().getGisCommonInfoDir().getStratum() == null
                || "".equals(response.getGeorreferenceResponse().getGisCommonInfoDir().getStratum())) {
            if (stratum != null) {
                response.getGeorreferenceResponse().getGisCommonInfoDir().setStratum(stratum);
            } else {
                response.getGeorreferenceResponse().getGisCommonInfoDir().setStratum("3");
            }
        }

        return AddressNormalizeMapper.buildAddressResponse(response);
    }

    public AddressMSResponse getAddress(Long id) {
        AddressResponse response = addressClient.getAddress(id);
        return AddressRequestMapper.buildAddressResponseForMS(response);
    }

    public long createAddress(AddressMSResponse address) {
        AddressRequest request = AddressRequestMapper.buildAddressRequestForMS(address);
        return addressClient.createAddress(request);
    }

    public void updateAddress(Long id, AddressMSResponse address) {
        AddressRequest request = AddressRequestMapper.buildAddressRequestForMS(address);
        addressClient.updateAddress(id, request);
    }

}
