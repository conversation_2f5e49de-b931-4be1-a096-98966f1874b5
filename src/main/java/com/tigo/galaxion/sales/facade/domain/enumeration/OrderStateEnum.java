package com.tigo.galaxion.sales.facade.domain.enumeration;

import lombok.Getter;

@Getter
public enum OrderStateEnum {
    COMPLETED("COMPLETED"),
    SKIPPED("SKIPPED"),
    RECEIVED("RECEIVED"),
    IN_PROGRESS("IN_PROGRESS"),
    PENDING("PENDING"),
    ERROR("ERROR"),
    PARTIAL("PARTIAL");

    private final String state;

    OrderStateEnum(String state) {
        this.state = state;
    }

    public static OrderStateEnum fromString(String state) {
        for (OrderStateEnum orderState : OrderStateEnum.values()) {
            if (orderState.getState().equalsIgnoreCase(state)) {
                return orderState;
            }
        }
        throw new IllegalArgumentException("Invalid state: " + state);
    }
}
