package com.tigo.galaxion.sales.facade.connector.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;

import com.tigo.galaxion.sales.facade.domain.problem.FailedToRenewAccessTokenProblem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.openfeign.support.AbstractFormWriter;
//import org.springframework.cloud.openfeign.support.JsonFormWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.client.OAuth2AuthorizeRequest;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.zalando.problem.Status;

import java.time.Instant;
import java.util.List;

import static java.lang.String.format;
import static java.util.Objects.isNull;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class PrivateAuthFeignClientConfiguration {

    public static final String CLIENT_REGISTRATION_ID = "keycloak";
    private final OAuth2AuthorizedClientManager authorizedClientManager;
    private final ClientRegistrationRepository clientRegistrationRepository;
    @Value("${spring.application.name}")
    private String applicationName;
    private List<String> secureFeignClients = List.of("WorkflowEngineClient");

    @Bean
    public RequestInterceptor oAuth2FeignRequestInterceptor() {
        return requestTemplate -> {
            String clientName = requestTemplate.feignTarget().name();
            if (secureFeignClients.contains(clientName)) {
                var clientRegistration = clientRegistrationRepository.findByRegistrationId(CLIENT_REGISTRATION_ID);
                String accessToken = getAccessToken(authorizedClientManager, clientRegistration);
                requestTemplate.header("Authorization");
                requestTemplate.header("Authorization", "Bearer " + accessToken);
            }
        };
    }
    private String getAccessToken(OAuth2AuthorizedClientManager manager, ClientRegistration clientRegistration) {
        var authorizeRequest = OAuth2AuthorizeRequest
                .withClientRegistrationId(clientRegistration.getRegistrationId())
                .principal(applicationName)
                .build();

        if (authorizeRequest.getAuthorizedClient() == null
                || authorizeRequest.getAuthorizedClient().getAccessToken() == null
                || authorizeRequest.getAuthorizedClient().getAccessToken().getExpiresAt() == null
                || Instant.now().compareTo(authorizeRequest.getAuthorizedClient().getAccessToken().getExpiresAt()) >= 0) {

            OAuth2AuthorizedClient client = manager.authorize(authorizeRequest);
            if (isNull(client)) {
                throw new FailedToRenewAccessTokenProblem(
                        "error.oauth.cannot_authorize",
                        format("Failed to authorize client with registrationId %s", clientRegistration.getRegistrationId()),
                        Status.INTERNAL_SERVER_ERROR
                );
            }
            return client.getAccessToken().getTokenValue();
        }

        return authorizeRequest.getAuthorizedClient().getAccessToken().getTokenValue();
    }
}
