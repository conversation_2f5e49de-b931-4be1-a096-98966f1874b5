package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class CreateOtherEquipmentV2Request {
	@Valid
	private List<CreateAttributeV2Request> attributes;
	@NotBlank
	private String catalogCode;
	@NotNull
	private Integer orderId;
	private Integer price;
	private String pricePlanCatalogCode;
	private String type;
}
