//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:48:22 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.process_task_ex;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ProcessTaskEx" type="{http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap}Task"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "processTaskEx"
})
@XmlRootElement(name = "ProcessTaskExRequest")
public class ProcessTaskExRequest {

    @XmlElement(name = "ProcessTaskEx", required = true)
    protected Task processTaskEx;

    /**
     * Gets the value of the processTaskEx property.
     * 
     * @return
     *     possible object is
     *     {@link Task }
     *     
     */
    public Task getProcessTaskEx() {
        return processTaskEx;
    }

    /**
     * Sets the value of the processTaskEx property.
     * 
     * @param value
     *     allowed object is
     *     {@link Task }
     *     
     */
    public void setProcessTaskEx(Task value) {
        this.processTaskEx = value;
    }

}
