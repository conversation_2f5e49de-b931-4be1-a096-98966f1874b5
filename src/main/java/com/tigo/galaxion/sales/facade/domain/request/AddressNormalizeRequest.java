package com.tigo.galaxion.sales.facade.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

@Builder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor

public class AddressNormalizeRequest {


    @NotBlank
    @ApiModelProperty(value = "The country code.", required = true, example = "57")
    @Size(max = 6)
    @JsonProperty("countryCode")
    private String countryCode;

    @NotBlank
    @ApiModelProperty(value = "The department code.", required = true, example = "5")
    @Size(max = 6)
    @JsonProperty("departmentCode")
    private String departmentCode;

    @NotBlank
    @ApiModelProperty(value = "The municipality code.", required = true, example = "5631000")
    @Size(max = 6)
    @JsonProperty("municipalityCode")
    private String municipalityCode;

    @NotBlank
    @ApiModelProperty(value = "The natural address.", required = true, example = "CR 43 A")
    @JsonProperty("naturalAddress")
    private String naturalAddress;
}
