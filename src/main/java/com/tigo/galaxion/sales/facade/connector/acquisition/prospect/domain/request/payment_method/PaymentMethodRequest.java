package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.payment_method;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.PaymentMethodTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "paymentMethodType")
@JsonSubTypes({@JsonSubTypes.Type(value = DirectDebitPaymentMethodRequest.class),
        // For other payment method insert case here
})
@ApiModel(description = "Payment method request", discriminator = "paymentMethodType")
public interface PaymentMethodRequest {

    @ApiModelProperty(value = "The payment method type", required = true)
    PaymentMethodTypeEnum getPaymentMethodType();
}
