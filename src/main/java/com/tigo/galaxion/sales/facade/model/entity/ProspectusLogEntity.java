package com.tigo.galaxion.sales.facade.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "prospectus_log")
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProspectusLogEntity {
    @EmbeddedId
    private ProspectusLogId prospectusLogId;

    @Column(name = "user_id", nullable = true)
    private String user_id;

    @Column(name = "user_name", nullable = true)
    private String user_name;

    @Column(name = "ip_equipment", nullable = true)
    private String ip_equipment;

    @Column(name = "sales_channel", nullable = true)
    private String sales_channel;

    @Column(name = "authentication_type", nullable = true)
    private String authentication_type;

    @Column(name = "transaction", nullable = true)
    private String transaction;

    @Column(name = "created_at", nullable = true)
    private LocalDateTime createdAt;
}