package com.tigo.galaxion.sales.facade.connector.credit.score;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;
import com.tigo.galaxion.sales.facade.connector.credit.score.domain.problem.CreditScoreProblem;
import org.zalando.problem.ThrowableProblem;

public class CreditScoreErrorDecoder extends BaseProblemErrorDecoder {

    public CreditScoreErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "credit-scores-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        return new CreditScoreProblem(throwableProblem);
    }

}
