package com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.CopperCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.DtHCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.FoCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.GponCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.GsM3GCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.HfCNetworkCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.LtECoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.PrepaidPaymentCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.RitelCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.ThirdPartyCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.WimaxCoverage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FeasibilityServicesResponse {

    @JsonProperty("addressCode")
    private String addressCode;

    @JsonProperty("highRiskZone")
    private String highRiskZone;

    @JsonProperty("highRiskZoneStatus")
    private String highRiskZoneStatus;

    @JsonProperty("requestCode")
    private String requestCode;

    @JsonProperty("gponCoverage")
    private GponCoverage gponCoverage;

    @JsonProperty("hfCNetworkCoverage")
    private HfCNetworkCoverage hfCNetworkCoverage;

    @JsonProperty("foCoverage")
    private FoCoverage foCoverage;

    @JsonProperty("wimaxCoverage")
    private WimaxCoverage wimaxCoverage; 

    @JsonProperty("copperCoverage")
    private CopperCoverage copperCoverage;

    @JsonProperty("ltECoverage")
    private LtECoverage ltECoverage;


    @JsonProperty("gsM3GCoverage")
    private GsM3GCoverage gsM3GCoverage;

    @JsonProperty("dtHCoverage")
    private DtHCoverage dtHCoverage;

    @JsonProperty("thirdPartyCoverage")
    private ThirdPartyCoverage thirdPartyCoverage;

    @JsonProperty("ritelCoverage")
    private RitelCoverage ritelCoverage;

    @JsonProperty("prepaidPaymentCoverage")
    private PrepaidPaymentCoverage prepaidPaymentCoverage;
 
}
