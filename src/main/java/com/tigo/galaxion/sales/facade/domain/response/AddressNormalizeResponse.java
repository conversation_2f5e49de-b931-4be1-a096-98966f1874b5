package com.tigo.galaxion.sales.facade.domain.response;

import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@Getter
@ToString
@Builder
@NoArgsConstructor
public class AddressNormalizeResponse {
    @ApiModelProperty(value = "Natural Address", required = true, example = "CR 43 A")
    @Size(max=320)
    @JsonProperty("naturalAddress")
    private String naturalAddress;
    @Valid
    @JsonProperty("gisCommonInfoDir")
    AddressNormalizeGisCommonInfoDir gisCommonInfoDir;
}
