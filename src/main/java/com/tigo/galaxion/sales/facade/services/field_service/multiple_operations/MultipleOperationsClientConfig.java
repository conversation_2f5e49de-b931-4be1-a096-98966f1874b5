package com.tigo.galaxion.sales.facade.services.field_service.multiple_operations;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;

import com.tigo.galaxion.sales.facade.services.field_service.BaseClientConfig;

@Configuration
public class MultipleOperationsClientConfig extends BaseClientConfig {
  @Value("${environment.url.multiple-operations-service}")
  private String serviceURL;

  @Bean(name = "marshallerMultipleOperations")
  public Jaxb2Marshaller marshallerMultipleOperations() {
    Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
    marshaller.setContextPath("com.tigo.galaxion.sales.facade.soap.multiple_operations");
    return marshaller;
  }

  @Bean(name = "multipleOperationsClient")
  public MultipleOperationsClient multipleOperationsClient(Jaxb2Marshaller marshallerMultipleOperations) {
    MultipleOperationsClient client = new MultipleOperationsClient();

    WebServiceTemplate webServiceTemplate = createWebServiceTemplate(serviceURL, marshallerMultipleOperations);

    client.setDefaultUri(serviceURL);
    client.setWebServiceTemplate(webServiceTemplate);

    return client;
  }

}
