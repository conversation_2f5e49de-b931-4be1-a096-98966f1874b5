package com.tigo.galaxion.sales.facade.connector.contact.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Singular;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ContactV2Request {

    private String firstName;
    private String lastName;
    @Singular
    private List<EmailV2Request> emails = new ArrayList<>();
    @Singular
    private List<PhoneNumberV2Request> phoneNumbers = new ArrayList<>();
    @Singular
    private List<AddressV2Request> addresses = new ArrayList<>();
    private String accountId;
    private String contactType;
}
