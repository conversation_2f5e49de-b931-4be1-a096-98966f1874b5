package com.tigo.galaxion.sales.facade.connector.credit.score;

import com.tigo.galaxion.sales.facade.connector.credit.score.domain.request.UpdateCreditScoreRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "credit-scores-service", url = "${environment.url.credit-scores-service}", configuration = CreditScoreErrorDecoder.class)
public interface CreditScoreClient {

    @PutMapping("/api/v1/credit-scores")
    void updateCreditScore(@RequestBody UpdateCreditScoreRequest request);
}
