package com.tigo.galaxion.sales.facade.services.field_service.time_interval;

import java.text.ParseException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.UUID;

import javax.xml.datatype.DatatypeConfigurationException;

import com.tigo.galaxion.sales.facade.soap.time_interval.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ws.client.WebServiceIOException;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;

import com.tigo.galaxion.sales.facade.domain.request.FieldServiceSlotsRequest;

@Slf4j
public class TimeIntervalClient extends WebServiceGatewaySupport {

    @Value("${environment.time-interval.number}")
    private int number;

    @Value("${environment.time-interval.area}")
    private String area;

    @Value("${environment.time-interval.district}")
    private String district;

    @Value("${environment.time-interval.priority}")
    private int priority;

    @Value("${environment.time-interval.countryid}")
    private String countryId;

    private static final String PROFILE = "TC_PA_LD_800-1700";

    public TimeIntervalResponse getTimeIntervals(FieldServiceSlotsRequest data)
            throws DatatypeConfigurationException, ParseException {
        log.info("Iniciando obtención de intervalos de tiempo para request: {}", data);

        Task task = new Task();
        Region region = new Region();
        Period period = new Period();

        log.info("Inicializando Task con datos básicos.");
        task.setCallID(data.getCallId() != null ? data.getCallId() : UUID.randomUUID().toString());
        task.setNumber(number);
        task.setArea(data.getArea() != null ? data.getArea() : area);
        task.setDistrict(data.getDistrict() != null ? data.getDistrict() : district);
        task.setPriority(data.getPriority() != null ? data.getPriority() : priority);
        task.setCountryID(data.getCountryId() != null ? data.getCountryId() : countryId);

        log.info("Seteando tipos de tarea: category={} type={}", data.getTaskTypeCategory(), data.getTaskType());
        task.setTaskTypeCategory(data.getTaskTypeCategory());
        task.setTaskType(data.getTaskType());

        log.info("Configurando fechas y localización para la tarea.");
        task.setEarlyStart(formatDateTime(data.getStartDate()));
        task.setDueDate(formatDateTime(data.getEndDate()));
        task.setMCBillingAccountInfo(data.getAddress());
        task.setLatitude(data.getLatitude());
        task.setLongitude(data.getLongitude());

        log.info("Seteando datos de ciudad y fecha de apertura.");
        task.setCity(data.getCity() != null ? data.getCity() : "");
        task.setOpenDate(data.getOpenDate() != null ? data.getOpenDate() : "");

        log.info("Seteando región y calle: region={} street={}", data.getRegion(), data.getStreet());
        region.setName(data.getRegion() != null ? data.getRegion() : "");
        task.setRegion(region);
        task.setStreet(data.getStreet());

        log.info("Creando objeto ExtendedGetAppointment y seteando valores.");
        ExtendedGetAppointment extendedGetAppointment = new ExtendedGetAppointment();
        extendedGetAppointment.setProfile(PROFILE);
        extendedGetAppointment.setTask(task);

        period.setStart(null);
        period.setFinish(null);
        extendedGetAppointment.setPeriod(period);

        extendedGetAppointment.setMessageError("");

        TimeIntervalRequest timeIntervalRequest = new TimeIntervalRequest();
        timeIntervalRequest.setExtendedGetAppointment(extendedGetAppointment);

        log.info("Llamando a servicio externo para obtener los intervalos de tiempo...");
        try {
            TimeIntervalResponse response = (TimeIntervalResponse) getWebServiceTemplate().marshalSendAndReceive(timeIntervalRequest);
            log.info("Respuesta recibida del servicio con éxito: {}", response.getErrorMessage());
            return response;
        } catch (WebServiceIOException e) {
            log.info("Error de transporte SOAP: {}", e.getMessage(), e);
            TimeIntervalResponse errorResponse = new TimeIntervalResponse();
            ExtendedGetAppointment errorAppointment = new ExtendedGetAppointment();
            errorAppointment.setMessageError("SOAP Transport Error: " + e.getMessage());
            errorResponse.setErrorMessage(errorAppointment.getMessageError());
            return errorResponse;
        } catch (Exception e) {
            log.info("Error inesperado al obtener intervalos de tiempo: {}", e.getMessage(), e);
            TimeIntervalResponse errorResponse = new TimeIntervalResponse();
            ExtendedGetAppointment errorAppointment = new ExtendedGetAppointment();
            errorAppointment.setMessageError("Unhandled Error: " + e.getMessage());
            errorResponse.setErrorMessage(errorAppointment.getMessageError());
            return errorResponse;
        }
    }

    String formatDateTime(String date) {
        if (date == null || date.isEmpty()) {
            return null;
        }

        try {
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(date);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssX");
            return zonedDateTime.format(formatter);
        } catch (DateTimeParseException e) {
            logger.info("Error parsing date string: " + date + " - " + e.getMessage());
            return null;
        }
    }
}
