package com.tigo.galaxion.sales.facade.connector.alfresco;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.CommitmentDurationEnum;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AddOnResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.BaseOfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.DiscountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.OfferResponse;
import com.tigo.galaxion.sales.facade.connector.catalog.domain.response.TariffPlansResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Address;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Setter
@Getter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class MetadataFormato42 {

    private final static String ID_FORMATO = "42";
    private final static String ID_VERSION = "1.1";

    private String idFormato;
    private String idVersion;
    private String valorCB;
    private String renovacion;
    private String telefonia;
    private String internet;
    @JsonProperty("Television")
    private String Television;
    private String otros;
    private String fechaActivacion;
    private String nombres;
    private String mail;
    private String telefono;
    private String direccionserv;
    private String estrato;
    private String departamento;
    private String ciudad;
    private String dirSuscriptor;
    private String planTelefonia;
    @JsonProperty("VlrPlanTO")
    private String VlrPlanTO;
    private String serviciosAdicTO;
    private String planTelevision;
    @JsonProperty("VlrPlanTV")
    private String VlrPlanTV;
    private String serviciosAdicTV;
    @JsonProperty("DecosAd")
    private String DecosAd;
    private String planBandaAncha;
    @JsonProperty("VlrPlanBA")
    private String VlrPlanBA;
    private String velocidad;
    private String cantidadExtensorWifi;
    @JsonProperty("ObservacionInternet")
    private String ObservacionInternet;
    private String permanenciaProducto1;
    private String permanenciaProducto2;
    private String permanenciaProducto3;
    private String valorMinimo;
    private String costoReconexion;
    private String permanenciamin;
    @JsonProperty("Vlrconexion")
    private String Vlrconexion;
    private String descuentoconexion;
    private String iniciaPermanencia;
    private String finPermanencia;
    private String mes1;
    private String mes2;
    private String mes3;
    private String mes4;
    private String mes5;
    private String mes6;
    private String mes12;
    private String mes11;
    private String mes10;
    private String mes9;
    private String mes8;
    private String mes7;
    private String tipoDocumento;
    private String numeroDocumento;
    private String fecha;
    private String firma;
    private String productosPaquete;
    private String serviciosAdicInternet;
    private String observacionesTo;
    private String cantidadExtensorWifiMesh;
    private String observacionesTv;

    public MetadataFormato42(ProspectLeadResponse prospectLead, CartResponse cart, Double penalty) {

        List<Address> filteredAddresses = new ArrayList<>();

        for (Address address : prospectLead.getAddress()) {
            if (address.getAddressType() == 1) {
                filteredAddresses.add(address);
            }
        }

        if (filteredAddresses.size() > 1) {
            throw new IllegalArgumentException("Filtered addresses contain more than one addressType=1");
        }

        if (filteredAddresses.size() == 0) {
            throw new NoSuchElementException("Filtered addresses doesn't contain any addressType=1");
        }

        Address direccionInstalacion = filteredAddresses.get(0);
        LocalDate fechaActual = LocalDate.now();
        String fechaActivacion = prospectLead.getCustomer().getAppointmentStartDate() != null
                ? prospectLead.getCustomer().getAppointmentStartDate()
                : "";
        fechaActivacion = fechaActivacion.substring(0, fechaActivacion.indexOf("T"));

        setIdFormato(ID_FORMATO);
        setIdVersion(ID_VERSION);
        setValorCB(calculateValorCB(cart.getOffers()));
        // setValorCB(Long.toString(cart.getAmountVatIncluded().getRecurringAmount().getAmount()));
        setRenovacion("NO");
        setTelefonia("NO");
        setInternet(this.hasInternet(cart.getOffers()));
        setTelevision(this.hasTelevision(cart.getOffers()));
        setOtros(this.concatAddons(cart.getOffers(), "ADDONS_TV"));

        setFechaActivacion(fechaActivacion);
        setNombres(prospectLead.getCustomer().getNames().concat(" ").concat(prospectLead.getCustomer().getLastName()));
        setMail(prospectLead.getCustomer().getContactEmail());
        setTelefono(prospectLead.getCustomer().getContactPhone());
        setDireccionserv(direccionInstalacion.getAddress());
        setEstrato(Integer.toString(direccionInstalacion.getStratum()));
        setDepartamento(direccionInstalacion.getDepartment());
        setCiudad(direccionInstalacion.getMunicipality());
        setDirSuscriptor(direccionInstalacion.getAddress());
        setPlanTelefonia("");
        setVlrPlanTO("");
        setServiciosAdicTO("");
        setPlanTelevision(this.findPlanTelevision(cart.getOffers()));
        setVlrPlanTV(this.findVlrPlanTV(cart.getOffers()));
        setServiciosAdicTV(this.concatAddons(cart.getOffers(), "ADDONS_TV"));
        setDecosAd(Integer.toString(this.countAddons(cart.getOffers(), "ADDONS_EQUIPMENT")));
        setPlanBandaAncha(this.findPlanBandaAncha(cart.getOffers()));
        setVlrPlanBA(this.findVlrPlanBA(cart.getOffers()));
        setVelocidad("");
        setCantidadExtensorWifi("");
        setObservacionInternet("");
        setPermanenciaProducto1(cart.getOffers().size() >= 1 ? "12" : "");
        setPermanenciaProducto2(cart.getOffers().size() >= 2 ? "12" : "");
        setPermanenciaProducto3("");
        setValorMinimo(this.findValorMinimo(cart.getOffers()));
        setCostoReconexion("");
        setPermanenciamin(this.requiresPermanenciamin(cart.getOffers()));
        setVlrconexion(this.calculateVlrconexion(cart));
        setDescuentoconexion(this.findDescuentoconexion(cart.getOffers()));
        setIniciaPermanencia(fechaActual.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        setFinPermanencia(fechaActual.plusYears(1).format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        
        setMes1(findMesN(cart, 12, penalty));
        setMes2(findMesN(cart, 11, penalty));
        setMes3(findMesN(cart, 10, penalty));
        setMes4(findMesN(cart, 9, penalty));
        setMes5(findMesN(cart, 8, penalty));
        setMes6(findMesN(cart, 7, penalty));
        setMes7(findMesN(cart, 6, penalty));
        setMes8(findMesN(cart, 5, penalty));
        setMes9(findMesN(cart, 4, penalty));
        setMes10(findMesN(cart, 3, penalty));
        setMes11(findMesN(cart, 2, penalty));
        setMes12(findMesN(cart, 1, penalty));

        setTipoDocumento(this.getTypeDocument(prospectLead.getCustomer().getDocumentType()));
        setNumeroDocumento(prospectLead.getCustomer().getDocumentId());
        setFecha(fechaActual.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        setFirma(prospectLead.getCustomer().getCallId() != null ? prospectLead.getCustomer().getCallId() : "");
        setProductosPaquete(this.isProductosPaquete(cart.getOffers()));
        setServiciosAdicInternet("");
        setObservacionesTo("");
        setCantidadExtensorWifiMesh("");
        setObservacionesTv("");

    }

    public String toMetadataString() {
        // String idFormato42 =
        // "{\"idFormato\":42,\"idVersion\":\"1.1\",\"valorCB\":\"120.000\",\"renovacion\":\"NO\",\"telefonia\":\"NO\",\"internet\":\"SI\",\"Television\":\"SI\",\"otros\":\"Satélite\",\"fechaActivacion\":\"03/12/2021\",\"nombres\":\"Nicolas
        // Wagner\",\"mail\":\"<EMAIL>\",\"telefono\":\"321300012\",\"direccionserv\":\"Calle
        // 123 # 4 56\",\"estrato\":\"4\",\"departamento\":\"Bogotá
        // D.C\",\"ciudad\":\"Bogotá D.C\",\"dirSuscriptor\":\"Calle 123 # 4
        // 56\",\"planTelefonia\":\"El más
        // alto\",\"VlrPlanTO\":\"50.000\",\"serviciosAdicTO\":\"5.000\",\"planTelevision\":\"Ultra\",\"VlrPlanTV\":\"40.000\",\"serviciosAdicTV\":\"NO\",\"DecosAd\":\"1\",\"planBandaAncha\":\"UltramegaSatelital\",\"VlrPlanBA\":\"30.000\",\"velocidad\":\"ADSL
        // Ultra\",\"cantidadExtensorWifi\":\"2\",\"ObservacionInternet\":\"Ninguna\",\"permanenciaProducto1\":\"5
        // años\",\"permanenciaProducto2\":\"5 años\",\"permanenciaProducto3\":\"5
        // años\",\"valorMinimo\":\"120.000\",\"costoReconexion\":\"0\",\"permanenciamin\":\"SI\",\"Vlrconexion\":\"0\",\"descuentoconexion\":\"0\",\"iniciaPermanencia\":\"01/12/2021\",\"finPermanencia\":\"01/12/2026\",\"mes1\":\"10000\",\"mes2\":\"10000\",\"mes3\":\"10000\",\"mes4\":\"10000\",\"mes5\":\"10000\",\"mes6\":\"10000\",\"mes12\":\"10000\",\"mes11\":\"10000\",\"mes10\":\"10000\",\"mes9\":\"10000\",\"mes8\":\"10000\",\"mes7\":\"10000\",\"tipoDocumento\":\"Cédula
        // Ciudadanía\",\"numeroDocumento\":\"45000014\",\"fecha\":\"01/12/2021\",\"firma\":\"https://upload.wikimedia.org/wikipedia/commons/8/8b/Firma_de_Harold.jpg\",\"productosPaquete\":
        // \"SI\",\"serviciosAdicInternet\":\"\",\"observacionesTo\":\"Alguna
        // observación\",\"cantidadExtensorWifiMesh\":\"\",\"observacionesTv\":\"Otra
        // Obervación\" }";

        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.valueToTree(this);

        return jsonNode.toString();

        // return idFormato42;

    }

    private String hasInternet(List<OfferResponse> os) {
        for (OfferResponse item : os) {
            if ("HFC".equals(item.getBaseOffer().getServiceGroup())) {
                return "SI";
            }
        }
        return "NO";
    }

    private String hasTelevision(List<OfferResponse> os) {
        for (OfferResponse item : os) {
            if ("TV".equals(item.getBaseOffer().getServiceGroup())) {
                return "SI";
            }
        }
        return "NO";
    }

    private String concatAddons(List<OfferResponse> os, String mItemGroup) {
        List<String> addonsList = new ArrayList<String>();
        for (OfferResponse o : os) {
            for (Iterator<AddOnResponse> it = o.getAddOns().iterator(); it.hasNext();) {
                AddOnResponse a = it.next();
                if (mItemGroup.equals(a.getItemGroup())) {
                    addonsList.add(a.getDescription().toUpperCase());
                }
            }
        }
        return String.join(",", addonsList);
    }

    private Integer countAddons(List<OfferResponse> os, String mItemGroup) {

        List<String> addonsList = new ArrayList<String>();

        for (OfferResponse o : os) {

            for (Iterator<AddOnResponse> it = o.getAddOns().iterator(); it.hasNext();) {

                AddOnResponse a = it.next();
                if (mItemGroup.equals(a.getItemGroup())) {
                    addonsList.add(a.getDescription().toUpperCase());
                }
            }
        }
        return addonsList.size();
    }

    private String findPlanTelevision(List<OfferResponse> os) {
        for (OfferResponse o : os) {
            BaseOfferResponse base = o.getBaseOffer();
            if ("TV".equals(base.getServiceGroup())) {
                return base.getCatalogCode();
            }
        }
        return "";
    }

    private String findVlrPlanTV(List<OfferResponse> os) {
        for (OfferResponse o : os) {
            BaseOfferResponse base = o.getBaseOffer();
            if ("TV".equals(base.getServiceGroup())) {
                return Long.toString(base.getAmountVatIncluded().getRecurringAmount().getAmount());
            }
        }
        return "";
    }

    private String findPlanBandaAncha(List<OfferResponse> os) {
        for (OfferResponse o : os) {
            BaseOfferResponse base = o.getBaseOffer();
            if ("HFC".equals(base.getServiceGroup())) {
                return base.getCatalogCode();
            }
        }
        return "";
    }

    private String findVlrPlanBA(List<OfferResponse> os) {
        for (OfferResponse o : os) {
            BaseOfferResponse base = o.getBaseOffer();
            if ("HFC".equals(base.getServiceGroup())) {
                return Long.toString(base.getAmountVatIncluded().getRecurringAmount().getAmount());
            }
        }
        return "";
    }

    private String findValorMinimo(List<OfferResponse> os) {
        for (OfferResponse o : os) {
            BaseOfferResponse base = o.getBaseOffer();
            return Long.toString(base.getAmountVatIncluded().getOneOffAmount());
        }
        return "";
    }

    private String requiresPermanenciamin(List<OfferResponse> os) {
        for (OfferResponse o : os) {
            BaseOfferResponse base = o.getBaseOffer();
            if (base.getCommitmentDuration() != null) {
                return "SI";
            }
        }
        return "NO";
    }

    private String calculateVlrconexion(CartResponse c) {
        // return Long.toString(c.getAmountVatIncluded().getOneOffAmount());
        if (c.getOffers().size() > 1) {
            return Long.toString(c.getAmountVatIncluded().getOneOffAmount() / 2);
        } else {
            return Long.toString(c.getAmountVatIncluded().getOneOffAmount());
        }
    }

    private String findDescuentoconexion(List<OfferResponse> os) {
        for (OfferResponse o : os) {

            Long amount = o.getAmountVatIncluded().getRecurringAmount().getAmount();
            Long ufAmount = o.getAmountVatIncluded().getUpFrontAmount();

            return Long.toString(amount - ufAmount);

        }
        return "";
    }

    private String findMesN(CartResponse c, double mes, Double penalty) {

        for (OfferResponse o : c.getOffers()) {
            BaseOfferResponse base = o.getBaseOffer();

            if (base.getCommitmentDuration() != CommitmentDurationEnum.ONE) {
                return String.format("%.2f", (double) (penalty * (mes / 12.0)));
            }
        }
        return "";
    }

    private String isProductosPaquete(List<OfferResponse> os) {
        for (OfferResponse o : os) {
            List<DiscountResponse> ds = o.getDiscounts();
            for (DiscountResponse d : ds) {
                if (d.getCatalogCode().indexOf("DISCOUNT") != -1
                        && d.getCatalogCode().indexOf("COMBO") != -1) {
                    return "SI";
                }
            }
        }
        return "NO";
    }

    private String getTypeDocument(String documentType) {
        String typeDocument = "";
        switch (documentType) {
            case "CC":
            case "Cédula de Ciudadanía":
                typeDocument = "Cédula de Ciudadanía";
                break;
            case "CE":
            case "Cédula de Extranjería":
                typeDocument = "Cédula de Extranjería";
                break;
            case "Numero de Identificacion Tributaria":
            case "NIT":
                typeDocument = "NIT";
                break;
            case "Pasaporte":
                typeDocument = documentType;
                break;
            default:
                typeDocument = "Do not exists this Document Type";
        }
        return typeDocument;
    }

    private String calculateValorCB(List<OfferResponse> os) {

        Long amount = 0L;

        for (OfferResponse o : os) {
            amount = amount + o.getBaseOffer().getAmountVatIncluded().getRecurringAmount().getAmount();
        }
        return Long.toString(amount);
    }

}
