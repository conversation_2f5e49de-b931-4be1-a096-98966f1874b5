package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class CreateChargeV2Request {
	//enum
	private String advanceArrears;
	//enum
	private String billingType;
	private String catalogCode;
	private Integer coolingOffPeriod;
	private String description;
	private List<CreateNetworkElementV2Request> networkElements;
	private Integer orderId;
	@NotNull
	private Integer price;
	private String  pricePlanCatalogCode;
}
