package com.tigo.galaxion.sales.facade.connector.fraudmanagement;

import com.tigo.galaxion.sales.facade.config.FeignRetryConfig;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.FraudsRequestBody;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.response.FraudsResponseBody;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "tigo-fraud-management-service", url = "${environment.url.tigo-fraud-management-service}", configuration = {
		FeignRetryConfig.class, FraudManagementErrorDecoder.class })
public interface FraudManagementClient {
    @PostMapping("/fraud")
    FraudsResponseBody frauds(@RequestBody FraudsRequestBody request);
}
