package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoAddressResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TigoAddressResponseMapper {

    public static TigoAddressResponse buildTigoAddressResponse(AddressResponse request) {
        return TigoAddressResponse.builder()
                                  .area(request.getArea())
                                  .town(request.getTown())
                                  .streetName(request.getStreet())
                                  .streetNumber(request.getStreetNumber())
                                  .postCode(request.getCode())
                                  .build();
    }
}
