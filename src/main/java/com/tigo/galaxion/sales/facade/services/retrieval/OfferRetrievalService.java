package com.tigo.galaxion.sales.facade.services.retrieval;

import com.tigo.galaxion.sales.facade.model.entity.OfferEntity;
import com.tigo.galaxion.sales.facade.model.repository.OfferRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class OfferRetrievalService {

    private final OfferRepository offerRepository;

    public Optional<OfferEntity> get(Long offerId) {
        return offerRepository.findByOfferId(offerId);
    }
}
