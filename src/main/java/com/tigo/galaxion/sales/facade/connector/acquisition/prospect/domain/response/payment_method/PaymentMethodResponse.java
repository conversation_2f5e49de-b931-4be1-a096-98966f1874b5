package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.payment_method;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.PaymentMethodTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "paymentMethodType")
@JsonSubTypes({
        @JsonSubTypes.Type(value = CardPaymentMethodResponse.class),
        @JsonSubTypes.Type(value = DirectDebitPaymentMethodResponse.class),
        @JsonSubTypes.Type(value = ManualPaymentMethodResponse.class),
        @JsonSubTypes.Type(value = AnonymousCardPaymentMethodResponse.class)
        // For other payment method insert case here
})
@SuperBuilder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public abstract class PaymentMethodResponse {

    private String prospectReference;
    private PaymentTypeEnum type;

    public abstract PaymentMethodTypeEnum getPaymentMethodType();
}
