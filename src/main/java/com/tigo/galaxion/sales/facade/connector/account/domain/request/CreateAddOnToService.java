package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateAddOnToService {
	private String catalogCode;
	private List<CreateChargeV2Request> charges;
	private CreateContractV2Request contract;
	private List<CreateDiscountV2Request> discounts;
	private List<CreateAddonHandsetV2Request> handsets;
	private List<CreateNetworkElementV2Request> networkElements;
	private Integer orderId;
	private String orderReference;
	private List<CreateOtherEquipmentV2Request> otherEquipments;
	private CreateResidentialGatewayV2Request residentialGateway;
	private CreateAddonSimCardV2Request simCard;
	private List<CreateUsageQuotaV2Request> usageQuotas;
}
