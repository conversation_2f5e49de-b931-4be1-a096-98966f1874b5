package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import java.util.List;

import lombok.Data;

@Data
public class CreateAddonV3Request {
	private String catalogCode;
	private List<CreateChargeV3Request> charges;
	private CreateContractV3Request contract;
	private List<CreateDiscountV3Request> discounts;
	private List<Object> equipments;
	private List<CreateNetworkElementV3Request> networkElements;
	private String orderReference;
	private List<CreateUsageQuotaV3Request> usageQuotas;
}
