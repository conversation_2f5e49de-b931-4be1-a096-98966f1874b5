package com.tigo.galaxion.sales.facade.services.retrieval;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.problem.AcquisitionProspectContactNotFoundProblem;
import com.tigo.galaxion.sales.facade.connector.address.AddressClient;
import com.tigo.galaxion.sales.facade.connector.contact.ContactClientCached;
import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import com.tigo.galaxion.sales.facade.domain.response.TigoContactResponse;
import com.tigo.galaxion.sales.facade.mapper.dto.TigoPermissionResponseMapper;
import com.tigo.galaxion.sales.facade.model.repository.ContactAddressRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.tigo.galaxion.sales.facade.connector.contact.ContactClientCached.ALLOW_FOTS_CONTACT;
import static com.tigo.galaxion.sales.facade.connector.contact.ContactClientCached.NO_LONGER_CUSTOMER;
import static com.tigo.galaxion.sales.facade.mapper.dto.TigoAddressResponseMapper.buildTigoAddressResponse;

@Service
@RequiredArgsConstructor
public class ContactRetrievalService {

    private final AcquisitionProspectClient acquisitionProspectClient;
    private final ContactClientCached contactClientCached;
    private final AddressClient addressClient;

    private final ContactAddressRepository contactAddressRepository;
    private final ContactIdentityDocumentRetrievalService contactIdentityDocumentRetrievalService;

    @Transactional(readOnly = true)
    public TigoContactResponse getContactResponseForAcquisition(String prospectReference) {
        try {
            var contactResponse = acquisitionProspectClient.getContact(prospectReference);
            retrieveAndSetAddresses(prospectReference, contactResponse);
            filterPermissions(contactResponse);
            retrieveAndSetIdentityDocument(prospectReference, contactResponse);
            return contactResponse;
        } catch (AcquisitionProspectContactNotFoundProblem problem) {
            return fillContactNotFound();
        }
    }

    private void retrieveAndSetAddresses(String prospectReference, TigoContactResponse contactResponse) {
        var optionalBillingContactAddressEntity = contactAddressRepository.findByContactAddressId_ReferenceAndContactAddressId_Type(prospectReference, AddressTypeEnum.BILLING);
        var optionalDeliveryContactAddressEntity = contactAddressRepository.findByContactAddressId_ReferenceAndContactAddressId_Type(prospectReference, AddressTypeEnum.DELIVERY);
        if (optionalBillingContactAddressEntity.isPresent()) {
            var billingAddress = addressClient.getAddress(optionalBillingContactAddressEntity.get().getAddressId());
            contactResponse.setBillingAddress(buildTigoAddressResponse(billingAddress));
        }
        if (optionalDeliveryContactAddressEntity.isPresent()) {
            var deliveryAddress = addressClient.getAddress(optionalDeliveryContactAddressEntity.get().getAddressId());
            contactResponse.setDeliveryAddress(buildTigoAddressResponse(deliveryAddress));
        }
    }

    private TigoContactResponse fillContactNotFound() {
        var permissions = contactClientCached.getPermissions();
        return TigoContactResponse.builder()
                                  .permissions(TigoPermissionResponseMapper.buildTigoAcquisitionPermissionGroupListResponse(permissions))
                                  .build();
    }

    private void filterPermissions(TigoContactResponse tigoContactResponse) {
        if (tigoContactResponse.getPermissions() != null) {
            tigoContactResponse.getPermissions()
                                .getPermissionGroups()
                                .removeIf(tigoPermissionGroupResponse -> NO_LONGER_CUSTOMER.equals(tigoPermissionGroupResponse.getPermissionGroup()));
            tigoContactResponse.getPermissions()
                                .getPermissionGroups()
                                .forEach(tigoPermissionGroupResponse -> tigoPermissionGroupResponse.getPermissions()
                                                                                                     .removeIf(tigoPermissionResponse -> ALLOW_FOTS_CONTACT.equals(tigoPermissionResponse.getPermission())));
        }
    }

    private void retrieveAndSetIdentityDocument(String prospectReference, TigoContactResponse contact) {
        var identityDocument = contactIdentityDocumentRetrievalService.getContactIdentityDocumentResponse(prospectReference);
        contact.setIdentityDocument(identityDocument);
    }
}
