package com.tigo.galaxion.sales.facade.connector.georeference;
import com.tigo.galaxion.sales.facade.config.FeignRetryConfig;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.request.GeoreferenceRequestBody;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.response.GeoreferenceResponseBody;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "tigo-georeference-service",
             url = "${environment.url.tigo-georeference-service}",
            configuration = { FeignRetryConfig.class, GeoreferenceErrorDecoder.class }
             )
public interface GeoreferenceClient {
    @PostMapping("/georeference")
    GeoreferenceResponseBody normalize(@RequestBody GeoreferenceRequestBody request);
}

