package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@Builder
@ToString
@AllArgsConstructor
@Getter
@EqualsAndHashCode(of = "code")
public class TigoDiscountResponse {

    @ApiModelProperty(value = "The discount's code", required = true, example = "Combi_Order_Mobile_Discount")
    private String code;

    @ApiModelProperty(value = "The discount's description", required = true, example = "Mobile with Broadband combo discount")
    private String description;

    @ApiModelProperty(value = "The discount's billing type", required = true, example = "RECURRING")
    private String billingType;

    @ApiModelProperty(value = "The discount's catalog item type", required = true, example = "OFFER")
    private String discountItemType;

    @ApiModelProperty(value = "The discount's value", required = true, example = "1000")
    private Long value;

    @ApiModelProperty(value = "The reason for the discount", required = true, example = "Customer is nice")
    private String reason;

    @ApiModelProperty(value = "The discount's value", required = true, example = "770")
    private Long vatExcludedAmount;

}
