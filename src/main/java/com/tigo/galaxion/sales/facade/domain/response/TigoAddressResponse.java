package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoAddressResponse {

    @NotBlank
    @ApiModelProperty(value = "The aera", example = "GOZO", required = true)
    private String area;

    @NotBlank
    @ApiModelProperty(value = "The Town", example = "Lyon", required = true)
    private String town;

    @NotBlank
    @ApiModelProperty(value = "The street name", example = "rue de la hess", required = true)
    private String streetName;

    @NotBlank
    @ApiModelProperty(value = "The street number")
    private String streetNumber;

    @ApiModelProperty(value = "88999")
    private String postCode;
}
