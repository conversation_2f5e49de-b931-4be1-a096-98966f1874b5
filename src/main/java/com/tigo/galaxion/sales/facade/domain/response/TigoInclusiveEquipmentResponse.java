package com.tigo.galaxion.sales.facade.domain.response;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@ApiModel
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = TigoInclusiveHandsetResponse.class, name = EquipmentTypeEnum.Constant.HANDSET),
        @JsonSubTypes.Type(value = TigoInclusiveTVCpeResponse.class, name = EquipmentTypeEnum.Constant.TV_CPE),
        @JsonSubTypes.Type(value = TigoInclusiveResidentialGatewayResponse.class, name = EquipmentTypeEnum.Constant.RESIDENTIAL_GATEWAY),
        @JsonSubTypes.Type(value = TigoInclusiveMobileBroadbandModemResponse.class, name = EquipmentTypeEnum.Constant.MOBILE_BROADBAND_MODEM),
})
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TigoInclusiveEquipmentResponse {

    @ApiModelProperty(value = "Determines if the equipment is the main equipment of the offer", required = true, example = "true")
    private Boolean main;

    @ApiModelProperty(value = "The handset code", example = "GALC1066S", required = true)
    private String catalogCode;

    @ApiModelProperty(value = "The Dongle color", required = true, example = "BLACK")
    private String color;

    @ApiModelProperty(value = "The Dongle color code", example = "#000000", required = true)
    private String colorCode;

    @ApiModelProperty(value = "The equipment description", example = "My equipment", required = true)
    private String description;

    @ApiModelProperty(value = "The equipment's manufacturer")
    private String manufacturer;

    @ApiModelProperty(value = "The equipment's model", example = "MyPhone", required = true)
    private String model;

    @ApiModelProperty(value = "The equipment's inventory code", example = "BAPMY", required = true)
    private String inventoryCode;

    @ApiModelProperty(value = "The equipment's type", example = "HANDSET", required = true)
    private EquipmentTypeEnum type;

}
