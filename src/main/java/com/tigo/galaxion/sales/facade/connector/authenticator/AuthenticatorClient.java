package com.tigo.galaxion.sales.facade.connector.authenticator;
import com.tigo.galaxion.sales.facade.connector.authenticator.domain.response.AuthenticatorResponse;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(value = "${environment.auth.name}",
             url = "${environment.url.auth-adapter}",
            configuration = { AuthenticatorErrorDecoder.class }
             )
public interface AuthenticatorClient {
    @GetMapping("/oauth/token")
    AuthenticatorResponse authentication(@RequestHeader Map<String,String> headers);
}

