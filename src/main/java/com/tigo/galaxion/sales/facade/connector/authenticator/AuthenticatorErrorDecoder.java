package com.tigo.galaxion.sales.facade.connector.authenticator;

import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;

import org.apache.commons.lang3.StringUtils;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;
import org.zalando.problem.ThrowableProblem;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.authenticator.domain.problem.AuthenticatorProblem;
import com.tigo.galaxion.sales.facade.connector.authenticator.domain.response.AuthenticatorErrorResponse;

@Slf4j
@RequiredArgsConstructor
public class AuthenticatorErrorDecoder implements ErrorDecoder {

    private static final String TITLE = "ms-oauth-adapter-error";

    @Override
    public Exception decode(String methodKey, Response response) {
        throw new AuthenticatorProblem(getThrowableProblem(response));
    }

    private ThrowableProblem getThrowableProblem(Response response) {
        if (StringUtils.isNotBlank(response.body().toString())) {
            log.error(response.body().toString());
        }

        AuthenticatorErrorResponse message = null;
        try (InputStream bodyIs = response.body().asInputStream()) {
            ObjectMapper mapper = new ObjectMapper();
            message = mapper.readValue(bodyIs, AuthenticatorErrorResponse.class);
            
            var problemBuilder = Problem
                .builder()
                .withDetail("Error from Authenticator Connector. " + message.getBackendErrorCode().toString()) 
                .withStatus(Status.valueOf(response.status()))
                .withTitle(TITLE);
            return problemBuilder.build();

        } catch (IOException e) {
            var problemBuilder = Problem
                .builder()
                .withDetail("Error from Authenticator Connector. " + e.getMessage()) 
                .withStatus(Status.valueOf(response.status()))
                .withTitle(TITLE);
            return problemBuilder.build();
        }

    }
}