package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

@SuperBuilder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AccessoryEquipmentResponse extends EquipmentResponse {

    @Builder.Default
    private List<ServiceDomainResponse> serviceDomains = new ArrayList<>();

}
