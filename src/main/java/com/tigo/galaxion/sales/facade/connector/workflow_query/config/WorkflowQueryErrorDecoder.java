package com.tigo.galaxion.sales.facade.connector.workflow_query.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;       
import com.tigo.galaxion.sales.facade.connector.workflow_query.problem.WorkflowQueryProblem;
       
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.ThrowableProblem;

public class WorkflowQueryErrorDecoder extends BaseProblemErrorDecoder {

    public WorkflowQueryErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "workflow-query-service-error";
    }

    @Override
    protected AbstractThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        return new WorkflowQueryProblem(throwableProblem);
    }
}

