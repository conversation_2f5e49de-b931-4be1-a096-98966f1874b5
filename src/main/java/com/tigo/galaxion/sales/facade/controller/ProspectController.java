package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.domain.response.TigoProspectResponse;
import com.tigo.galaxion.sales.facade.services.ContractSignatureOptionService;
import com.tigo.galaxion.sales.facade.services.retrieval.ProspectRetrievalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.NO_CONTENT;

@Api
@RequiredArgsConstructor
@RestController
public class ProspectController {

    private final ContractSignatureOptionService contractSignatureOptionService;
    private final ProspectRetrievalService prospectRetrievalService;

    @PutMapping("/prospects/{prospect_reference}/contract-signature-option")
    @ApiOperation("Update contract signature option.")
    @ApiResponses(value = {
            @ApiResponse(code = 204, message = "Contract signature option updated."),
    })
    @ResponseStatus(NO_CONTENT)
    public void updateContractSignatureOption(
            @PathVariable("prospect_reference") String prospectReference,
            @RequestParam String contractSignatureOption) {
        contractSignatureOptionService.updateContractSignatureOption(prospectReference, contractSignatureOption);
    }

    @GetMapping("/prospects/{prospect_reference}")
    @ApiOperation("Get prospect.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Prospect details."),
            @ApiResponse(code = 404, message = "Prospect not found (Problem title = 'prospect-not-found')")
    })
    public TigoProspectResponse getProspect(@PathVariable("prospect_reference") String prospectReference) {
        return prospectRetrievalService.getProspect(prospectReference);
    }

}
