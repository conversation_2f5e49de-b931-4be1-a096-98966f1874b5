package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.AcquisitionProspectsAddOfferToCartRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellAddOfferToCartRequest;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferToAcquisitionProspectsCartRequest;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferToCrossSellCartRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AddOfferToCartMapper {

    public static AcquisitionProspectsAddOfferToCartRequest buildAcquisitionProspectsAddOfferToCartRequest(
            AddOfferToAcquisitionProspectsCartRequest data) {

        var apb = AcquisitionProspectsAddOfferToCartRequest.builder()
                .parentOfferId(data.getParentOfferId())
                .catalogOfferCode(data.getCatalogOfferCode())
                .catalogTariffPlanCode(data.getCatalogTariffPlanCode())
                .portInType(data.getPortInType());

        if (data.getInstallationAddressId() != null) {
            apb.installationAddressId(data.getInstallationAddressId());
        }

        return apb.build();
    }

    public static CrossSellAddOfferToCartRequest buildCrossSellAddOfferToCartRequest(
            AddOfferToCrossSellCartRequest addOfferToAcquisitionProspectsCartRequest) {

        return CrossSellAddOfferToCartRequest.builder()
                .parentOfferId(addOfferToAcquisitionProspectsCartRequest.getParentOfferId())
                .catalogOfferCode(addOfferToAcquisitionProspectsCartRequest.getCatalogOfferCode())
                .catalogTariffPlanCode(addOfferToAcquisitionProspectsCartRequest.getCatalogTariffPlanCode())
                .portInType(addOfferToAcquisitionProspectsCartRequest.getPortInType())
                .parentSubscriptionId(addOfferToAcquisitionProspectsCartRequest.getParentSubscriptionId())
                .installationAddressId(addOfferToAcquisitionProspectsCartRequest.getInstallationAddressId())
                .build();
    }
}
