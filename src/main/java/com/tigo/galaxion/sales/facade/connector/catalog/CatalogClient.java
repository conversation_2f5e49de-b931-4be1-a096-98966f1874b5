package com.tigo.galaxion.sales.facade.connector.catalog;

import com.tigo.galaxion.sales.facade.connector.catalog.domain.response.TariffPlansResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(value = "catalog-service", url = "${environment.url.catalog-service}", configuration = CatalogErrorDecoder.class)
public interface CatalogClient {

    @GetMapping("/api/v1/tariff-plans/{tariff_plan_code}")
    TariffPlansResponse getTariffPlans(@PathVariable(name = "tariff_plan_code") String tariffPlanCode);
}
