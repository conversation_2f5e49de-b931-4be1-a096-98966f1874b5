package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class ValidDateProblem extends AbstractThrowableProblem {
    public ValidDateProblem(String message, String type, String start, String end) {
        super(null,
                type + "-date-invalid",
                Status.BAD_REQUEST,
                String.format(message, start, end)
        );
    }
}
