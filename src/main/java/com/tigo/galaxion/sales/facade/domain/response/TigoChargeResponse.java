package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
public class TigoChargeResponse {

    @ApiModelProperty(value = "The charge code", example = "QWERTY")
    private String catalogCode;

    @ApiModelProperty(value = "Current price plan of the charge")
    private TigoPricePlanResponse pricePlan;
}
