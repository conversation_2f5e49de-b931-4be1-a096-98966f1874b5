package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoPermissionGroupListResponse {

    @ApiModelProperty(value = "If the contact allow to be contacted by third parties.", required = true)
    private boolean allowThirdParty;

    @ApiModelProperty(value = "If the contact has marketing profiling enabled.", required = true)
    private Boolean hasMarketingProfiling;

    @Builder.Default
    private List<TigoPermissionGroupResponse> permissionGroups = new ArrayList<>();
}
