package com.tigo.galaxion.sales.facade.connector.notification.domain.request;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ValuesNotification {

    
    @JsonProperty("cliente")
    private String cliente;
    
    @JsonProperty("orderId")
    private String orderId;
    
    @JsonProperty("beginDate")
    private String beginDate;
    
    @JsonProperty("corregimiento")
    private String corregimiento;
    
    @JsonProperty("barrio")
    private String barrio;
    
    @JsonProperty("calle")
    private String calle;
    
    @JsonProperty("casa")
    private String casa;
    
    @JsonProperty("provincia")
    private String provincia;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("url")
    private String url;
    
    @JsonProperty("firstName")
    private String firstName;
    
    @JsonProperty("lastName")
    private String lastName;
    
    
    @JsonProperty("precioRegular")
    private double precioRegular;
    
    @JsonProperty("descuentoPermanente")
    private double descuentoPermanente;
    
    @JsonProperty("pagoMensual")
    private double pagoMensual;
    
    @JsonProperty("pagoAdicional")
    private double pagoAdicional;
    
    @JsonProperty("pagoAdelantado")
    private double pagoAdelantado;
    
    @JsonProperty("nombreOferta")
    private String nombreOferta;
    
    @JsonProperty("descripcionOferta")
    private String descripcionOferta;
    
    @JsonProperty("planes")
    private List<PlansNotification> planes;
    
    @JsonProperty("servicios")
    private List<ServicesNotification> servicios;
    
    @JsonProperty("descuentosTemporales")
    private List<TemporalDiscountsNotification> descuentosTemporales;
    
    @JsonProperty("cargos")
    private List<ChargeNotifications> cargos;
}
