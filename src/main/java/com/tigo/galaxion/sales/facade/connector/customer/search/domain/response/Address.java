package com.tigo.galaxion.sales.facade.connector.customer.search.domain.response;

public record Address(
  String daneCode,
  String data,
  String stratum,
  String source,
  String cityName,
  String cityDaneCode,
  String departmentName,
  String departmentDaneCode,
  String numReports,
  String numberEntities,
  String order,
  String reportedFrom,
  boolean correspondenceType,
  boolean jobOrCommercialType,
  boolean residencyType,
  boolean mainType,
  boolean agencyType,
  boolean branchType,
  boolean establishmentType,
  boolean typeCentralInformacion,
  String type,
  String lastReport
) {

}
