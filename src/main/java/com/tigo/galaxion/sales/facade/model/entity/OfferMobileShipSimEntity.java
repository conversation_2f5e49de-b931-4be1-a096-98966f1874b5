package com.tigo.galaxion.sales.facade.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "offer_mobile_ship_sim")
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OfferMobileShipSimEntity {

    @EmbeddedId
    private OfferId offerId;

    @Column(name = "address_id", nullable = false)
    private Long addressId;
}
