package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.collection.CollectionClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.domain.problem.AccountInCollectionProblem;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class EligibilityService {

    private final CollectionClient collectionClient;
    private final CrossSellClient crossSellClient;

    public void checkCrossSellEligibility(String accountId) {
        checkAccountIsInCollection(accountId);
        crossSellClient.getServiceGroupsEligibility(accountId);
    }

    public void checkAccountIsInCollection(String accountId) {
        if (collectionClient.checkAccountIsInCollection(accountId)) {
            throw new AccountInCollectionProblem(accountId);
        }

    }
}
