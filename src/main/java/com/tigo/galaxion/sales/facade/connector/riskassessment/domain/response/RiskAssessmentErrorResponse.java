package com.tigo.galaxion.sales.facade.connector.riskassessment.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class RiskAssessmentErrorResponse {
    @JsonProperty("code")
    private String code;
    @Valid
    @JsonProperty("message")
    private String message;
    }
