package com.tigo.galaxion.sales.facade.controller;


import java.util.List;

import static org.springframework.http.HttpStatus.CREATED;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;


import com.tigo.galaxion.sales.facade.connector.account.AccountClient;
import com.tigo.galaxion.sales.facade.connector.account.domain.request.CreateAddOnToService;
import com.tigo.galaxion.sales.facade.connector.account.domain.request.CreateInclusiveEquipmentRequest;
import com.tigo.galaxion.sales.facade.connector.account.domain.response.AccountsCategoriesResponseBody;
import com.tigo.galaxion.sales.facade.connector.account.domain.response.EquipmentWithoutAddonV3Response;
import com.tigo.galaxion.sales.facade.services.AccountsService;

import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

@Api
@RestController
@RequiredArgsConstructor
public class AccountsController {
    private final AccountsService accountsService;

    @GetMapping("/api/v3/accounts/categories")
    @ApiOperation("Get a list of categories")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Accounts Categories."),
    })
    public List<AccountsCategoriesResponseBody> getAccountsCategories(@RequestParam(required = false) String type) {
        return accountsService.getAccountsCategories(type);
    }
}
