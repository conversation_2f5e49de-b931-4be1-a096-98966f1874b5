package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.domain.request.GetOfferForAcquisitionProspectRequest;
import com.tigo.galaxion.sales.facade.domain.request.GetOfferForCrossSellRequest;
import com.tigo.galaxion.sales.facade.domain.request.GetTariffPlansForAnOfferRequest;
import com.tigo.galaxion.sales.facade.domain.response.ItemGroupWithAddOnsResponse;
import com.tigo.galaxion.sales.facade.domain.response.OffersAndConditionalDiscountsResponse;
import com.tigo.galaxion.sales.facade.domain.response.ProspectCatalogInclusiveEquipmentGroupResponse;
import com.tigo.galaxion.sales.facade.domain.response.TariffPlansAndConditionalDiscountsResponse;
import com.tigo.galaxion.sales.facade.services.CatalogService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;
import org.springframework.web.bind.annotation.RequestParam;



@Api
@RestController
@RequiredArgsConstructor
public class CatalogController {

    private final CatalogService catalogService;

    @GetMapping("/api/v1/prospects/{prospect_reference}/offers")
    public OffersAndConditionalDiscountsResponse getOffersForAcquisition(
        @PathVariable("prospect_reference") String prospectReference,
        @Valid GetOfferForAcquisitionProspectRequest request
    ) {
        return catalogService.getOffersForAcquisition(prospectReference, request);
    }
    

    @GetMapping("/cross-sells/{cross_sell_reference}/offers")
    public OffersAndConditionalDiscountsResponse getOffersForCrossSell(@PathVariable("cross_sell_reference") String crossSellReference,
                                                                       @Valid GetOfferForCrossSellRequest request) {
        return catalogService.getOffersForCrossSell(crossSellReference, request);
    }

    @GetMapping("/api/v1/prospects/{prospect_reference}/offers/{offer_code}/tariff-plans")
    public TariffPlansAndConditionalDiscountsResponse getTariffs(
        @RequestHeader("Authorization") String bearerToken,
        @PathVariable("prospect_reference") String prospectReference,
        @PathVariable("offer_code") String offerCode,
        GetTariffPlansForAnOfferRequest request
    ) {
        return catalogService.getTariffsPlansForAnOffer(prospectReference, offerCode, request);
    }

    @GetMapping("/api/v1/prospects/{prospect_reference}/offers/{offer_id}/add-ons")
    public List<ItemGroupWithAddOnsResponse> getAddOnsForAnOffer(
        @PathVariable("prospect_reference") String prospectReference,
        @PathVariable("offer_id") Long offerId
    ) {
        return catalogService.getAddOnsForAnOffer(prospectReference, offerId);
    }
    
    
    @GetMapping("/api/v1/prospects/{prospect_reference}/offers/{offer_id}/equipments")
    public List<ProspectCatalogInclusiveEquipmentGroupResponse> getEquipmentsForAnOffer(
        @PathVariable("prospect_reference") String prospectReference,
        @PathVariable("offer_id") Long offerId
    ){
        return catalogService.getEquipmentsForAnOffer(prospectReference, offerId);
    }
}
