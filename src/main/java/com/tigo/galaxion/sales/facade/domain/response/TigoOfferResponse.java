package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoOfferResponse {

    @ApiModelProperty(value = "The ID of the offer in the cart", example = "52", required = true)
    private Long id;

    @ApiModelProperty(value = "The base offer details", required = true)
    private TigoBaseOfferResponse baseOffer;

    @ApiModelProperty(value = "The amount to be paid vat included", required = true)
    private TigoAmountResponse amountVatIncluded;

    @ApiModelProperty(value = "The amount to be paid vat excluded", required = true)
    private TigoAmountResponse amountVatExcluded;

    @Builder.Default
    @ApiModelProperty(value = "The list of add-ons in cart for this offer")
    private List<TigoAddonResponse> addOns = new ArrayList<>();

    @Builder.Default
    @ApiModelProperty(value = "The discount applied on the offer")
    private List<TigoDiscountResponse> discounts = new ArrayList<>();

    @Setter
    @ApiModelProperty(value = "Sim delivery type")
    private String simDeliveryType;

    @Setter
    @ApiModelProperty(value = "Number type")
    private String numberType;

    @ApiModelProperty(value = "The sim card DTO (if needed)")
    private TigoSimCardResponse simCard;

    @ApiModelProperty(value = "The top up amount for this offer", example = "1000")
    private Long topUpAmount;

    @ApiModelProperty(value = "The number for this offer", example = "08123465679")
    private String number;

    @ApiModelProperty(value = "Directory preference", example = "UNLISTED")
    private String directoryPreference;

    @ApiModelProperty(value = "If the deposit should be apply to the offer", required = true, example = "true")
    private Boolean applyDeposit;

    @ApiModelProperty(value = "The deposit amount to be paid vat included", example = "5000")
    private Long depositAmountVatIncluded;

    @ApiModelProperty(value = "The deposit amount to be paid vat exclude", example = "5000")
    private Long depositAmountVatExcluded;

    @ApiModelProperty(value = "The deposit term", example = "7")
    private Long depositTerm;

    @Builder.Default
    @ApiModelProperty(value = "The equipment DTO (if needed)")
    private List<TigoEquipmentResponse> equipments = new ArrayList<>();

    @Builder.Default
    @ApiModelProperty(value = "The equipments inclusive to this offer")
    private List<TigoInclusiveEquipmentResponse> inclusiveEquipments = new ArrayList<>();

    @ApiModelProperty(value = "The activation fee amount vat included to be paid", example = "999")
    private TigoAmountResponse activationFeeVatIncluded;

    @ApiModelProperty(value = "The activation fee amount vat excluded to be paid", example = "999")
    private TigoAmountResponse activationFeeVatExcluded;

    @ApiModelProperty(value = "The ID of the parent offer", example = "52")
    private Long parentOfferId;

    @ApiModelProperty(value = "The installation address id of the offer", example = "42")
    private Long installationAddressId;

    @ApiModelProperty(value = "The activation date", example = "2022-22-08")
    private LocalDate activationAt;

    @ApiModelProperty(value = "The subscriber's birth date",  example = "1951-05-04")
    private LocalDate subscriberBirthDate;

    @ApiModelProperty(value = "Recurring amounts by occurrence")
    @Builder.Default
    private List<TigoRecurringAmountByOccurrenceResponse> recurringAmountsByOccurrence = new ArrayList<>();
}
