package com.tigo.galaxion.sales.facade.controller;

import java.util.List;

import static org.springframework.http.HttpStatus.CREATED;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.connector.account.AccountClient;
import com.tigo.galaxion.sales.facade.connector.account.domain.request.CreateAddOnToService;
import com.tigo.galaxion.sales.facade.connector.account.domain.request.CreateInclusiveEquipmentRequest;
import com.tigo.galaxion.sales.facade.connector.account.domain.response.EquipmentWithoutAddonV3Response;
import com.tigo.galaxion.sales.facade.connector.equipments.EquipmentClient;
import com.tigo.galaxion.sales.facade.connector.equipments.domain.Equipment;
import com.tigo.galaxion.sales.facade.connector.equipments.domain.EquipmentCpe;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

@Api
@RestController
@RequiredArgsConstructor
public class InclusiveEquipmentController {

	private final AccountClient accountClient;
	private final EquipmentClient equipmentClient;

	@PostMapping("/api/v3/subscriptions/{subscription_id}/inclusive-equipments")
	@ResponseStatus(value = CREATED)
	public List<EquipmentWithoutAddonV3Response> postMethodName(@PathVariable("subscription_id") Integer subscriptionId, @RequestBody List<CreateInclusiveEquipmentRequest> body) {
		return accountClient.createInclusiveEquipment(subscriptionId, body);
	}
	
	@PostMapping("/api/v2/services/{service_id}/add-ons")
	@ResponseStatus(value = CREATED)
    public Integer addAddOnToService(@PathVariable("service_id") String serviceId, @RequestBody CreateAddOnToService addOnId){
		return accountClient.addAddOnToService(serviceId, addOnId);
	}

	/**
	 * Equipment
	 */
	@GetMapping("/api/v1/equipments/equipment/orders/{orderId}/services/{serviceId}")
	Equipment getEquipmentByServiceIdAndOrderId(@PathVariable("orderId") String orderId, @PathVariable("serviceId") Integer serviceId){
		return equipmentClient.getEquipmentByServiceIdAndOrderId(orderId, serviceId);
	}

	@PostMapping("/api/v1/equipments/equipment")
	@ResponseStatus(value = CREATED)
	Equipment addEquipment(@RequestBody Equipment equipment){
		return equipmentClient.addEquipment(equipment);
	}

	/**
	 * Equipment CPE
	 */
	@PostMapping("/api/v1/equipments/equipment/{equipmentId}/cpe")
	@ResponseStatus(value = CREATED)
	EquipmentCpe addEquipmentCPE(@PathVariable("equipmentId") String equipmentId, @RequestBody EquipmentCpe equipment){
		return equipmentClient.addEquipmentCPE(equipmentId, equipment);
	}

	@GetMapping("/api/v1/equipments/cpe/{equipmentId}")
	EquipmentCpe getEquipmentByServiceIdAndOrderId(@PathVariable("equipmentId") String equipmentId){
		return equipmentClient.getEquipmentByServiceIdAndOrderId(equipmentId);
	}
}
