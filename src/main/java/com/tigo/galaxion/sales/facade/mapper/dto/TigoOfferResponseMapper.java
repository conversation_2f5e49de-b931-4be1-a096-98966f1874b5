package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AddOnResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AmountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.BaseOfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.ChargeResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.DiscountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.EquipmentResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.HandsetResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.OfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.PricePlanResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.RecurringAmountByOccurrenceResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.RecurringAmountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.SimCardResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.TariffPlanDiscountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.UsageResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.equipments.InclusiveEquipmentResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.equipments.InclusiveHandsetResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;
import com.tigo.galaxion.sales.facade.domain.response.TigoAddonResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoAmountResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoBaseOfferResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoChargeResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoDiscountResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoEquipmentResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoHandsetResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoInclusiveEquipmentResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoInclusiveHandsetResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoOfferResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoPricePlanResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoRecurringAmountByOccurrenceResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoSimCardResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoTariffPlanDiscountResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoUsageResponse;
import com.tigo.galaxion.sales.facade.model.entity.OfferEntity;
import com.tigo.galaxion.sales.facade.services.retrieval.OfferRetrievalService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class TigoOfferResponseMapper {

    private final OfferRetrievalService offerRetrievalService;

    public List<TigoOfferResponse> buildTigoOfferResponses(List<OfferResponse> offerResponses) {
        return offerResponses.stream()
                             .map(this::buildTigoOfferResponse)
                             .toList();
    }

    private static TigoAddonResponse buildTigoAddonResponse(AddOnResponse addOnResponse) {
        if (addOnResponse == null) {
            return null;
        }
        return TigoAddonResponse.builder()
                                .id(addOnResponse.getId())
                                .description(addOnResponse.getDescription())
                                .displayOrder(addOnResponse.getDisplayOrder())
                                .usages(buildTigoUsageResponses(addOnResponse.getUsages()))
                                .catalogCode(addOnResponse.getCatalogCode())
                                .amountVatIncluded(buildTigoAmountResponse(addOnResponse.getAmountVatIncluded()))
                                .amountVatExcluded(buildTigoAmountResponse(addOnResponse.getAmountVatExcluded()))
                                .max(addOnResponse.getMax())
                                .itemGroup(addOnResponse.getItemGroup())
                                .recurringAmountsByOccurrence(buildTigoRecurringAmountByOccurrenceResponses(addOnResponse.getRecurringAmountsByOccurrence()))
                                .build();
    }

    private static List<TigoInclusiveEquipmentResponse> buildTigoInclusiveEquipmentResponses(List<InclusiveEquipmentResponse> responses) {
        return responses.stream().map(TigoOfferResponseMapper::buildTigoInclusiveEquipmentResponse).toList();
    }

    private static TigoInclusiveEquipmentResponse buildTigoInclusiveEquipmentResponse(InclusiveEquipmentResponse response) {
        var builder = TigoInclusiveEquipmentResponse.builder();
        if (response.getType() == EquipmentTypeEnum.HANDSET) {
            InclusiveHandsetResponse handset = (InclusiveHandsetResponse) response;
            builder = TigoInclusiveHandsetResponse.builder()
                                                  .capacity(handset.getCapacity())
                                                  .networkCompatibility(handset.getNetworkCompatibility())
                                                  .simCardTypes(handset.getSimCardTypes());
        }
        return builder.main(response.getMain())
                      .catalogCode(response.getCatalogCode())
                      .color(response.getColor())
                      .colorCode(response.getColorCode())
                      .description(response.getDescription())
                      .manufacturer(response.getManufacturer())
                      .model(response.getModel())
                      .inventoryCode(response.getInventoryCode())
                      .type(response.getType())
                      .build();
    }

    private static List<TigoEquipmentResponse> buildTigoEquipmentResponses(List<EquipmentResponse> responses) {
        return responses.stream().map(TigoOfferResponseMapper::buildTigoEquipmentResponse).toList();
    }

    private static TigoEquipmentResponse buildTigoEquipmentResponse(EquipmentResponse response) {
        var builder = TigoEquipmentResponse.builder();
        if (response.getType() == EquipmentTypeEnum.HANDSET) {
            HandsetResponse handset = (HandsetResponse) response;
            builder = TigoHandsetResponse.builder()
                                         .capacity(handset.getCapacity())
                                         .networkCompatibility(handset.getNetworkCompatibility())
                                         .simCardTypes(handset.getSimCardTypes());
        }
        return builder.id(response.getId())
                      .type(response.getType())
                      .catalogCode(response.getCatalogCode())
                      .imei(response.getImei())
                      .amountVatIncluded(buildTigoAmountResponse(response.getAmountVatIncluded()))
                      .amountVatExcluded(buildTigoAmountResponse(response.getAmountVatExcluded()))
                      .color(response.getColor())
                      .colorCode(response.getColorCode())
                      .description(response.getDescription())
                      .manufacturer(response.getManufacturer())
                      .model(response.getModel())
                      .inventoryCode(response.getInventoryCode())
                      .charge(buildTigoChargeResponse(response.getCharge()))
                      .addOn(buildTigoAddonResponse(response.getAddon()))
                      .subsidy(response.getSubsidy())
                      .subsidyApplied(response.isSubsidyApplied())
                      .equipmentFinancing(response.getEquipmentFinancing())
                      .build();
    }

    private static TigoSimCardResponse buildTigoSimCardResponse(SimCardResponse simCardResponse) {
        if (simCardResponse == null) return null;
        return TigoSimCardResponse.builder()
                                  .msisdn(simCardResponse.getMsisdn())
                                  .iccid(simCardResponse.getIccid())
                                  .imsi(simCardResponse.getImsi())
                                  .build();
    }

    private static TigoDiscountResponse buildTigoDiscountResponse(DiscountResponse discountResponse) {
        return TigoDiscountResponse.builder()
                                   .code(discountResponse.getCatalogCode())
                                   .description(discountResponse.getDescription())
                                   .billingType(discountResponse.getBillingType())
                                   .discountItemType(discountResponse.getDiscountItemType())
                                   .value(discountResponse.getValue())
                                   .vatExcludedAmount(discountResponse.getVatExcludedAmount())
                                   .reason(discountResponse.getReason())
                                   .build();
    }

    private static List<TigoDiscountResponse> buildTigoDiscountResponses(List<DiscountResponse> discountResponses) {
        return discountResponses.stream().map(TigoOfferResponseMapper::buildTigoDiscountResponse).toList();
    }

    public static List<TigoRecurringAmountByOccurrenceResponse> buildTigoRecurringAmountByOccurrenceResponses(List<RecurringAmountByOccurrenceResponse> responses) {
        return responses.stream().map(TigoOfferResponseMapper::buildTigoRecurringAmountByOccurrenceResponse).toList();
    }

    private static List<TigoAddonResponse> buildTigoAddonResponses(List<AddOnResponse> responses) {
        return responses.stream().map(TigoOfferResponseMapper::buildTigoAddonResponse).toList();
    }

    private static TigoBaseOfferResponse buildTigoBaseOfferResponse(BaseOfferResponse baseOfferResponse) {
        return TigoBaseOfferResponse.builder()
                                    .catalogCode(baseOfferResponse.getCatalogCode())
                                    .serviceGroup(baseOfferResponse.getServiceGroup())
                                    .serviceDomains(baseOfferResponse.getServiceDomains())
                                    .description(baseOfferResponse.getDescription())
                                    .comment(baseOfferResponse.getComment())
                                    .catalogTariffPlanCode(baseOfferResponse.getCatalogTariffPlanCode())
                                    .tariffPlanDiscounts(buildTigoTariffPlanDiscountResponses(baseOfferResponse.getTariffPlanDiscounts()))
                                    .simOnly(baseOfferResponse.getSimOnly())
                                    .tariffPlanDescription(baseOfferResponse.getTariffPlanDescription())
                                    .commitmentDuration(baseOfferResponse.getCommitmentDuration().name())
                                    .amountVatIncluded(buildTigoAmountResponse(baseOfferResponse.getAmountVatIncluded()))
                                    .amountVatExcluded(buildTigoAmountResponse(baseOfferResponse.getAmountVatExcluded()))
                                    .displayOrder(baseOfferResponse.getDisplayOrder())
                                    .usages(buildTigoUsageResponses(baseOfferResponse.getUsages()))
                                    .charges(buildTigoChargeResponses(baseOfferResponse.getCharges()))
                                    .broadbandTechnology(baseOfferResponse.getBroadbandTechnology())
                                    .recurringAmountsByOccurrence(buildTigoRecurringAmountByOccurrenceResponses(baseOfferResponse.getRecurringAmountsByOccurrence()))
                                    .build();
    }

    private static TigoRecurringAmountByOccurrenceResponse buildTigoRecurringAmountByOccurrenceResponse(RecurringAmountByOccurrenceResponse response) {
        return TigoRecurringAmountByOccurrenceResponse.builder()
                                                      .amountVatIncluded(response.amountVatIncluded())
                                                      .amountVatExcluded(response.amountVatExcluded())
                                                      .occurrence(response.occurrence())
                                                      .build();
    }

    private TigoOfferResponse buildTigoOfferResponse(OfferResponse offerResponse) {
        var offer = offerRetrievalService.get(offerResponse.getId());
        return TigoOfferResponse.builder()
                                .id(offerResponse.getId())
                                .baseOffer(buildTigoBaseOfferResponse(offerResponse.getBaseOffer()))
                                .amountVatIncluded(buildTigoAmountResponse(offerResponse.getAmountVatIncluded()))
                                .amountVatExcluded(buildTigoAmountResponse(offerResponse.getAmountVatExcluded()))
                                .addOns(buildTigoAddonResponses(offerResponse.getAddOns()))
                                .discounts(buildTigoDiscountResponses(offerResponse.getDiscounts()))
                                .simCard(buildTigoSimCardResponse(offerResponse.getSimCard()))
                                .topUpAmount(offerResponse.getTopUpAmount())
                                .number(offerResponse.getNumber())
                                .directoryPreference(offerResponse.getDirectoryPreference() == null ? null : offerResponse.getDirectoryPreference().name())
                                .applyDeposit(offerResponse.getApplyDeposit())
                                .depositAmountVatIncluded(offerResponse.getDepositAmountVatIncluded())
                                .depositAmountVatExcluded(offerResponse.getDepositAmountVatExcluded())
                                .depositTerm(offerResponse.getDepositTerm())
                                .equipments(buildTigoEquipmentResponses(offerResponse.getEquipments()))
                                .inclusiveEquipments(buildTigoInclusiveEquipmentResponses(offerResponse.getInclusiveEquipments()))
                                .activationFeeVatIncluded(buildTigoAmountResponse(offerResponse.getActivationFeeVatIncluded()))
                                .activationFeeVatExcluded(buildTigoAmountResponse(offerResponse.getActivationFeeVatExcluded()))
                                .parentOfferId(offerResponse.getParentOfferId())
                                .installationAddressId(offerResponse.getInstallationAddressId())
                                .activationAt(offerResponse.getActivationAt())
                                .subscriberBirthDate(offerResponse.getSubscriberBirthDate())
                                .simDeliveryType(offer.map(OfferEntity::getSimDeliveryType).orElse(null))
                                .numberType(offer.map(OfferEntity::getMsisdnType).orElse(null))
                                .recurringAmountsByOccurrence(buildTigoRecurringAmountByOccurrenceResponses(offerResponse.getRecurringAmountsByOccurrence()))
                                .build();
    }

    private static TigoChargeResponse buildTigoChargeResponse(ChargeResponse chargeResponse) {
        return TigoChargeResponse.builder()
                                 .catalogCode(chargeResponse.getCatalogCode())
                                 .pricePlan(buildTigoPricePlanResponse(chargeResponse.getPricePlan()))
                                 .build();
    }

    private static List<TigoChargeResponse> buildTigoChargeResponses(List<ChargeResponse> chargeResponses) {
        return chargeResponses.stream().map(TigoOfferResponseMapper::buildTigoChargeResponse).toList();
    }

    private static TigoPricePlanResponse buildTigoPricePlanResponse(PricePlanResponse pricePlanResponse) {
        return TigoPricePlanResponse.builder()
                                    .catalogCode(pricePlanResponse.getCatalogCode())
                                    .amountVatIncluded(pricePlanResponse.getAmountVatIncluded())
                                    .amountVatExcluded(pricePlanResponse.getAmountVatExcluded())
                                    .build();
    }

    private static TigoUsageResponse buildTigoUsageResponse(UsageResponse usageResponse) {
        return TigoUsageResponse.builder()
                                .catalogCode(usageResponse.getCatalogCode())
                                .description(usageResponse.getDescription())
                                .type(usageResponse.getType())
                                .displayOrder(usageResponse.getDisplayOrder())
                                .build();
    }

    private static List<TigoUsageResponse> buildTigoUsageResponses(List<UsageResponse> usageResponses) {
        return usageResponses.stream().map(TigoOfferResponseMapper::buildTigoUsageResponse).toList();
    }

    private static List<TigoTariffPlanDiscountResponse> buildTigoTariffPlanDiscountResponses(List<TariffPlanDiscountResponse> tariffPlanDiscountResponses) {
        return tariffPlanDiscountResponses.stream()
                                          .map(TigoOfferResponseMapper::buildTigoTariffPlanDiscountResponse)
                                          .toList();
    }

    private static TigoTariffPlanDiscountResponse buildTigoTariffPlanDiscountResponse(TariffPlanDiscountResponse tariffPlanDiscountResponse) {
        return TigoTariffPlanDiscountResponse.builder()
                                             .description(tariffPlanDiscountResponse.getDescription())
                                             .amountVatIncluded(tariffPlanDiscountResponse.getAmountVatIncluded())
                                             .amountVatExcluded(tariffPlanDiscountResponse.getAmountVatExcluded())
                                             .catalogCode(tariffPlanDiscountResponse.getCatalogCode())
                                             .billingType(tariffPlanDiscountResponse.getBillingType())
                                             .occurrence(tariffPlanDiscountResponse.getOccurrence())
                                             .discountItemType(tariffPlanDiscountResponse.getDiscountItemType())
                                             .build();
    }

    public static TigoAmountResponse buildTigoAmountResponse(AmountResponse amountResponse) {
        if (amountResponse == null) return null;
        return TigoAmountResponse.builder()
                                 .oneOffAmount(amountResponse.getOneOffAmount())
                                 .discountedOneOffAmount(amountResponse.getDiscountedOneOffAmount())
                                 .upFrontAmount(amountResponse.getUpFrontAmount())
                                 .discountedUpFrontAmount(amountResponse.getDiscountedUpFrontAmount())
                                 .recurringAmount(amountResponse.getRecurringAmount())
                                 .build();
    }

}
