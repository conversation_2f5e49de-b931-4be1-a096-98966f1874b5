
package com.tigo.galaxion.sales.facade.soap.get_task;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.Date;

@Getter
@Setter
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TaskResponse", propOrder = {
    "key",
    "revision",
    "stamp",
    "callID",
    "number",
    "earlyStart",
    "dueDate",
    "lateStart",
    "priority",
    "status",
    "customer",
    "calendar",
    "region",
    "district",
    "postcode",
    "preferredEngineers",
    "contractType",
    "openDate",
    "contactDate",
    "confirmationDate",
    "taskType",
    "duration",
    "requiredEngineers",
    "numberOfRequiredEngineers",
    "requiredSkills1",
    "requiredSkills2",
    "engineerType",
    "requiredEngineerTools",
    "critical",
    "timeDependencies",
    "engineerDependencies",
    "appointmentStart",
    "appointmentFinish",
    "contactName",
    "contactPhoneNumber",
    "binaryData",
    "latitude",
    "longitude",
    "gisDataSource",
    "street",
    "city",
    "mcState",
    "state",
    "taskStatusContext",
    "isCrewTask",
    "countryID",
    "engineerRequirements",
    "isScheduled",
    "customerEmail",
    "excludedEngineers",
    "requiredCrewSize",
    "inJeopardy",
    "pinned",
    "jeopardyState",
    "displayStatus",
    "dispatchDate",
    "scheduleDate",
    "displayDate",
    "onSiteDate",
    "comment",
    "customerReference",
    "stateSubdivision",
    "citySubdivision",
    "team",
    "signature",
    "externalRefID",
    "partsUsed",
    "assets",
    "backReportings",
    "taskTypeCategory",
    "workOrderItem",
    "userCustomerAccount",
    "isAppointment",
    "travelDate",
    "completionDate",
    "attachments",
    "cancellationDate",
    "isMegatask",
    "isBundled",
    "isManuallyBundled",
    "megatask",
    "megataskPureDuration",
    "bundlerConfiguration",
    "subtasks",
    "skillsDuration",
    "customerAccount",
    "area",
    "incompleteReason",
    "mcWorkPackageDescription",
    "mcComment",
    "mccrmComment",
    "mccontactEmail",
    "mccustomerCode",
    "mccustomerPhoneNumber",
    "mcsaldoPending",
    "mcbillingAccountInfo",
    "mcconnectionData",
    "lastRejectedEngineer",
    "rejectedDate",
    "rejectionReason",
    "cancellationReason",
    "mcconfirmationStatus",
    "mcmvpartsRequired",
    "mcmvmaterialUsed",
    "mcmvequipmentUsed",
    "mcmvequipmentCollected",
    "mcnoMaterialUsed",
    "mcnoEquipmentUsed",
    "mccauseReason",
    "mclaboresUsed",
    "mccustomsSeal",
    "mccodeBobina1",
    "mccodeBobina2",
    "mcmvservices",
    "dynamicPriority",
    "externalRefIDExtension",
    "serviceSummary",
    "streetSmartJob",
    "recurrenceTask",
    "rtIsRecurringTask",
    "rtUpdateAllRecurrences",
    "rtIsPrime",
    "archiveStatus",
    "assignedEngineerName",
    "mobileKey",
    "unit",
    "supervisorStatusGroup",
    "isSingleTask",
    "fieldCommentEng",
    "mccustomerClass",
    "mcopeningReason",
    "mczonaRamal",
    "mctap",
    "mcboca",
    "mcinfoCustomerSite",
    "mcTaskClosureLongitude",
    "mcTaskClosureLatitude",
    "mcLastLocationInfo",
    "mcTaskClosureGeoToken",
    "mcTaskClosureGeoTokenEntered",
    "urlAuditInfo",
    "urlSurvey",
    "mcbuildingType",
    "mcservicesSignature",
    "mcplaca",
    "mccustomerIdentityNumber",
    "isDESent",
    "returnVerification",
    "isETASent",
    "isReminder24HourSent",
    "isSurveySent",
    "scheduleLowerBound",
    "scheduleUpperBound",
    "surveyAnswer",
    "surveyComment",
    "serviceAccepted",
    "mcgoogleLatitude",
    "mcgoogleLongitude",
    "mccustomerAssets",
    "mcptWorkingArea",
    "mcptFilter",
    "mctpPolygonFilter",
    "workingArea",
    "mcdispatchOMSent",
    "mcdeComment",
    "mccemAttachments",
    "mccontactPhoneNumbers",
    "taskAppointmentTime",
    "taskAppointmentUpdated",
    "mcservicePaid",
    "mcunscheduledForNotPaid",
    "mccemabCancellationNotification",
    "mcunscheduledForNotPaidCounter",
    "mctaskUnpaidReminderSent",
    "mccemNoMsgOnDispatcherReschedule",
    "mcisNonABTaskScheduledNotificationSent",
    "mcnewTaskAssigned",
    "mcwifiCertService",
    "mcshowCertificateWifiInitiate",
    "mccertificateResultWifi",
    "mccertificationActionType",
    "mccertWifiGenerateTriggerC",
    "mccertificateWifiRequired",
    "mctapHighFrecuency",
    "mctapLowFrecuency",
    "mcsplitterHighFrecuency",
    "mcsplitterLowFrecuency",
    "mccmHighFrecuency",
    "mccmLowFrecuency",
    "mcresendClosureOM",
    "codigoTecnico",
    "serviceAppointmentID",
    "appointmentStatus",
    "serviceTerritory"
})
public class TaskResponse {

    @XmlElement(name = "Key")
    protected int key;

    @XmlElement(name = "Revision")
    protected int revision;

    @XmlElement(name = "Stamp")
    protected Stamp stamp;

    @XmlElement(name = "CallID")
    protected String callID;

    @XmlElement(name = "Number")
    protected int number;

    @XmlElement(name = "EarlyStart")
    @XmlSchemaType(name = "date")
    protected Date earlyStart;

    @XmlElement(name = "DueDate")
    @XmlSchemaType(name = "date")
    protected Date dueDate;

    @XmlElement(name = "LateStart")
    @XmlSchemaType(name = "date")
    protected Date lateStart;

    @XmlElement(name = "Priority")
    protected String priority;

    @XmlElement(name = "Status")
    protected String status;

    @XmlElement(name = "Customer")
    protected String customer;

    @XmlElement(name = "Calendar")
    protected String calendar;

    @XmlElement(name = "Region")
    protected String region;

    @XmlElement(name = "District")
    protected String district;

    @XmlElement(name = "Postcode")
    protected String postcode;

    @XmlElement(name = "PreferredEngineers")
    protected String preferredEngineers;

    @XmlElement(name = "ContractType")
    protected String contractType;

    @XmlElement(name = "OpenDate")
    @XmlSchemaType(name = "date")
    protected Date openDate;

    @XmlElement(name = "ContactDate")
    @XmlSchemaType(name = "date")
    protected Date contactDate;

    @XmlElement(name = "ConfirmationDate")
    @XmlSchemaType(name = "date")
    protected Date confirmationDate;

    @XmlElement(name = "TaskType")
    protected String taskType;

    @XmlElement(name = "Duration")
    protected double duration;

    @XmlElement(name = "RequiredEngineers")
    protected String requiredEngineers;

    @XmlElement(name = "NumberOfRequiredEngineers")
    protected int numberOfRequiredEngineers;

    @XmlElement(name = "RequiredSkills1")
    protected TaskRequiredSkill1 requiredSkills1;

    @XmlElement(name = "RequiredSkills2")
    protected String requiredSkills2;

    @XmlElement(name = "EngineerType")
    protected String engineerType;

    @XmlElement(name = "RequiredEngineerTools")
    protected String requiredEngineerTools;

    @XmlElement(name = "Critical")
    protected int critical;

    @XmlElement(name = "TimeDependencies")
    protected String timeDependencies;

    @XmlElement(name = "EngineerDependencies")
    protected String engineerDependencies;

    @XmlElement(name = "AppointmentStart")
    @XmlSchemaType(name = "date")
    protected Date appointmentStart;

    @XmlElement(name = "AppointmentFinish")
    @XmlSchemaType(name = "date")
    protected Date appointmentFinish;

    @XmlElement(name = "ContactName")
    protected String contactName;

    @XmlElement(name = "ContactPhoneNumber")
    protected int contactPhoneNumber;

    @XmlElement(name = "BinaryData")
    protected String binaryData;

    @XmlElement(name = "Latitude")
    protected double latitude;

    @XmlElement(name = "Longitude")
    protected double longitude;

    @XmlElement(name = "GISDataSource")
    protected int gisDataSource;

    @XmlElement(name = "Street")
    protected String street;

    @XmlElement(name = "City")
    protected String city;

    @XmlElement(name = "MCState")
    protected String mcState;

    @XmlElement(name = "State")
    protected String state;

    @XmlElement(name = "TaskStatusContext")
    protected int taskStatusContext;

    @XmlElement(name = "IsCrewTask")
    protected int isCrewTask;

    @XmlElement(name = "CountryID")
    protected String countryID;

    @XmlElement(name = "EngineerRequirements")
    protected String engineerRequirements;

    @XmlElement(name = "IsScheduled")
    protected int isScheduled;

    @XmlElement(name = "CustomerEmail")
    protected int customerEmail;

    @XmlElement(name = "ExcludedEngineers")
    protected int excludedEngineers;

    @XmlElement(name = "RequiredCrewSize")
    protected int requiredCrewSize;

    @XmlElement(name = "InJeopardy")
    protected int inJeopardy;

    @XmlElement(name = "Pinned")
    protected int pinned;

    @XmlElement(name = "JeopardyState")
    protected int jeopardyState;

    @XmlElement(name = "DisplayStatus")
    protected String displayStatus;

    @XmlElement(name = "DispatchDate")
    @XmlSchemaType(name = "date")
    protected Date dispatchDate;

    @XmlElement(name = "ScheduleDate")
    @XmlSchemaType(name = "date")
    protected Date scheduleDate;

    @XmlElement(name = "DisplayDate")
    @XmlSchemaType(name = "date")
    protected Date displayDate;

    @XmlElement(name = "OnSiteDate")
    @XmlSchemaType(name = "date")
    protected Date onSiteDate;

    @XmlElement(name = "Comment")
    protected String comment;

    @XmlElement(name = "CustomerReference")
    protected String customerReference;

    @XmlElement(name = "StateSubdivision")
    protected String stateSubdivision;

    @XmlElement(name = "CitySubdivision")
    protected String citySubdivision;

    @XmlElement(name = "Team")
    protected String team;

    @XmlElement(name = "Signature")
    protected String signature;

    @XmlElement(name = "ExternalRefID")
    protected String externalRefID;

    @XmlElement(name = "PartsUsed")
    protected String partsUsed;

    @XmlElement(name = "Assets")
    protected String assets;

    @XmlElement(name = "BackReportings")
    protected String backReportings;

    @XmlElement(name = "TaskTypeCategory")
    protected String taskTypeCategory;

    @XmlElement(name = "WorkOrderItem")
    protected String workOrderItem;

    @XmlElement(name = "User_CustomerAccount")
    protected CustomerAccount userCustomerAccount;

    @XmlElement(name = "IsAppointment")
    protected int isAppointment;

    @XmlElement(name = "TravelDate")
    @XmlSchemaType(name = "date")
    protected Date travelDate;

    @XmlElement(name = "CompletionDate")
    @XmlSchemaType(name = "date")
    protected Date completionDate;

    @XmlElement(name = "Attachments")
    protected String attachments;

    @XmlElement(name = "CancellationDate")
    @XmlSchemaType(name = "date")
    protected Date cancellationDate;

    @XmlElement(name = "IsMegatask")
    protected int isMegatask;

    @XmlElement(name = "IsBundled")
    protected int isBundled;

    @XmlElement(name = "IsManuallyBundled")
    protected int isManuallyBundled;

    @XmlElement(name = "Megatask")
    protected String megatask;

    @XmlElement(name = "MegataskPureDuration")
    protected int megataskPureDuration;

    @XmlElement(name = "BundlerConfiguration")
    protected String bundlerConfiguration;

    @XmlElement(name = "Subtasks")
    protected String subtasks;

    @XmlElement(name = "SkillsDuration")
    protected String skillsDuration;

    @XmlElement(name = "CustomerAccount")
    protected String customerAccount;

    @XmlElement(name = "Area")
    protected String area;

    @XmlElement(name = "IncompleteReason")
    protected String incompleteReason;

    @XmlElement(name = "MCWorkPackageDescription")
    protected String mcWorkPackageDescription;

    @XmlElement(name = "MCComment")
    protected String mcComment;

    @XmlElement(name = "MCCRMComment")
    protected String mccrmComment;

    @XmlElement(name = "MCContactEmail")
    protected String mccontactEmail;

    @XmlElement(name = "MCCustomerCode")
    protected int mccustomerCode;

    @XmlElement(name = "MCCustomerPhoneNumber")
    protected int mccustomerPhoneNumber;

    @XmlElement(name = "MCSaldoPending")
    protected double mcsaldoPending;

    @XmlElement(name = "MCBillingAccountInfo")
    protected String mcbillingAccountInfo;

    @XmlElement(name = "MCConnectionData")
    protected String mcconnectionData;

    @XmlElement(name = "LastRejectedEngineer")
    protected String lastRejectedEngineer;

    @XmlElement(name = "RejectedDate")
    @XmlSchemaType(name = "date")
    protected Date rejectedDate;

    @XmlElement(name = "RejectionReason")
    protected String rejectionReason;

    @XmlElement(name = "CancellationReason")
    protected String cancellationReason;

    @XmlElement(name = "MCConfirmationStatus")
    protected String mcconfirmationStatus;

    @XmlElement(name = "MCMVPartsRequired")
    protected String mcmvpartsRequired;

    @XmlElement(name = "MCMVMaterialUsed")
    protected MCMVMaterialUsedKey mcmvmaterialUsed;

    @XmlElement(name = "MCMVEquipmentUsed")
    protected MCMVEquipmentUsedKey mcmvequipmentUsed;

    @XmlElement(name = "MCMVEquipmentCollected")
    protected MCMVEquipmentCollectedKey mcmvequipmentCollected;

    @XmlElement(name = "MCNoMaterialUsed")
    protected int mcnoMaterialUsed;

    @XmlElement(name = "MCNoEquipmentUsed")
    protected int mcnoEquipmentUsed;

    @XmlElement(name = "MCCauseReason")
    protected String mccauseReason;

    @XmlElement(name = "MCLaboresUsed")
    protected MCLaboresUsedKey mclaboresUsed;

    @XmlElement(name = "MCCustomsSeal")
    protected String mccustomsSeal;

    @XmlElement(name = "MCCodeBobina1")
    protected String mccodeBobina1;

    @XmlElement(name = "MCCodeBobina2")
    protected String mccodeBobina2;

    @XmlElement(name = "MCMVServices")
    protected MCServices mcmvservices;

    @XmlElement(name = "DynamicPriority")
    protected int dynamicPriority;

    @XmlElement(name = "ExternalRefIDExtension")
    protected int externalRefIDExtension;

    @XmlElement(name = "ServiceSummary")
    protected String serviceSummary;

    @XmlElement(name = "StreetSmartJob")
    protected int streetSmartJob;

    @XmlElement(name = "RecurrenceTask")
    protected String recurrenceTask;

    @XmlElement(name = "RTIsRecurringTask")
    protected int rtIsRecurringTask;

    @XmlElement(name = "RTUpdateAllRecurrences")
    protected int rtUpdateAllRecurrences;

    @XmlElement(name = "RTIsPrime")
    protected int rtIsPrime;

    @XmlElement(name = "ArchiveStatus")
    protected String archiveStatus;

    @XmlElement(name = "AssignedEngineerName")
    protected String assignedEngineerName;

    @XmlElement(name = "MobileKey")
    protected String mobileKey;

    @XmlElement(name = "Unit")
    protected String unit;

    @XmlElement(name = "SupervisorStatusGroup")
    protected String supervisorStatusGroup;

    @XmlElement(name = "IsSingleTask")
    protected int isSingleTask;

    @XmlElement(name = "FieldCommentEng")
    protected String fieldCommentEng;

    @XmlElement(name = "MCCustomerClass")
    protected String mccustomerClass;

    @XmlElement(name = "MCOpeningReason")
    protected String mcopeningReason;

    @XmlElement(name = "MCZonaRamal")
    protected String mczonaRamal;

    @XmlElement(name = "MCTap")
    protected String mctap;

    @XmlElement(name = "MCBoca")
    protected String mcboca;

    @XmlElement(name = "MCInfoCustomerSite")
    protected String mcinfoCustomerSite;

    @XmlElement(name = "MCTaskClosureLongitude")
    protected int mcTaskClosureLongitude;

    @XmlElement(name = "MCTaskClosureLatitude")
    protected int mcTaskClosureLatitude;

    @XmlElement(name = "MCLastLocationInfo")
    protected String mcLastLocationInfo;

    @XmlElement(name = "MCTaskClosureGeoToken")
    protected String mcTaskClosureGeoToken;

    @XmlElement(name = "MCTaskClosureGeoTokenEntered")
    protected String mcTaskClosureGeoTokenEntered;

    @XmlElement(name = "URLAuditInfo")
    protected String urlAuditInfo;

    @XmlElement(name = "URLSurvey")
    protected String urlSurvey;

    @XmlElement(name = "MCBuildingType")
    protected String mcbuildingType;

    @XmlElement(name = "MCServicesSignature")
    protected String mcservicesSignature;

    @XmlElement(name = "MCPlaca")
    protected String mcplaca;

    @XmlElement(name = "MCCustomerIdentityNumber")
    protected String mccustomerIdentityNumber;

    @XmlElement(name = "IsDESent")
    protected int isDESent;

    @XmlElement(name = "ReturnVerification")
    protected String returnVerification;

    @XmlElement(name = "IsETASent")
    protected int isETASent;

    @XmlElement(name = "IsReminder24HourSent")
    protected int isReminder24HourSent;

    @XmlElement(name = "IsSurveySent")
    protected int isSurveySent;

    @XmlElement(name = "ScheduleLowerBound")
    @XmlSchemaType(name = "date")
    protected Date scheduleLowerBound;

    @XmlElement(name = "ScheduleUpperBound")
    @XmlSchemaType(name = "date")
    protected Date scheduleUpperBound;

    @XmlElement(name = "SurveyAnswer")
    protected String surveyAnswer;

    @XmlElement(name = "SurveyComment")
    protected String surveyComment;

    @XmlElement(name = "ServiceAccepted")
    protected int serviceAccepted;

    @XmlElement(name = "MCGoogleLatitude")
    protected float mcgoogleLatitude;

    @XmlElement(name = "MCGoogleLongitude")
    protected float mcgoogleLongitude;

    @XmlElement(name = "MCCustomerAssets")
    protected String mccustomerAssets;

    @XmlElement(name = "MCPTWorkingArea")
    protected String mcptWorkingArea;

    @XmlElement(name = "MCPTFilter")
    protected String mcptFilter;

    @XmlElement(name = "MCTPPolygonFilter")
    protected String mctpPolygonFilter;

    @XmlElement(name = "WorkingArea")
    protected String workingArea;

    @XmlElement(name = "MCDispatchOMSent")
    protected int mcdispatchOMSent;

    @XmlElement(name = "MCDEComment")
    protected String mcdeComment;

    @XmlElement(name = "MCCEMAttachments")
    protected String mccemAttachments;

    @XmlElement(name = "MCContactPhoneNumbers")
    protected int mccontactPhoneNumbers;

    @XmlElement(name = "TaskAppointmentTime")
    protected String taskAppointmentTime;

    @XmlElement(name = "TaskAppointmentUpdated")
    protected int taskAppointmentUpdated;

    @XmlElement(name = "MCServicePaid")
    protected boolean mcservicePaid;

    @XmlElement(name = "MCUnscheduledForNotPaid")
    protected int mcunscheduledForNotPaid;

    @XmlElement(name = "MCCEMABCancellationNotification")
    protected int mccemabCancellationNotification;

    @XmlElement(name = "MCUnscheduledForNotPaidCounter")
    protected int mcunscheduledForNotPaidCounter;

    @XmlElement(name = "MCTaskUnpaidReminderSent")
    protected int mctaskUnpaidReminderSent;

    @XmlElement(name = "MCCEMNoMsgOnDispatcherReschedule")
    protected int mccemNoMsgOnDispatcherReschedule;

    @XmlElement(name = "MCIsNonABTaskScheduledNotificationSent")
    protected int mcisNonABTaskScheduledNotificationSent;

    @XmlElement(name = "MCNewTaskAssigned")
    protected int mcnewTaskAssigned;

    @XmlElement(name = "MCWifiCertService")
    protected String mcwifiCertService;

    @XmlElement(name = "MCShowCertificateWifiInitiate")
    protected int mcshowCertificateWifiInitiate;

    @XmlElement(name = "MCCertificateResultWifi")
    protected String mccertificateResultWifi;

    @XmlElement(name = "MCCertificationActionType")
    protected String mccertificationActionType;

    @XmlElement(name = "MCCertWifiGenerateTrigger__c")
    protected boolean mccertWifiGenerateTriggerC;

    @XmlElement(name = "MCCertificateWifiRequired")
    protected boolean mccertificateWifiRequired;

    @XmlElement(name = "MCTAPHighFrecuency")
    protected String mctapHighFrecuency;

    @XmlElement(name = "MCTAPLowFrecuency")
    protected String mctapLowFrecuency;

    @XmlElement(name = "MCSplitterHighFrecuency")
    protected String mcsplitterHighFrecuency;

    @XmlElement(name = "MCSplitterLowFrecuency")
    protected String mcsplitterLowFrecuency;

    @XmlElement(name = "MCCMHighFrecuency")
    protected String mccmHighFrecuency;

    @XmlElement(name = "MCCMLowFrecuency")
    protected String mccmLowFrecuency;

    @XmlElement(name = "MCResendClosureOM")
    protected int mcresendClosureOM;

    @XmlElement(name = "CodigoTecnico")
    protected String codigoTecnico;

    @XmlElement(name = "ServiceAppointmentID")
    protected String serviceAppointmentID;

    @XmlElement(name = "AppointmentStatus")
    protected String appointmentStatus;

    @XmlElement(name = "ServiceTerritory")
    protected String serviceTerritory;
}