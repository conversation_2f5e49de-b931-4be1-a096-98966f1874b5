package com.tigo.galaxion.sales.facade.connector.order.service;

import com.tigo.galaxion.sales.facade.connector.order.service.response.ServiceResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "tigo-services-order-service",
        url = "${environment.url.tigo-services-service}"
)
public interface ServiceClient {

    @GetMapping("/serviceorder")
    ServiceResponse getOrder(@RequestHeader("partyRole") String partyRole,
                                   @RequestParam("msisdn") Long msisdn,
                                   @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate,
                                   @RequestParam("page") Integer page, @RequestParam("size") Integer size);
}
