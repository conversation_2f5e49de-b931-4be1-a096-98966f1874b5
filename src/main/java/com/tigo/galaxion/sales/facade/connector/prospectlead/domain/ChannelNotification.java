package com.tigo.galaxion.sales.facade.connector.prospectlead.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChannelNotification {
    @ApiModelProperty(value = "Notification Channel", required = true, example = "1")
    @JsonProperty("notificationChannel")
    private Number notificationChannel;

    @ApiModelProperty(value = "Notification Id", required = true, example = "0")
    @JsonProperty("notificationId")
    private Number notificationId;
}
