package com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.frauds;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Offer {
    @NotBlank
    private String id;

    @NotBlank
    private String name;

    @NotBlank
    private String value;
}
