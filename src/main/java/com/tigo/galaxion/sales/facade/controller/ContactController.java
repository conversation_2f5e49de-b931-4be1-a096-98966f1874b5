package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellResponse;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoCrossSellDeliveryContactRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoProspectContactRequest;
import com.tigo.galaxion.sales.facade.domain.response.ContactIdentityDocumentResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoContactResponse;
import com.tigo.galaxion.sales.facade.model.entity.ContactAddressEntity;
import com.tigo.galaxion.sales.facade.services.CrossSellContactService;
import com.tigo.galaxion.sales.facade.services.ProspectContactService;
import com.tigo.galaxion.sales.facade.services.retrieval.ContactIdentityDocumentRetrievalService;
import com.tigo.galaxion.sales.facade.services.retrieval.ContactRetrievalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api
@RestController
@RequiredArgsConstructor
public class ContactController {

    private final ContactRetrievalService contactRetrievalService;
    private final ContactIdentityDocumentRetrievalService contactIdentityDocumentRetrievalService;
    private final ProspectContactService prospectContactService;
    private final CrossSellContactService crossSellContactService;

    @GetMapping("/api/v1/prospects/{prospect_reference}/contacts")
    public TigoContactResponse getContact(@PathVariable("prospect_reference") String prospectReference) {
        return contactRetrievalService.getContactResponseForAcquisition(prospectReference);
    }

    @GetMapping("/api/v1/prospects/{prospect_reference}/contacts/identity-documents")
    public ContactIdentityDocumentResponse getContactIdentityDocument(
            @ApiParam(value = "The prospect reference", required = true, example = "53ODP1O7") @PathVariable("prospect_reference") String prospectReference) {
        return contactIdentityDocumentRetrievalService.getContactIdentityDocumentResponse(prospectReference);
    }

    @PutMapping("/api/v1/prospects/{prospect_reference}/contacts")
    public TigoContactResponse updateContactForProspect(@PathVariable("prospect_reference") String prospectReference,
            @RequestBody @Valid TigoProspectContactRequest request) {
        return prospectContactService.updateContact(prospectReference, request);
    }

    @PostMapping("/api/v1/prospects/{prospect_reference}/contact_address")
    public ContactAddressEntity createContactAddressForProspect(
            @PathVariable("prospect_reference") String prospectReference,
            @RequestBody @Valid AddOfferAddressRequest request) {
        return prospectContactService.createInstallationAddress(prospectReference, request);
    }

    @GetMapping("/api/v1/prospects/{prospect_reference}/contact_address")
    public AddressResponse getContactAddressForProspect(
            @PathVariable("prospect_reference") String prospectReference) {
        return prospectContactService.getInstallationAddress(prospectReference);
    }

    @PutMapping("/cross-sells/{reference}/contacts")
    public CrossSellResponse updateContactForCrossSell(@PathVariable("reference") String reference,
            @RequestBody @Valid TigoCrossSellDeliveryContactRequest request) {
        return crossSellContactService.updateContact(reference, request);
    }
}
