package com.tigo.galaxion.sales.facade.connector.crm_api.workflow_engine_facade;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.account.domain.problem.AccountProblem;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;
import org.zalando.problem.ThrowableProblem;

public class WEFErrorDecoder extends BaseProblemErrorDecoder {

    public WEFErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "accounts-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        return new AccountProblem(throwableProblem);
    }

}
