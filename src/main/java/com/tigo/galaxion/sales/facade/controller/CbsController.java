package com.tigo.galaxion.sales.facade.controller;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.UnsupportedEncodingException;
import com.tigo.galaxion.sales.facade.connector.cbs.CbsClient;
import com.tigo.galaxion.sales.facade.connector.cbs.CbsBusinessClient;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.request.CbsBalanceRequest;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.request.CbsInvoiceRequest;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.request.CbsPaymentRequest;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.request.CbsAccountRequest;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.response.CbsBalanceResponse;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.response.CbsInvoiceResponse;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.response.CbsPaymentResponse;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.response.CbsAccountResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

@Api
@RestController
@RequiredArgsConstructor
public class CbsController {

	private final CbsClient cbsClient;
	private final CbsBusinessClient cbsBusinessClient;

	@PostMapping("/api/v1/invoices")
	@ApiOperation("This interface will be used to query the invoice information of an account.")
	public CbsInvoiceResponse queryCbsInvoice(@RequestBody CbsInvoiceRequest cbsInvoiceRequest)
			throws JsonProcessingException, UnsupportedEncodingException {
		return cbsClient.queryCbsInvoice(urlEncode(cbsInvoiceRequest));
	}

	@PostMapping("/api/v1/payments")
	@ApiOperation("This interface will be used to query the payments information of an account.")
	public CbsPaymentResponse queryCbsPayment(@RequestBody CbsPaymentRequest cbsPaymentRequest)
			throws JsonProcessingException, UnsupportedEncodingException {
		return cbsClient.queryCbsPayment(urlEncode(cbsPaymentRequest));
	}

	@PostMapping("/api/v1/balances")
	@ApiOperation("This interface will be used to query the balance of account.")
	public CbsBalanceResponse queryCbsBalance(@RequestBody CbsBalanceRequest cbsBalanceRequest)
			throws JsonProcessingException, UnsupportedEncodingException {
		return cbsClient.queryCbsBalance(urlEncode(cbsBalanceRequest));
	}

	@PostMapping("/api/v1/accounts")
	@ApiOperation("This interface will be used to query the account information.")
	public CbsAccountResponse queryCbsAccount(@RequestBody CbsAccountRequest cbsAccountRequest)
			throws JsonProcessingException {
		return cbsBusinessClient.queryCbsAccount(cbsAccountRequest.getTraceId(), cbsAccountRequest.getAccountKey());
	}

	private String urlEncode(Object object) throws JsonProcessingException, UnsupportedEncodingException {
		ObjectMapper objectMapper = new ObjectMapper();
		// delete/ignore null fields
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		String jsonRequest = objectMapper.writeValueAsString(object);
		return URLEncoder.encode(jsonRequest, "UTF-8");
	}
}
