package com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.request;

import com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoCreditScoreRequest {

    private String documentNumber;
    private IdDocumentTypeEnum type;
    private String nationality;

    public String getType() {
        return type.getName();
    }
}
