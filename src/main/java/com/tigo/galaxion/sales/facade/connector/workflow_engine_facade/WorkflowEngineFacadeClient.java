package com.tigo.galaxion.sales.facade.connector.workflow_engine_facade;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.cloud.openfeign.FeignClient;

import com.tigo.galaxion.sales.facade.connector.config.PrivateAuthFeignClientConfiguration;
import com.tigo.galaxion.sales.facade.connector.workflow_engine_facade.domain.request.OrderRequestDTO;

@FeignClient(value = "WorkflowEngineFacadeClient", url = "${environment.url.workflow-engine-facade}",  configuration = PrivateAuthFeignClientConfiguration.class)
public interface  WorkflowEngineFacadeClient {

     @PostMapping(value = "/orders")
    void createOrder(
        @RequestBody OrderRequestDTO request,
        @RequestParam(name = "isSync", defaultValue = "true" , required = false) boolean isSync
    );
    
}
