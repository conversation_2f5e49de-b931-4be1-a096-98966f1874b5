package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CartAddOnRequest {

    @ApiModelProperty(value = "The addOn's catalog code", example = "AO_DATA_3GB_BOOSTER", required = true)
    @NotBlank(message = "Catalog Code can not be null or blank")
    private String catalogCode;
}
