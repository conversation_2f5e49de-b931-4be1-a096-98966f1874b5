package com.tigo.galaxion.sales.facade.domain.response;

import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Set;

@SuperBuilder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class TigoInclusiveHandsetResponse extends TigoInclusiveEquipmentResponse {

    @ApiModelProperty(value = "The handset capacity", required = true, example = "128 Go")
    private String capacity;

    @ApiModelProperty(value = "The handset network compatibility", required = true, example = "4G/5G")
    private String networkCompatibility;

    @ApiModelProperty(value = "The handset sim card size", required = true, example = "[NANO]")
    private Set<String> simCardTypes;

    @Builder.Default
    @ApiModelProperty(value = "The item type", example = "HANDSET", required = true)
    private EquipmentTypeEnum type = EquipmentTypeEnum.HANDSET;
}
