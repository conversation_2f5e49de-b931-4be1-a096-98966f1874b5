package com.tigo.galaxion.sales.facade.mapper.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.tigo.galaxion.sales.facade.connector.address.domain.request.AddressRequest;
import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoAddressRequest;
import com.tigo.galaxion.sales.facade.domain.response.AddressMSResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AddressRequestMapper {

    public static AddressRequest buildAddressRequest(TigoAddressRequest request) {
        return AddressRequest.builder()
                .area(request.getArea())
                .town(request.getTown())
                .street(request.getStreetName())
                .streetNumber(request.getStreetNumber())
                .code(request.getPostCode())
                .addressLine1(request.getStreetNumber() + " " + request.getStreetName())
                .build();
    }

    public static AddressRequest buildAddressRequest(AddOfferAddressRequest request) {
        return AddressRequest.builder()
                .county(request.getCountryCode().toString())
                .town(request.getDepartmentCode().toString())
                .area(request.getMunicipalityCode().toString())
                .street(request.getNormalizedAddress())
                .addressLine1(request.getLatitude().toString())
                .addressLine2(request.getLongitude().toString())
                .addressLine3(request.getStratum().toString())
                .code(request.getAddressCode().toString())
                .build();
    }

    public static AddressRequest buildAddressRequestForMS(AddressMSResponse request) {
        return AddressRequest.builder()
                .country(request.getCountry())
                .area(request.getCountryCode())
                .town(request.getDepartment())
                .street(request.getMunicipality())
                .streetNumber(request.getLatitude())
                .streetQualifier(request.getStratum())
                .code(request.getLongitude())
                .addressLine1(request.getAddress())
                .addressLine2(request.getMicrozone())
                .addressLine3(request.getAddressCode())
                .build();
    }

    public static AddressMSResponse buildAddressResponseForMS(AddressResponse response) {
        return AddressMSResponse.builder()
                .country(response.getCountry())
                .countryCode(response.getArea())
                .department(response.getTown())
                .municipality(response.getStreet())
                .latitude(response.getStreetNumber())
                .stratum(response.getStreetQualifier())
                .longitude(response.getCode())
                .address(response.getAddressLine1())
                .microzone(response.getAddressLine2())
                .addressCode(response.getAddressLine3())
                .build();
    }

    private static String getTextValue(JsonNode node, String fieldName, String defaultValue) {
        JsonNode fieldNode = node.get(fieldName);
        return (fieldNode != null && !fieldNode.isNull()) ? fieldNode.asText() : defaultValue;
    }

    public static AddressMSResponse builAddressMSResponseForJsonNode (JsonNode response) {
        return AddressMSResponse.builder()
                .country("Colombia")
                .countryCode(getTextValue(response, "countryCode", null))
                .department(getTextValue(response, "department", null))
                .municipality(getTextValue(response, "municipality", null))
                .latitude(getTextValue(response, "latitude", null))
                .stratum(getTextValue(response, "stratum", null))
                .longitude(getTextValue(response, "longitude", null))
                .address(getTextValue(response, "address", null))
                .microzone(getTextValue(response, "microzone", null))
                .addressCode(getTextValue(response, "addressCode", null))
                .build();
    }
}
