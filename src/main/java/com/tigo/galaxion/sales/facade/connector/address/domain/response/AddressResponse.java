package com.tigo.galaxion.sales.facade.connector.address.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class AddressResponse {

    private Long id;
    private String area;
    private String town;
    private String street;
    private String streetNumber;
    private String code;
    private String county;
    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String poBox;
    private String country;
    private String streetQualifier;
}
