package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "code")
public class ProspectOfferResponse {

    @ApiModelProperty(value = "The offer catalog code", example = "GOMO", required = true)
    private String code;

    @ApiModelProperty(value = "The offer recurring amount vat included", example = "500", required = true)
    private Long recurringChargeAmountVatIncluded;

    @ApiModelProperty(value = "The offer recurring amount vat excluded", example = "500", required = true)
    private Long recurringChargeAmountVatExcluded;

    @ApiModelProperty(value = "The offer recurring amount vat included minus the conditional discount", example = "500", required = true)
    private Long discountedRecurringChargeAmountVatIncluded;

    @ApiModelProperty(value = "The offer recurring amount vat excluded minus the conditional discount", example = "500", required = true)
    private Long discountedRecurringChargeAmountVatExcluded;

    @ApiModelProperty(value = "The description", example = "Go More 2", required = true)
    private String description;

    @ApiModelProperty(value = "The comment", example = "GOV All in 5GB", required = true)
    private String comment;

    @ApiModelProperty(value = "Display order, lowest numbers displayed first", example = "10", required = true)
    private Long displayOrder;

    @Builder.Default
    @ToString.Exclude
    @ApiModelProperty(value = "The usages", required = true)
    private List<ProspectUsageResponse> usages = new ArrayList<>();

    @ApiModelProperty(value = "The broadband technology")
    private String broadbandTechnology;
}
