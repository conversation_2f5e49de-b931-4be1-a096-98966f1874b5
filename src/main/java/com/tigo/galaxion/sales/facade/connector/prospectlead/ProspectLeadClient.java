package com.tigo.galaxion.sales.facade.connector.prospectlead;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.request.ProspectLeadRequest;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectCustomersResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponseByEntity;
import com.fasterxml.jackson.databind.JsonNode;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

@FeignClient(value = "tigo-prospectlead-service", url = "${environment.url.tigo-prospectlead-service}", configuration = {
                ProspectLeadErrorDecoder.class })
@RequestMapping("/api/v1/prospectLead")
public interface ProspectLeadClient {

        @GetMapping("/{prospectId}/all")
        ProspectLeadResponse getAllProspectEntities(@PathVariable("prospectId") String prospectId);

        @GetMapping("/{prospectId}")
        ProspectLeadResponseByEntity getProspectEntityByEntityType(@PathVariable("prospectId") String prospectId,
                        @RequestParam(value = "entity") String entity,
                        @RequestParam(value = "entityType") Integer entityType);

        @PatchMapping("/{prospectId}/all")
        @ResponseStatus(value = HttpStatus.NO_CONTENT)
        void updateAllProspectEntities(@PathVariable("prospectId") String prospectId,
                        @RequestBody ProspectLeadRequest request);

        @PatchMapping("/{prospectId}")
        @ResponseStatus(value = HttpStatus.NO_CONTENT)
        void updateProspectEntyByEntityType(@PathVariable("prospectId") String prospectId,
                        @RequestParam(value = "entity") String entity,
                        @RequestBody JsonNode request);

        @GetMapping("/{documentType}/{documentId}/allCustomer")
        ProspectCustomersResponse getProspectLeadCustomer(@PathVariable String documentType, @PathVariable String documentId);
        
}
