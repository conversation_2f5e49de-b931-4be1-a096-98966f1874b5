package com.tigo.galaxion.sales.facade.connector.authenticator.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class AuthenticatorBackendError {

	@JsonProperty("errorHTTCode")
    private String errorHTTCode;
    
    @JsonProperty("errorCode")
    private String errorCode;
    
    @JsonProperty("errorType")
    private String errorType;
    
    @JsonProperty("errorDescription")
    private String errorDescription;
}
