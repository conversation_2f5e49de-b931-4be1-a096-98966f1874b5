package com.tigo.galaxion.sales.facade.services;

import java.util.Base64;

import com.tigo.galaxion.sales.facade.connector.otpManagement.client.OtpClient;
import com.tigo.galaxion.sales.facade.connector.otpManagement.request.OtpRequest;
import com.tigo.galaxion.sales.facade.connector.otpManagement.request.OtpVerificationRequest;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.OtpResponse;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.OtpVerificationResponse;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import org.springframework.web.server.ResponseStatusException;

@Service
@RequiredArgsConstructor
public class OtpManagementService {

    private static final Logger logger = LoggerFactory.getLogger(OtpManagementService.class);
    private final OtpClient otpClient;

    @Value("${management-otp.length}")
    private String length;
    @Value("${management-otp.connect.email}")
    private String connectionEmailOtp;
    @Value("${management-otp.ttl}")
    private String ttl;
    @Value("${management-otp.username}")
    private String otpUsername;
    @Value("${management-otp.password}")
    private String otpPassword;


    public OtpResponse sendCodeOtp(OtpRequest otpRequest) throws Exception {
        if (otpRequest.getEmail() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Email Required");
        }
        if (!otpRequest.getEmail().contains("@")) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Format email not valid");
        }

        otpRequest.setConnection(connectionEmailOtp);
        return otpClient.createOtpSession(length, getToken(), otpRequest);
    }

    public OtpVerificationResponse validateCode(String otp, String uuid) throws Exception {
        OtpVerificationRequest otpVerificationRequest = new OtpVerificationRequest();

        if (!otp.isEmpty()) {
            if (!uuid.isEmpty()) {
                otpVerificationRequest.setOtp(otp);
                otpVerificationRequest.setUuid(uuid);
            }
        }
        return otpClient.verifyOtpSession(getToken(), otpVerificationRequest);
    }

    private String getToken() {
        String basicAuthCredentials = otpUsername + ":" + otpPassword;
        String basicAuthToken = "Basic " + Base64.getEncoder().encodeToString(basicAuthCredentials.getBytes());
        return basicAuthToken;
    }

    @CacheEvict(value = "otp-token")
    @Scheduled(fixedRateString = "${management-otp.ttl}")
    public void clearCache() {
        logger.info("Cache Clear after {} seconds", ttl);
    }
}