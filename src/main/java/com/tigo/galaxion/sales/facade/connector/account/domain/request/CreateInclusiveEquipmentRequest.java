package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import java.util.List;

import javax.validation.Valid;

import com.tigo.galaxion.sales.facade.connector.account.domain.enums.InclusiveEquipmentsDomains;

import lombok.Data;

@Data
public class CreateInclusiveEquipmentRequest {
	@Valid
	List<InclusiveEquipmentsDomains> domains;
	@Valid
	List<CreateInclusiveEquipmentV3Request> equipments;
}
