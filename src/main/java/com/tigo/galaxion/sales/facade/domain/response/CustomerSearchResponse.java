package com.tigo.galaxion.sales.facade.domain.response;

import java.util.Date;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@Getter
@ToString
@Builder
@NoArgsConstructor
public class CustomerSearchResponse {
    @NotNull
    @ApiModelProperty(value = "Customer credential type, length of 255 caracters alphabetic.", example = "Pasaporte")
    private String type;

    @NotNull
    @ApiModelProperty(value = "Number associated with the type of customer credential, maximum length of 25 caracters alphabetic.", example = "2177258210203")
    private String identifier;

    @NotNull
    @ApiModelProperty(value = "length of 255 caracters alphabetic.", example = "María Guadalupe")
    private String first_name;

    @NotNull
    @ApiModelProperty(value = "length of 255 caracters alphabetic.", example = "Gutierrez")
    private String last_name;

    @NotNull
    @ApiModelProperty(value = "Expiration date Customer credential.", example = "2025-04-03")
    private Date expiration_date;

    @NotNull
    @ApiModelProperty(value = "E-mail customer, maximum length of 255 caracters alphabetic.", example = "<EMAIL>")
    private String email;

    @NotNull
    @ApiModelProperty(value = "Phone number customer, maximum length of 255 caracters numeric.", example = "*************")
    private String phone_number;

    @NotNull
    @ApiModelProperty(value = "Account id customer, maximum length of 255 caracters numeric.", example = "*********")
    private String account_id;

}
