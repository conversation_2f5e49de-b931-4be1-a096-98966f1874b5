package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.collection.domain.problem.CollectionProblem;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellEligibilityRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellOfferTypeEligibilityResponse;
import com.tigo.galaxion.sales.facade.domain.problem.AccountInCollectionProblem;
import com.tigo.galaxion.sales.facade.domain.response.EligibilityResponse;
import com.tigo.galaxion.sales.facade.domain.response.OfferTypeEligibilityResponse;
import com.tigo.galaxion.sales.facade.helper.EligibilityCauseHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.tigo.galaxion.sales.facade.mapper.dto.EligibilityResponseMapper.buildEligibilityResponses;
import static com.tigo.galaxion.sales.facade.mapper.dto.EligibilityResponseMapper.buildOfferTypesEligibilityResponses;

@Service
@RequiredArgsConstructor
public class CrossSellService {

    private final CrossSellClient crossSellClient;
    private final EligibilityService eligibilityService;

    public List<OfferTypeEligibilityResponse> getOfferTypesEligibility(String accountId) {
        List<CrossSellOfferTypeEligibilityResponse> offerTypesEligibility = crossSellClient.getOfferTypesEligibility(accountId);
        try {
            eligibilityService.checkAccountIsInCollection(accountId);
        } catch (AccountInCollectionProblem | CollectionProblem p) {
            offerTypesEligibility.stream()
                    .filter(s -> "POSTPAY".equals(s.getOfferType()))
                    .findFirst().ifPresent(s -> {
                        s.addCause(EligibilityCauseHelper.reformatProblemTitleToCause(p.getTitle()));
                        s.setEligible(false);
                    });

        }
        return buildOfferTypesEligibilityResponses(offerTypesEligibility);
    }

    public List<EligibilityResponse> getServiceGroupsEligibility(String accountId, Long subscriptionId, String crossSellReference) {
        var eligibilityRequest = CrossSellEligibilityRequest
                .builder()
                .crossSellReference(crossSellReference)
                .parentSubscriptionId(subscriptionId)
                .build();
        var serviceGroupsEligibility = crossSellClient.getServiceGroupsEligibility(accountId, eligibilityRequest);
        return buildEligibilityResponses(serviceGroupsEligibility);
    }
}
