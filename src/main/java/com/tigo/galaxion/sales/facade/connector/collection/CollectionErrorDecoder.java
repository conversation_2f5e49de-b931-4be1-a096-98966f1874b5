package com.tigo.galaxion.sales.facade.connector.collection;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.collection.domain.problem.CollectionProblem;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;
import org.zalando.problem.ThrowableProblem;

public class CollectionErrorDecoder extends BaseProblemErrorDecoder {

    public CollectionErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "collection-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        return new CollectionProblem(throwableProblem);
    }

}
