package com.tigo.galaxion.sales.facade.domain.request;

import com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CreditScoreRequest {

    @NotBlank
    @ApiModelProperty(value = "The document ID.", required = true, example = "0123456789")
    private String documentId;

    @NotNull
    @ApiModelProperty(value = "The document type.", required = true, example = "NIT")
    private IdDocumentTypeEnum documentType;

    @NotBlank
    @ApiModelProperty(value = "The nationality.", required = true, example = "CO")
    private String nationality;
}
