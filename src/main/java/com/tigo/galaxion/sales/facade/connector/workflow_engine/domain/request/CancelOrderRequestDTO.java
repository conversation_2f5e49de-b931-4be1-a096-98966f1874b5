package com.tigo.galaxion.sales.facade.connector.workflow_engine.domain.request;

import lombok.Getter;
import mc.monacotelecom.workflow.base.enumeration.ProcessType;

import javax.validation.constraints.NotNull;

@Getter
public class CancelOrderRequestDTO {

    private @NotNull ProcessType type;
    private @NotNull String orderId;

    public CancelOrderRequestDTO(ProcessType type, String orderId) {
        this.type = type;
        this.orderId = orderId;
    }
}
