package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.order.domain.OrderClient;
import com.tigo.galaxion.sales.facade.connector.order.domain.response.GetOrdersResponse;
import com.tigo.galaxion.sales.facade.connector.order.domain.response.OrderResponse;
import com.tigo.galaxion.sales.facade.connector.order.service.ServiceClient;
import com.tigo.galaxion.sales.facade.connector.order.service.response.EntityResponse;
import com.tigo.galaxion.sales.facade.connector.order.service.response.OrderServiceResponse;
import com.tigo.galaxion.sales.facade.connector.order.service.response.ServiceResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.OrderStateEnum;
import com.tigo.galaxion.sales.facade.domain.problem.ValidDateProblem;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class OrderService {

    private final OrderClient orderClient;
    private final ServiceClient serviceClient;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderService.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Value("${productOrders.user}")
    private String user = "";

    @Value("${productOrders.header}")
    private String header = "";

    public Page<GetOrdersResponse> getOrdersByClient(Integer page, Integer size, Long msisdn, String start, String end) {
        try {

            validDate(start, end);

            List<OrderResponse> clientResponse = orderClient.getOrder(header, msisdn, user, start, end, page, size);

            List<GetOrdersResponse> mappedResponse = clientResponse.stream()
                    .map(this::mapToGetOrdersResponse)
                    .collect(Collectors.toList());

            Pageable pageable = PageRequest.of(page, size);
            int startPage = Math.min((int) pageable.getOffset(), mappedResponse.size());
            int endPage = Math.min(startPage + pageable.getPageSize(), mappedResponse.size());

            if (startPage >= mappedResponse.size()) {
                return new PageImpl<>(mappedResponse, pageable, mappedResponse.size());
            }

            List<GetOrdersResponse> paginatedList = mappedResponse.subList(startPage, endPage);

            return new PageImpl<>(paginatedList, pageable, mappedResponse.size());


        } catch (DateTimeParseException e) {
            throw new ValidDateProblem("StartDate: %s is after EndDate: %s", "orders", start, end);
        }
    }

    public OrderServiceResponse getServicesByClient(Integer page, Integer size, Long msisdn, String start, String end) {
        try {

            validDate(start, end);

            ServiceResponse clientResponse = serviceClient.getOrder(header, msisdn, start, end, page, size);

            return mapToOrdersResponse(clientResponse);

        } catch (DateTimeParseException e) {
            throw new ValidDateProblem("StartDate: %s is after EndDate: %s", "orders", start, end);
        }
    }

    private OrderServiceResponse mapToOrdersResponse(ServiceResponse clientResponse) {
        List<GetOrdersResponse> mappedEntities = clientResponse.getEntities().stream()
                .map(this::mapToGetServiceResponse)
                .collect(Collectors.toList());

        return OrderServiceResponse.builder()
                .page(clientResponse.getPage())
                .size(clientResponse.getSize())
                .total(clientResponse.getTotal())
                .content(mappedEntities)
                .build();
    }

    public GetOrdersResponse mapToGetServiceResponse(EntityResponse orderResponse) {
        return GetOrdersResponse.builder()
                .orderId(orderResponse.getId())
                .creationDate(orderResponse.getCreationDate())
                .completionDate(orderResponse.getCompletionDate())
                .lifecycleState(mapToOrderState(orderResponse.getState()))
                .orderType("purchaseTigoX" + orderResponse.getType())
                .relatedParty(orderResponse.getRelatedParty() != null && !orderResponse.getRelatedParty().isEmpty() &&
                        orderResponse.getRelatedParty().get(0).getPartyOrPartyRole() != null ?
                        orderResponse.getRelatedParty().get(0).getPartyOrPartyRole().getName() : null)
                .channel(orderResponse.getChannel() != null && !orderResponse.getChannel().isEmpty() &&
                        orderResponse.getChannel().get(0).getChannel() != null ?
                        orderResponse.getChannel().get(0).getChannel().getName() : null)
                .build();
    }

    public GetOrdersResponse mapToGetOrdersResponse(OrderResponse orderResponse) {
        return GetOrdersResponse.builder()
                .orderId(orderResponse.getId())
                .creationDate(orderResponse.getCreationDate())
                .completionDate(orderResponse.getCompletionDate())
                .lifecycleState(mapToOrderState(orderResponse.getState()))
                .orderType("purchaseTigoX" + orderResponse.getType())
                .relatedParty(orderResponse.getRelatedParty() != null && !orderResponse.getRelatedParty().isEmpty() ?
                        orderResponse.getRelatedParty().get(0).getPartyOrPartyRole().getName() : null)
                .channel(orderResponse.getChannel() != null && !orderResponse.getChannel().isEmpty() ?
                        orderResponse.getChannel().get(0).getChannel().getName() : null)
                .build();
    }

    private void validDate(String start, String end) {
        LocalDate startDate = LocalDate.parse(start, DATE_FORMATTER);
        LocalDate endDate = LocalDate.parse(end, DATE_FORMATTER);

        LOGGER.debug("start: {} end: {}", startDate, endDate);

        if (startDate.isAfter(endDate)) {
            throw new DateTimeParseException("start date is after end date", "", 0);
        }
    }

    private String mapToOrderState(String state) {
        try {
            return OrderStateEnum.fromString(state).getState();
        } catch (IllegalArgumentException e) {
            return "UNKNOWN";
        }
    }
}
