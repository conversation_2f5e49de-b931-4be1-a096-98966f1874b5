package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

import java.time.LocalDate;

public class IdentityDocumentAlreadyExistsProblem extends AbstractThrowableProblem {

    public IdentityDocumentAlreadyExistsProblem(String firstname,
                                                String lastname,
                                                String email,
                                                LocalDate birthdate) {
        super(null,
              "identity-document-already-exists",
              Status.BAD_REQUEST,
              String.format("This document already exist in galaxion with : [firstname : %s, lastname : %s, email : %s, birthdate : %s].", firstname, lastname, email, birthdate));
    }

}
