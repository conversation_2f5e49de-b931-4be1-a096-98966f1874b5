package com.tigo.galaxion.sales.facade.connector.evident.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.GenerationResultGenerate;
import com.tigo.galaxion.sales.facade.connector.evident.domain.QuestionnaireResponse;
import com.tigo.galaxion.sales.facade.connector.evident.domain.ValidationResult;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentGenerateResponseBody {
    
    @JsonProperty("validationResult")
    private ValidationResult validationResult; 

    @JsonProperty("generationResult")
    private GenerationResultGenerate generationResult; 

    @JsonProperty("questionnaireData")
    private QuestionnaireResponse questionnaireData;

}
