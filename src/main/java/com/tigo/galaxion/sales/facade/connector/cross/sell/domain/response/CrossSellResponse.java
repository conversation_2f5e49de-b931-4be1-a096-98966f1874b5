package com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.PaymentSettingsResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.OfferTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@Getter
@ToString
@SuperBuilder
@NoArgsConstructor
public class CrossSellResponse {

    private String reference;
    private OfferTypeEnum offerType;
    private String brand;
    private String channelCode;
    private String channelGroup;
    private String customerType;
    private String createdBy;
    private String creditScore;
    private String cartUuid;
    private String status;
    private String accountId;
    private PaymentSettingsResponse paymentSettings;
    private String contactDeliveryUuid;
}
