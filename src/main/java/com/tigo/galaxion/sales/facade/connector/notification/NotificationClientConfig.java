package com.tigo.galaxion.sales.facade.connector.notification;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class NotificationClientConfig {
    @Bean
    public RequestInterceptor requestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                // Add any custom headers or configurations here
                template.header("galaxion-user-identifier", "SYSTEM");
                template.header("galaxion-user-type", "SYSTEM");
            }
        };
    }
}
