package com.tigo.galaxion.sales.facade.connector.credit.score.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCreditScoreRequest {

    @NotBlank
    @ApiModelProperty(value = "The contact uuid", required = true, example = "162d67-e690-4268-9f77-f5523")
    private String contactUuid;

    @NotBlank
    @ApiModelProperty(value = "The credit score.", required = true, example = "2")
    private String creditScore;

}
