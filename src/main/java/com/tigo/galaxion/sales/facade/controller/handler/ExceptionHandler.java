package com.tigo.galaxion.sales.facade.controller.handler;

import org.springframework.boot.info.BuildProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.NativeWebRequest;
import org.zalando.problem.Problem;
import org.zalando.problem.ProblemBuilder;
import org.zalando.problem.StatusType;
import org.zalando.problem.ThrowableProblem;
import org.zalando.problem.spring.web.advice.ProblemHandling;
import org.zalando.problem.spring.web.advice.security.SecurityAdviceTrait;
import org.zalando.problem.violations.ConstraintViolationProblem;

import java.net.URI;
import java.util.Optional;

@ControllerAdvice
public class ExceptionHandler implements ProblemHandling, SecurityAdviceTrait {

    private static final String CONTACT_CUSTOMER_SERVICE_MESSAGE = "An error has occurred, please try again later or contact our customer service.";

    private final BuildProperties buildProperties;

    public ExceptionHandler(BuildProperties buildProperties) {
        this.buildProperties = buildProperties;
    }

    @Override
    public ProblemBuilder prepare(Throwable throwable, StatusType status, URI type) {
        var problemBuilder = Problem.builder()
                                    .withType(type)
                                    .withTitle(status.getReasonPhrase())
                                    .withStatus(status);
        int statusCode = status.getStatusCode();
        if (statusCode == HttpStatus.BAD_REQUEST.value() || statusCode == HttpStatus.FORBIDDEN.value()) {
            problemBuilder.withDetail(throwable.getMessage());
        } else {
            problemBuilder.withDetail(CONTACT_CUSTOMER_SERVICE_MESSAGE);
        }
        problemBuilder.with("sources", getSourcesParameter(throwable));

        return problemBuilder;
    }

    @Override
    public ResponseEntity<Problem> create(Throwable throwable, Problem problem, NativeWebRequest request, HttpHeaders headers) {
        var problemBuilder = Problem.builder()
                                    .withDetail(problem.getDetail())
                                    .withInstance(problem.getInstance())
                                    .withStatus(problem.getStatus())
                                    .withTitle(problem.getTitle())
                                    .withType(problem.getType());

        problem.getParameters().forEach(problemBuilder::with);
        problemBuilder.with("sources", getSourcesParameter(throwable));
        addViolations(problem, problemBuilder);
        return ProblemHandling.super.create(throwable, problemBuilder.build(), request, headers);
    }

    private String getSourcesParameter(Throwable throwable) {
        return getExistingSources(throwable).map(existingSources -> buildProperties.getName() + " -> " + existingSources)
                                            .orElse(buildProperties.getName());
    }

    private Optional<String> getExistingSources(Throwable throwable) {
        Optional<String> optionalSources = Optional.empty();
        if (throwable instanceof ThrowableProblem) {
            Object sources = ((ThrowableProblem) throwable).getParameters().get("sources");
            if (sources != null) {
                optionalSources = Optional.of(sources.toString());
            }
        }
        return optionalSources;
    }

    private void addViolations(Problem problem, ProblemBuilder problemBuilder) {
        if (problem instanceof ConstraintViolationProblem) {
            var violations = ((ConstraintViolationProblem) problem).getViolations();
            problemBuilder.with("violations", violations);
        }
    }
}
