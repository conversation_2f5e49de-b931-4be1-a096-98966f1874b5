package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

@Getter
@SuperBuilder(toBuilder = true)
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoCartResponse {

    @ApiModelProperty(value = "The cart uuid", example = "0SJ9VPBZ", required = true)
    private String uuid;

    @ApiModelProperty(value = "The cart's credit score", required = true, example = "2")
    private String creditScore;

    @ApiModelProperty(value = "Terms and conditions accepted", required = true, example = "true")
    private Boolean agreedTermsAndConditions;

    @ApiModelProperty(value = "The amount to be paid vat included", required = true)
    private TigoAmountResponse amountVatIncluded;

    @ApiModelProperty(value = "The amount to not be paid vat excluded", required = false)
    private TigoAmountResponse amountVatExcluded;

    @ToString.Exclude
    @Builder.Default
    @ApiModelProperty(value = "The list of offers in the cart", required = true)
    private List<TigoOfferResponse> offers = new ArrayList<>();

    @ApiModelProperty(value = "The cart's channel group", required = true, example = "RETAIL")
    private String channelGroup;

    @ToString.Exclude
    @Builder.Default
    @ApiModelProperty(value = "The number of additional allowed subscriptions", required = true)
    private List<TigoAdditionalAllowedOfferResponse> numberAdditionalAllowedOffers = new ArrayList<>();

    @ApiModelProperty(value = "Recurring amounts by occurrence")
    @Builder.Default
    private List<TigoRecurringAmountByOccurrenceResponse> recurringAmountsByOccurrence = new ArrayList<>();

}
