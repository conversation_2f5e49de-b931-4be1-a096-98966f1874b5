package com.tigo.galaxion.sales.facade.model.entity;

import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serial;
import java.io.Serializable;

@Embeddable
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class ContactAddressId implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Column(name = "reference", nullable = false)
    private String reference;

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private AddressTypeEnum type;
}
