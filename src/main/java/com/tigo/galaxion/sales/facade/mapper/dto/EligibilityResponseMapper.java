package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellEligibilityResponse;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellOfferTypeEligibilityResponse;
import com.tigo.galaxion.sales.facade.domain.response.EligibilityResponse;
import com.tigo.galaxion.sales.facade.domain.response.OfferTypeEligibilityResponse;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;

import java.util.List;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public final class EligibilityResponseMapper {

    public static EligibilityResponse buildEligibilityResponse(CrossSellEligibilityResponse crossSellEligibilityResponse) {
        return EligibilityResponse
                .builder()
                .eligible(crossSellEligibilityResponse.isEligible())
                .serviceGroup(crossSellEligibilityResponse.getServiceGroup())
                .causes(crossSellEligibilityResponse.getCauses())
                .build();
    }

    public static List<EligibilityResponse> buildEligibilityResponses(List<CrossSellEligibilityResponse> crossSellEligibilityResponses) {
        return crossSellEligibilityResponses
                .stream()
                .map(EligibilityResponseMapper::buildEligibilityResponse)
                .toList();
    }

    private static OfferTypeEligibilityResponse buildOfferTypesEligibilityResponse(CrossSellOfferTypeEligibilityResponse crossSellEligibilityResponse) {
        return OfferTypeEligibilityResponse
                .builder()
                .eligible(crossSellEligibilityResponse.isEligible())
                .offerType(crossSellEligibilityResponse.getOfferType())
                .causes(crossSellEligibilityResponse.getCauses())
                .build();
    }

    public static List<OfferTypeEligibilityResponse> buildOfferTypesEligibilityResponses(List<CrossSellOfferTypeEligibilityResponse> crossSellEligibilityResponses) {
        return crossSellEligibilityResponses
                .stream()
                .map(EligibilityResponseMapper::buildOfferTypesEligibilityResponse)
                .toList();
    }
}
