package com.tigo.galaxion.sales.facade.connector.notification.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class NotificationRequest {
    
    @JsonProperty("emailNotification")
    private boolean emailNotification;
    
    @JsonProperty("externalReference")
    private String externalReference;
    
    @JsonProperty("locale")
    private String locale;
    
    @JsonProperty("reference")
    private String reference;
    
    @JsonProperty("smsNotification")
    private boolean smsNotification;
    
    @JsonProperty("variables")
    private VariablesNotification variables;
    
    @JsonProperty("webNotification")
    private boolean webNotification;
}
