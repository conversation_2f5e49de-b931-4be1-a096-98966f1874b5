package com.tigo.galaxion.sales.facade.services.order_notification;

import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointment;
import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointmentWFE;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.app.dto.VariableUpdateDTO;
import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;
import mc.monacotelecom.workflow.base.enumeration.ProcessType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ConfirmAppointmentService {
    @Value("${wfe.signal.SP_WAIT_BB_APPOINTMENT_REQUEST}")
    private String signalName;

    private final WorkflowEngineClient workflowEngineClient;

    public String sendNotification(String orderID, FieldServiceConfirmAppointmentWFE request)
            throws Exception {
        log.info("Create tecrep equipment: {}", orderID);
        workflowEngineClient.updateConnectorData(orderID,
                VariableUpdateDTO.builder()
                        .type(ProcessType.COM)
                        .data(request)
                        .variable("fieldservice")
                        .build());

        log.info("send signal from: {} to signalName: {}", orderID, signalName);

        var trackingNumber = UUID.randomUUID().toString();

        workflowEngineClient.processSignal(WorkflowSignalRequestDTO.builder()
                .name(signalName)
                .orderId(orderID)
                .context(Map.of("callId", orderID,
                        "customer", request.getContactUuid()))
                .build());

        return trackingNumber;
    }
}
