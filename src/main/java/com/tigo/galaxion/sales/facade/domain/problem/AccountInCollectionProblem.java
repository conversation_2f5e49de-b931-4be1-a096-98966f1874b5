package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class AccountInCollectionProblem extends AbstractThrowableProblem {

    public AccountInCollectionProblem(String account) {
        super(null,
                "account-in-collection",
                Status.BAD_REQUEST,
                String.format("Account #%s is in collection", account)
        );
    }

}
