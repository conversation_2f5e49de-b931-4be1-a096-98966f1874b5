package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.contact.AcquisitionPermissionGroupListResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.contact.AcquisitionPermissionGroupResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.contact.AcquisitionPermissionResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoPermissionGroupListResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoPermissionGroupResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoPermissionResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class
TigoPermissionResponseMapper {

    private static List<TigoPermissionResponse> buildTigoAcquisitionPermissionResponses(List<AcquisitionPermissionResponse> responses) {
        return responses.stream().map(TigoPermissionResponseMapper::buildTigoAcquisitionPermissionResponse).toList();
    }

    private static TigoPermissionResponse buildTigoAcquisitionPermissionResponse(AcquisitionPermissionResponse response) {
        return TigoPermissionResponse.builder()
                                     .permission(response.getPermission())
                                     .name(response.getName())
                                     .enabled(response.isEnabled())
                                     .build();
    }

    private static TigoPermissionGroupResponse buildTigoAcquisitionPermissionGroupListResponse(AcquisitionPermissionGroupResponse response) {
        return TigoPermissionGroupResponse.builder()
                                          .name(response.getName())
                                          .permissionGroup(response.getPermissionGroup())
                                          .permissions(buildTigoAcquisitionPermissionResponses(response.getPermissions()))
                                          .build();
    }

    private static List<TigoPermissionGroupResponse> buildTigoAcquisitionPermissionGroupListResponses(List<AcquisitionPermissionGroupResponse> responses) {
        return responses.stream().map(TigoPermissionResponseMapper::buildTigoAcquisitionPermissionGroupListResponse).toList();
    }

    public static TigoPermissionGroupListResponse buildTigoAcquisitionPermissionGroupListResponse(AcquisitionPermissionGroupListResponse response) {
        return TigoPermissionGroupListResponse.builder()
                                              .allowThirdParty(response.isAllowThirdParty())
                                              .permissionGroups(buildTigoAcquisitionPermissionGroupListResponses(response.getPermissionGroups()))
                                              .build();
    }
}
