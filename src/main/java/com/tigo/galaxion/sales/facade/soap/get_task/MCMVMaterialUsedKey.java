
package com.tigo.galaxion.sales.facade.soap.get_task;

import javax.xml.bind.annotation.*;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for MCMVMaterialUsedKey complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="MCMVMaterialUsedKey"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="attributes" type="{http://crmsaleforce.resourcemanager.millicom.com/gettasksoap}Attributes"/&gt;
 *         &lt;element name="Id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsDeleted" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="ProductConsumedNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CreatedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="CreatedById" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LastModifiedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="LastModifiedById" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SystemModstamp" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="LastViewedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="LastReferencedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="WorkOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="WorkOrderLineItemId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ProductItemId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PricebookEntryId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Product2Id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ProductName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="QuantityUnitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="QuantityConsumed" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *         &lt;element name="UnitPrice" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="Description" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Discount" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ListPrice" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *         &lt;element name="TotalPrice" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="Subtotal" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="IsConsumed" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="MCExternalRefID__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCAreaOperativa__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCSerialNumber__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCountry__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCServiceResource__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCProduct2__c" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MCMVMaterialUsedKey", propOrder = {
    "attributes",
    "id",
    "isDeleted",
    "productConsumedNumber",
    "createdDate",
    "createdById",
    "lastModifiedDate",
    "lastModifiedById",
    "systemModstamp",
    "lastViewedDate",
    "lastReferencedDate",
    "workOrderId",
    "workOrderLineItemId",
    "productItemId",
    "pricebookEntryId",
    "product2Id",
    "productName",
    "quantityUnitOfMeasure",
    "quantityConsumed",
    "unitPrice",
    "description",
    "discount",
    "listPrice",
    "totalPrice",
    "subtotal",
    "isConsumed",
    "mcExternalRefIDC",
    "mcAreaOperativaC",
    "mcSerialNumberC",
    "mcCountryC",
    "mcServiceResourceC",
    "mcProduct2C"
})
public class MCMVMaterialUsedKey {

    @XmlElement(required = true)
    protected Attributes attributes;
    @XmlElement(name = "Id", required = true)
    protected String id;
    @XmlElement(name = "IsDeleted")
    protected boolean isDeleted;
    @XmlElement(name = "ProductConsumedNumber", required = true)
    protected String productConsumedNumber;
    @XmlElement(name = "CreatedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar createdDate;
    @XmlElement(name = "CreatedById", required = true)
    protected String createdById;
    @XmlElement(name = "LastModifiedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastModifiedDate;
    @XmlElement(name = "LastModifiedById", required = true)
    protected String lastModifiedById;
    @XmlElement(name = "SystemModstamp", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar systemModstamp;
    @XmlElement(name = "LastViewedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastViewedDate;
    @XmlElement(name = "LastReferencedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastReferencedDate;
    @XmlElement(name = "WorkOrderId", required = true)
    protected String workOrderId;
    @XmlElement(name = "WorkOrderLineItemId", required = true)
    protected String workOrderLineItemId;
    @XmlElement(name = "ProductItemId", required = true)
    protected String productItemId;
    @XmlElement(name = "PricebookEntryId", required = true)
    protected String pricebookEntryId;
    @XmlElement(name = "Product2Id", required = true)
    protected String product2Id;
    @XmlElement(name = "ProductName", required = true)
    protected String productName;
    @XmlElement(name = "QuantityUnitOfMeasure", required = true)
    protected String quantityUnitOfMeasure;
    @XmlElement(name = "QuantityConsumed")
    protected double quantityConsumed;
    @XmlElement(name = "UnitPrice")
    protected long unitPrice;
    @XmlElement(name = "Description", required = true)
    protected String description;
    @XmlElement(name = "Discount", required = true)
    protected String discount;
    @XmlElement(name = "ListPrice")
    protected double listPrice;
    @XmlElement(name = "TotalPrice")
    protected long totalPrice;
    @XmlElement(name = "Subtotal")
    protected long subtotal;
    @XmlElement(name = "IsConsumed")
    protected boolean isConsumed;
    @XmlElement(name = "MCExternalRefID__c", required = true)
    protected String mcExternalRefIDC;
    @XmlElement(name = "MCAreaOperativa__c", required = true)
    protected String mcAreaOperativaC;
    @XmlElement(name = "MCSerialNumber__c", required = true)
    protected String mcSerialNumberC;
    @XmlElement(name = "MCCountry__c", required = true)
    protected String mcCountryC;
    @XmlElement(name = "MCServiceResource__c", required = true)
    protected String mcServiceResourceC;
    @XmlElement(name = "MCProduct2__c", required = true)
    protected String mcProduct2C;

    /**
     * Gets the value of the attributes property.
     * 
     * @return
     *     possible object is
     *     {@link Attributes }
     *     
     */
    public Attributes getAttributes() {
        return attributes;
    }

    /**
     * Sets the value of the attributes property.
     * 
     * @param value
     *     allowed object is
     *     {@link Attributes }
     *     
     */
    public void setAttributes(Attributes value) {
        this.attributes = value;
    }

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setId(String value) {
        this.id = value;
    }

    /**
     * Gets the value of the isDeleted property.
     * 
     */
    public boolean isIsDeleted() {
        return isDeleted;
    }

    /**
     * Sets the value of the isDeleted property.
     * 
     */
    public void setIsDeleted(boolean value) {
        this.isDeleted = value;
    }

    /**
     * Gets the value of the productConsumedNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProductConsumedNumber() {
        return productConsumedNumber;
    }

    /**
     * Sets the value of the productConsumedNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProductConsumedNumber(String value) {
        this.productConsumedNumber = value;
    }

    /**
     * Gets the value of the createdDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getCreatedDate() {
        return createdDate;
    }

    /**
     * Sets the value of the createdDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setCreatedDate(XMLGregorianCalendar value) {
        this.createdDate = value;
    }

    /**
     * Gets the value of the createdById property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCreatedById() {
        return createdById;
    }

    /**
     * Sets the value of the createdById property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreatedById(String value) {
        this.createdById = value;
    }

    /**
     * Gets the value of the lastModifiedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastModifiedDate() {
        return lastModifiedDate;
    }

    /**
     * Sets the value of the lastModifiedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastModifiedDate(XMLGregorianCalendar value) {
        this.lastModifiedDate = value;
    }

    /**
     * Gets the value of the lastModifiedById property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLastModifiedById() {
        return lastModifiedById;
    }

    /**
     * Sets the value of the lastModifiedById property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastModifiedById(String value) {
        this.lastModifiedById = value;
    }

    /**
     * Gets the value of the systemModstamp property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getSystemModstamp() {
        return systemModstamp;
    }

    /**
     * Sets the value of the systemModstamp property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setSystemModstamp(XMLGregorianCalendar value) {
        this.systemModstamp = value;
    }

    /**
     * Gets the value of the lastViewedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastViewedDate() {
        return lastViewedDate;
    }

    /**
     * Sets the value of the lastViewedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastViewedDate(XMLGregorianCalendar value) {
        this.lastViewedDate = value;
    }

    /**
     * Gets the value of the lastReferencedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastReferencedDate() {
        return lastReferencedDate;
    }

    /**
     * Sets the value of the lastReferencedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastReferencedDate(XMLGregorianCalendar value) {
        this.lastReferencedDate = value;
    }

    /**
     * Gets the value of the workOrderId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkOrderId() {
        return workOrderId;
    }

    /**
     * Sets the value of the workOrderId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkOrderId(String value) {
        this.workOrderId = value;
    }

    /**
     * Gets the value of the workOrderLineItemId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkOrderLineItemId() {
        return workOrderLineItemId;
    }

    /**
     * Sets the value of the workOrderLineItemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkOrderLineItemId(String value) {
        this.workOrderLineItemId = value;
    }

    /**
     * Gets the value of the productItemId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProductItemId() {
        return productItemId;
    }

    /**
     * Sets the value of the productItemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProductItemId(String value) {
        this.productItemId = value;
    }

    /**
     * Gets the value of the pricebookEntryId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPricebookEntryId() {
        return pricebookEntryId;
    }

    /**
     * Sets the value of the pricebookEntryId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPricebookEntryId(String value) {
        this.pricebookEntryId = value;
    }

    /**
     * Gets the value of the product2Id property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProduct2Id() {
        return product2Id;
    }

    /**
     * Sets the value of the product2Id property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProduct2Id(String value) {
        this.product2Id = value;
    }

    /**
     * Gets the value of the productName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProductName() {
        return productName;
    }

    /**
     * Sets the value of the productName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProductName(String value) {
        this.productName = value;
    }

    /**
     * Gets the value of the quantityUnitOfMeasure property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQuantityUnitOfMeasure() {
        return quantityUnitOfMeasure;
    }

    /**
     * Sets the value of the quantityUnitOfMeasure property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQuantityUnitOfMeasure(String value) {
        this.quantityUnitOfMeasure = value;
    }

    /**
     * Gets the value of the quantityConsumed property.
     * 
     */
    public double getQuantityConsumed() {
        return quantityConsumed;
    }

    /**
     * Sets the value of the quantityConsumed property.
     * 
     */
    public void setQuantityConsumed(double value) {
        this.quantityConsumed = value;
    }

    /**
     * Gets the value of the unitPrice property.
     * 
     */
    public long getUnitPrice() {
        return unitPrice;
    }

    /**
     * Sets the value of the unitPrice property.
     * 
     */
    public void setUnitPrice(long value) {
        this.unitPrice = value;
    }

    /**
     * Gets the value of the description property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the discount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDiscount() {
        return discount;
    }

    /**
     * Sets the value of the discount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDiscount(String value) {
        this.discount = value;
    }

    /**
     * Gets the value of the listPrice property.
     * 
     */
    public double getListPrice() {
        return listPrice;
    }

    /**
     * Sets the value of the listPrice property.
     * 
     */
    public void setListPrice(double value) {
        this.listPrice = value;
    }

    /**
     * Gets the value of the totalPrice property.
     * 
     */
    public long getTotalPrice() {
        return totalPrice;
    }

    /**
     * Sets the value of the totalPrice property.
     * 
     */
    public void setTotalPrice(long value) {
        this.totalPrice = value;
    }

    /**
     * Gets the value of the subtotal property.
     * 
     */
    public long getSubtotal() {
        return subtotal;
    }

    /**
     * Sets the value of the subtotal property.
     * 
     */
    public void setSubtotal(long value) {
        this.subtotal = value;
    }

    /**
     * Gets the value of the isConsumed property.
     * 
     */
    public boolean isIsConsumed() {
        return isConsumed;
    }

    /**
     * Sets the value of the isConsumed property.
     * 
     */
    public void setIsConsumed(boolean value) {
        this.isConsumed = value;
    }

    /**
     * Gets the value of the mcExternalRefIDC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCExternalRefIDC() {
        return mcExternalRefIDC;
    }

    /**
     * Sets the value of the mcExternalRefIDC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCExternalRefIDC(String value) {
        this.mcExternalRefIDC = value;
    }

    /**
     * Gets the value of the mcAreaOperativaC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCAreaOperativaC() {
        return mcAreaOperativaC;
    }

    /**
     * Sets the value of the mcAreaOperativaC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCAreaOperativaC(String value) {
        this.mcAreaOperativaC = value;
    }

    /**
     * Gets the value of the mcSerialNumberC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCSerialNumberC() {
        return mcSerialNumberC;
    }

    /**
     * Sets the value of the mcSerialNumberC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCSerialNumberC(String value) {
        this.mcSerialNumberC = value;
    }

    /**
     * Gets the value of the mcCountryC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCountryC() {
        return mcCountryC;
    }

    /**
     * Sets the value of the mcCountryC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCountryC(String value) {
        this.mcCountryC = value;
    }

    /**
     * Gets the value of the mcServiceResourceC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCServiceResourceC() {
        return mcServiceResourceC;
    }

    /**
     * Sets the value of the mcServiceResourceC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCServiceResourceC(String value) {
        this.mcServiceResourceC = value;
    }

    /**
     * Gets the value of the mcProduct2C property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCProduct2C() {
        return mcProduct2C;
    }

    /**
     * Sets the value of the mcProduct2C property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCProduct2C(String value) {
        this.mcProduct2C = value;
    }

}
