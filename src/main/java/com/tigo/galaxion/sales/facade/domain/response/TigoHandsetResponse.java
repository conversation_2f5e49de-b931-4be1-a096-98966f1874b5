package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Set;

@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TigoHandsetResponse extends TigoEquipmentResponse {

    @ApiModelProperty(value = "The handset capacity", required = true, example = "128 Go")
    private String capacity;

    @ApiModelProperty(value = "The handset network compatibility", required = true, example = "4G/5G")
    private String networkCompatibility;

    @ApiModelProperty(value = "The handset sim card types", required = true, example = "[NANO]")
    private Set<String> simCardTypes;

}
