package com.tigo.galaxion.sales.facade.services.field_service.get_task;

import com.tigo.galaxion.sales.facade.domain.request.GetTaskRequest;
import com.tigo.galaxion.sales.facade.soap.get_task.GtTaskRequest;
import com.tigo.galaxion.sales.facade.soap.get_task.GtTaskResponse;
import com.tigo.galaxion.sales.facade.soap.get_task.Task;
import com.tigo.galaxion.sales.facade.soap.get_task.TaskRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;

@Service
@Slf4j
public class GetTaskClient extends WebServiceGatewaySupport {

    public GtTaskResponse getTask(GetTaskRequest body) throws Exception {
        GtTaskRequest request = new GtTaskRequest();
        Task task = new Task();
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setCallID(body.getCallId());
        taskRequest.setNumber(null);
        task.setTask(taskRequest);
        task.setGetAssignment(null);
        request.setGtTask(task);
        return (GtTaskResponse) getWebServiceTemplate().marshalSendAndReceive(request);
    }
}
