package com.tigo.galaxion.sales.facade.connector.alfresco.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)  // Ignora campos desconocidos durante la deserialización
public class AlfrescoErrorResponse {
    @JsonProperty("status")
    private String status; 
    
    @JsonProperty("error")
    private String error;
    
    @JsonProperty("path")
    private String path;
}
