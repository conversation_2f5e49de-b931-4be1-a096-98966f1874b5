package com.tigo.galaxion.sales.facade.connector.config;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.ThrowableProblem;

public abstract class BaseProblem extends AbstractThrowableProblem {

    protected BaseProblem(ThrowableProblem throwableProblem) {
        super(throwableProblem.getType(),
              throwableProblem.getTitle(),
              throwableProblem.getStatus(),
              throwableProblem.getDetail(),
              throwableProblem.getInstance(),
              throwableProblem.getCause(),
              throwableProblem.getParameters());
    }

}
