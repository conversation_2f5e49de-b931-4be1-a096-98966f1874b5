package com.tigo.galaxion.sales.facade.controller;

import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.request.TecnicalFeasibilityRequestBody;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.response.TecnicalFeasibilityResponseBody;
import com.tigo.galaxion.sales.facade.services.TecnicalFeasibilityService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

@Slf4j
@Api
@RestController
@RequiredArgsConstructor
public class TecnicalFeasibilityController {
    
    private final TecnicalFeasibilityService tecnicalFeasibilityService;
    
    @PostMapping("/api/v1/tecnicalFeasibility")
    @ApiOperation("Create a new feasibility")
    @ApiResponses(value = { 
        @ApiResponse( code = 200, message = "Successfuly")
    })
    public TecnicalFeasibilityResponseBody createTecnical(@Valid @RequestBody TecnicalFeasibilityRequestBody request ){
        return tecnicalFeasibilityService.createTecnical(request);
    }
}
