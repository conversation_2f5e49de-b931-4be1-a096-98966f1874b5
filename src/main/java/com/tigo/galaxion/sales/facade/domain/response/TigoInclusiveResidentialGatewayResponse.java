package com.tigo.galaxion.sales.facade.domain.response;

import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Getter
@ApiModel
@SuperBuilder
@NoArgsConstructor
public class TigoInclusiveResidentialGatewayResponse extends TigoInclusiveEquipmentResponse {

    @Builder.Default
    @ApiModelProperty(value = "The equipment type", example = "RESIDENTIAL_GATEWAY", required = true)
    private EquipmentTypeEnum type = EquipmentTypeEnum.RESIDENTIAL_GATEWAY;
}
