package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.domain.problem.OfferNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.model.entity.OfferEntity;
import com.tigo.galaxion.sales.facade.model.repository.OfferRepository;
import com.tigo.galaxion.sales.facade.services.retrieval.OfferRetrievalService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class OfferMsisdnTypeChoiceService {

    private final CartService cartService;
    private final OfferRetrievalService offerRetrievalService;
    private final OfferRepository offerRepository;

    private static void setNumberType(
            Long offerId,
            String numberType,
            TigoCartResponse tigoCartResponse) {

        var offer = tigoCartResponse.getOffers().stream().filter(tigoOfferResponse -> tigoOfferResponse.getId().equals(offerId)).findFirst();
        if (offer.isEmpty()) {
            throw new OfferNotFoundProblem(offerId);
        }
        offer.get().setNumberType(numberType);
    }

    @Transactional
    public TigoCartResponse updateForProspect(
            String reference,
            Long offerId,
            String deliveryType) {

        var tigoCartResponse = cartService.getCartForProspect(reference);
        return update(tigoCartResponse, offerId, deliveryType);
    }

    @Transactional
    public TigoCartResponse updateForCrossSell(
            String reference,
            Long offerId,
            String deliveryType) {

        var tigoCartResponse = cartService.getCartForCrossSell(reference);
        return update(tigoCartResponse, offerId, deliveryType);
    }

    private TigoCartResponse update(
            TigoCartResponse tigoCartResponse,
            Long offerId,
            String numberType) {

        setNumberType(offerId, numberType, tigoCartResponse);
        saveNumberType(offerId, numberType);
        return tigoCartResponse;
    }

    private void saveNumberType(
            Long offerId,
            String numberType) {

        var offer = offerRetrievalService.get(offerId)
                                         .orElse(OfferEntity.builder().offerId(offerId).build());
        offer.setMsisdnType(numberType);
        offerRepository.save(offer);
    }

}
