package com.tigo.galaxion.sales.facade.connector.address;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.address.domain.problem.AddressProblem;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;
import org.zalando.problem.ThrowableProblem;

public class AddressErrorDecoder extends BaseProblemErrorDecoder {

    public AddressErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "addresses-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        return new AddressProblem(throwableProblem);
    }
}
