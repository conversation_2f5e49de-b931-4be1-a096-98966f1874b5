package com.tigo.galaxion.sales.facade.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AddOfferAddressRequest {

    @NotBlank
    @ApiModelProperty(value = "Normalized address", required = true, example = "CR 46 # 52 50")
    private String normalizedAddress;

    @NotNull
    @ApiModelProperty(value = "Latitude of the address", required = true, example = "14.6082")
    private Double latitude;

    @NotNull
    @ApiModelProperty(value = "Longitude of the address", example = "-90.4717")
    private Double longitude;

    @NotNull
    @ApiModelProperty(value = "Stratum code", example = "5")
    private Integer stratum;

    @NotNull
    @ApiModelProperty(value = "Country code", example = "57")
    private Integer countryCode;

    @NotNull
    @ApiModelProperty(value = "Department code", example = "1")
    private Integer departmentCode;

    @NotNull
    @ApiModelProperty(value = "Municipality code", example = "1")
    private Integer municipalityCode;

    @NotNull
    @ApiModelProperty(value = "Address code", example = "12345")
    private Integer addressCode;

}
