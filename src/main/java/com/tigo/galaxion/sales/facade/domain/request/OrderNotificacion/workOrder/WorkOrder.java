package com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.workOrder;

import lombok.Data;

import java.util.List;

@Data
class WorkOrder {
    private String wochannelID;
    private String operativeArea;
    private String country;
    private String city;
    private String territory;
    private String fieldEngComment;
    private String posaddress;
    private String posconnectionData4;
    private String posconnectionData3;
    private String posconnectionData2;
    private String posconnectionData1;
    private String wireUsageEnd;
    private String wireUsageStart;
    private String creationCauseReason;
    private String clossingCauseReason;
    private String clossingComment;
    private String channelComment;
    private List<Object> laboresUsed;
    private boolean noMaterialUsed;
    private List<MaterialUsed> materialUsed;
    private boolean noEquipmentUsed;
    private List<EquipmentUsed> equipmentUsed;
    private boolean noEquipmentCollected;
    private List<EquipmentUsed> equipmentCollected;
    private List<ServiceItem> services;
    private String woclosureGeoToken;
    private String woclosureGeoTokenEntered;
    private String woclosureLatitude;
    private String woclosureLongitude;
    private Integer priority;
    private String wostatus;
    private WOType localWOType;
    private WOType wotype;
    private WOTypeCategory wotypeCategory;
    private boolean wiFiCertGenerated;
    private String wiFiCertID;
    private String wiFiCertResult;
}
