package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import javax.validation.constraints.NotBlank;

import lombok.Data;

@Data
public class CreateDiscountV2Request {
	private Integer amountVatExcluded;
	private Integer amountVatIncluded;
	//enum
	@NotBlank
	private String billingType;
	@NotBlank
	private String catalogCode;
	//enum
	@NotBlank
	private String catalogItemType;
	private Boolean isManual;
	private Integer occurrence;
}
