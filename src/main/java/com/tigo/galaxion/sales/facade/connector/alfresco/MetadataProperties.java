package com.tigo.galaxion.sales.facade.connector.alfresco;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetadataProperties {
    @JsonProperty("numeroCuenta")
    String numeroCuenta;

    @JsonProperty("segmento_Clientes")
    String segmento_Clientes;

    @JsonProperty("Fechadocumento_OperacionesClientes")
    String Fechadocumento_OperacionesClientes;

    @JsonProperty("DocumentoIdentidad_Clientes")
    String DocumentoIdentidad_Clientes;

    @JsonProperty("TipoDocumentoidentificacion_Cliente")
    String TipoDocumentoidentificacion_Cliente;

    @JsonProperty("msisdn")
    String msisdn;

    @JsonProperty("idFactura")
    String idFactura;

    public MetadataProperties(ProspectLeadResponse prospectLeadResponse, AcquisitionProspectResponse acquisitionProspectResponse){ 
        setNumeroCuenta("");
        setSegmento_Clientes("HOME");
        setFechadocumento_OperacionesClientes(getdatetimeISO8601());
        setDocumentoIdentidad_Clientes(prospectLeadResponse.getCustomer().getDocumentId());        
        setTipoDocumentoidentificacion_Cliente(getTypeDocument(prospectLeadResponse.getCustomer().getDocumentType()));
        setMsisdn("");
        setIdFactura("");
    }

    public String toMetadataString(){
        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.valueToTree(this);
        return jsonNode.toString();
    }

    private String getdatetimeISO8601(){
        Instant now = Instant.now();
        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        String formatted = formatter.withZone(ZoneId.systemDefault()).format(now);
        return formatted;
    }

    private String getTypeDocument(String documentType){
        String typeDocument="";
        switch (documentType){
            case "CC":
            case "Cédula de Ciudadanía":
                typeDocument = "Cédula de Ciudadanía";
                break;
            case "CE":
            case "Cédula de Extranjería":
                typeDocument = "Cédula de Extranjería";
                break;
            case "Numero de Identificacion Tributaria":
            case "NIT":
                typeDocument = "NIT";
                break;
            case "Pasaporte":
                typeDocument = documentType;
                break;
            default:
                typeDocument = "Do not exists this Document Type";
        }
        return typeDocument;
    }
}
