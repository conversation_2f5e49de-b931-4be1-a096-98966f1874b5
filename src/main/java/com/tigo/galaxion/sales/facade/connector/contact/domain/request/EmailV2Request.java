package com.tigo.galaxion.sales.facade.connector.contact.domain.request;

import com.tigo.galaxion.sales.facade.connector.contact.domain.enumeration.TypeEmailEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EmailV2Request {

    private String email;
    private TypeEmailEnum type;
}
