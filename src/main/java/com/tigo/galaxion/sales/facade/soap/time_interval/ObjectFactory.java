//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.06.17 at 02:45:10 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.time_interval;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.tigo.galaxion.sales.facade.soap.time_interval package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.tigo.galaxion.sales.facade.soap.time_interval
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link TimeIntervalRequest }
     * 
     */
    public TimeIntervalRequest createTimeIntervalRequest() {
        return new TimeIntervalRequest();
    }

    /**
     * Create an instance of {@link ExtendedGetAppointment }
     * 
     */
    public ExtendedGetAppointment createExtendedGetAppointment() {
        return new ExtendedGetAppointment();
    }

    /**
     * Create an instance of {@link TimeIntervalResponse }
     * 
     */
    public TimeIntervalResponse createTimeIntervalResponse() {
        return new TimeIntervalResponse();
    }

    /**
     * Create an instance of {@link Slots }
     * 
     */
    public Slots createSlots() {
        return new Slots();
    }

    /**
     * Create an instance of {@link Task }
     * 
     */
    public Task createTask() {
        return new Task();
    }

    /**
     * Create an instance of {@link Period }
     * 
     */
    public Period createPeriod() {
        return new Period();
    }

    /**
     * Create an instance of {@link Region }
     * 
     */
    public Region createRegion() {
        return new Region();
    }

}
