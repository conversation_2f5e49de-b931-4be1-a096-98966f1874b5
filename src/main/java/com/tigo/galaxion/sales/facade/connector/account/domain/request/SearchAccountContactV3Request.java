package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Singular;
import lombok.ToString;

import java.util.List;
import java.util.Set;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@ToString
@Builder
public class SearchAccountContactV3Request {

    @Singular
    private List<String> accountIds;

    @Singular
    private Set<String> types;

    @Builder.Default
    private boolean active = true;

    @Singular
    private List<String> contactUuids;
}
