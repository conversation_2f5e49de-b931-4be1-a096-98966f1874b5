package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration;

public enum PaymentMethodTypeEnum {
    CREDIT_CARD(Constant.CREDIT_CARD),
    DIRECT_DEBIT(Constant.DIRECT_DEBIT),
    MANUAL(Constant.MANUAL),
    ANONYMOUS_CARD(Constant.ANONYMOUS_CARD);
    // For other payment method insert case here

    private final String type;

    PaymentMethodTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static final class Constant {

        public static final String CREDIT_CARD = "CREDIT_CARD";

        public static final String DIRECT_DEBIT = "DIRECT_DEBIT";

        public static final String MANUAL = "MANUAL";

        public static final String ANONYMOUS_CARD = "ANONYMOUS_CARD";

        private Constant() {
        }
    }
}
