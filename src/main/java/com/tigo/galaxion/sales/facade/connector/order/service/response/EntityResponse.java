package com.tigo.galaxion.sales.facade.connector.order.service.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.order.domain.response.*;
import lombok.*;

import java.util.List;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class EntityResponse {

    @JsonProperty("@type")
    private String type;
    private String id;
    private String category;
    private String description;
    private String priority;
    private String requestedCompletionDate;
    private String requestedStartDate;
    private String completionDate;
    private String creationDate;
    private String state;
    private String requestedInitialState;
    private List<Note> note;
    private List<Channel> channel;
    private String externalId;
    private List<RelatedParty> relatedParty;
}
