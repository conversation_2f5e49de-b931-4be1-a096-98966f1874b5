package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.domain.enumeration.DocumentTypeEnum;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

@RestController
@Api(tags = "id-document-management")
public class IdDocumentController {

  @GetMapping("/api/v1/id-document-types")
  @ApiOperation(value = "Retrieves a list of supported ID document types",
                notes = "Optionally filters the results by a specific document type value")
  @ApiResponses(value = {
      @ApiResponse(code = 200, message = "List of ID document types"),
      @ApiResponse(code = 400, message = "Invalid document type value provided")
  })
  public ResponseEntity<Object> getIdDocumentTypeEnumValues(
      @RequestParam(value = "value", required = false) String value) {

    List<Map<String, String>> values = new ArrayList<>();

    if (value == null) { 
      for (DocumentTypeEnum enumType : DocumentTypeEnum.values()) {
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("value", enumType.toString());
        valueMap.put("description", enumType.getName());
        values.add(valueMap);
      }
      return ResponseEntity.ok(values);
    } else { 
      boolean found = false;
      for (DocumentTypeEnum enumType : DocumentTypeEnum.values()) {
        if (enumType.toString().equals(value)) {
          Map<String, String> valueMap = new HashMap<>();
          valueMap.put("value", enumType.toString());
          valueMap.put("description", enumType.getName());
          values.add(valueMap);
          found = true;
          break;
        }
      }

      if (!found) {
        Map<String, String> errorMap = new HashMap<>();
        errorMap.put("message", "El tipo de documento no es válido");
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(errorMap);
      } else {
        return ResponseEntity.ok(values);
      }
    }
  }
}
