package com.tigo.galaxion.sales.facade.connector.customer.search;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.account.domain.problem.AccountProblem;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;
import org.zalando.problem.ThrowableProblem;

public class ExternalCustomerSearchErrorDecoder extends BaseProblemErrorDecoder {

    public ExternalCustomerSearchErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "external-customer-search-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        return new AccountProblem(throwableProblem);
    }

}
