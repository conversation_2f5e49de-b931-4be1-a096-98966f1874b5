package com.tigo.galaxion.sales.facade.connector.evident.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentQuestionRequestBody {
    @JsonProperty("identification")
    private String identification;

    @JsonProperty("validationRegister")
    private String validationRegister; 
    
    @JsonProperty("typeIdentification")
    private String typeIdentification; 

    @JsonProperty("onlyQuestionnaire")
    private boolean onlyQuestionnaire;

}
