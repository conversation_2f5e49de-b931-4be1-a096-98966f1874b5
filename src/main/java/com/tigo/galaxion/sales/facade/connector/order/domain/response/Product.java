package com.tigo.galaxion.sales.facade.connector.order.domain.response;

import lombok.*;

import java.util.List;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class Product {
    private String id;
    private List<ProductCharacteristic> productCharacteristic;
}

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
class ProductCharacteristic {
    private String id;
    private String value;
    private String name;
}
