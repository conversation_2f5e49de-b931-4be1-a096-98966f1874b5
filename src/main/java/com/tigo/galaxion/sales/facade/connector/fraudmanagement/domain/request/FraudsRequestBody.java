package com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;

import javax.validation.constraints.NotBlank;

import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.frauds.Customer;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.frauds.Offer;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FraudsRequestBody {

    private Customer customer;
    private ArrayList<Offer> offers;

    @NotBlank
    private String transactionType;
}
