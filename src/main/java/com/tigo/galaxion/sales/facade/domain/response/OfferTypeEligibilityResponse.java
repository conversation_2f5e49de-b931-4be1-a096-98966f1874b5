package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Getter
@ToString
@Builder
@NoArgsConstructor
public class OfferTypeEligibilityResponse {

    @ApiModelProperty(value = "Offer type", required = true, example = "PREPAY")
    private String offerType;

    @ApiModelProperty(value = "True means the account is eligible", required = true, example = "true")
    private boolean eligible;

    @Builder.Default
    @ApiModelProperty(value = "List of causes", required = true, example = "[\"NO_SUBSCRIPTIONS\",\"ONLY_PREPAY_SUBSCRIPTIONS\"]")
    private List<String> causes = new ArrayList<>();

}
