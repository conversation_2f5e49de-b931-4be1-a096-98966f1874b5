package com.tigo.galaxion.sales.facade.services.field_service.process_task_ex;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;

import com.tigo.galaxion.sales.facade.services.field_service.BaseClientConfig;

@Configuration
public class ProcessTaskExClientConfig extends BaseClientConfig {
    @Value("${environment.url.process-task-ex-field-service}")
    private String serviceURL;

    @Bean(name = "marshallerProcessTaskEx")
    public Jaxb2Marshaller marshallerProcessTaskEx() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath("com.tigo.galaxion.sales.facade.soap.process_task_ex");
        return marshaller;
    }

    @Bean(name = "processTaskExClient")
    public ProcessTaskExClient processTaskExClient(Jaxb2Marshaller marshallerProcessTaskEx) {
        ProcessTaskExClient client = new ProcessTaskExClient();

        WebServiceTemplate webServiceTemplate = createWebServiceTemplate(serviceURL, marshallerProcessTaskEx);

        client.setDefaultUri(serviceURL);
        client.setWebServiceTemplate(webServiceTemplate);

        return client;
    }
}
