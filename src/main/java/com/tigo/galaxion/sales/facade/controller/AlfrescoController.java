package com.tigo.galaxion.sales.facade.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.connector.alfresco.request.AlfrescoDocumentRequest;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoAuthResponse;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoDocumentResponse;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseNotification;
import com.tigo.galaxion.sales.facade.services.AlfrescoService;
import com.tigo.galaxion.sales.facade.services.NotificationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("/api")
public class AlfrescoController {
    @Autowired
    private AlfrescoService alfrescoService;

    @Autowired
    private NotificationService notificationService;

    @PostMapping("/alfrescoCreateDocument/{prospectId}")
    @ApiOperation("Create Alfresco Documento.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "OK."),
            @ApiResponse(code = 500, message = "Internal Server Error."),
    })
    public ResponseEntity<ResponseNotification> createFileByProspectId(@PathVariable("prospectId") String prospectId) {
        ResponseNotification response = notificationService.buildNotification(prospectId);
        return ResponseEntity.ok(response);
    }
    
}
