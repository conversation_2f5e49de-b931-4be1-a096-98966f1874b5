//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:50:36 PM CST 
//

package com.tigo.galaxion.sales.facade.soap.multiple_operations;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * Java class for OperationsResults complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="OperationsResults"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="SucceededGetObjectOperation" type="{http://www.clicksoftware.com}SucceededGetObjectOperation" minOccurs="0"/&gt;
 *         &lt;element name="FailedOperation" type="{http://www.clicksoftware.com}FailedOperation" minOccurs="0"/&gt;
 *         &lt;element name="SucceededOperation" type="{http://www.clicksoftware.com}SucceededOperation" minOccurs="0"/&gt;
 *         &lt;element name="Operation" type="{http://www.clicksoftware.com}OperationResult" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OperationsResults", propOrder = {
        "succeededGetObjectOperation",
        "failedOperation",
        "succeededOperation",
        "operation"
})
@Data
@NoArgsConstructor
public class OperationsResults {

    @XmlElement(name = "SucceededGetObjectOperation")
    protected SucceededGetObjectOperation succeededGetObjectOperation;
    @XmlElement(name = "FailedOperation")
    protected FailedOperation failedOperation;
    @XmlElement(name = "SucceededOperation")
    protected SucceededOperation succeededOperation;
    @XmlElement(name = "Operation")
    protected List<OperationResult> operation;
}
