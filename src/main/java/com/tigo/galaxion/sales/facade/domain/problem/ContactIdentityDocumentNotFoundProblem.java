package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class ContactIdentityDocumentNotFoundProblem extends AbstractThrowableProblem {

    public ContactIdentityDocumentNotFoundProblem(String reference) {
        super(null,
              "contact-identity-document-not-found",
              Status.NOT_FOUND,
              String.format("The contact identity document with reference %s not found.", reference));
    }

}
