package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.ZonedDateTime;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
@ToString
public class PortInResponse {

    private String portInReference;

    private String msisdn;

    private ZonedDateTime portStartDateTime;
}
