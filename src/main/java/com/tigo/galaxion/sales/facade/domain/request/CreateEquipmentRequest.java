package com.tigo.galaxion.sales.facade.domain.request;

import java.time.LocalDateTime;

import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentAccessType;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentActivity;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentCategory;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentNature;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateEquipmentRequest {
	private String serialNumber;
	private String externalNumber;
	private EquipmentAccessType accessType;
	private EquipmentNature nature;
	private Integer recyclable; // boolean
	private String batchNumber;
	private String serviceId;
	private String warewhouseId;
	private Integer preactivated; // boolean
	private EquipmentStatus status;
	private String orderId;
	private EquipmentCategory category;
	private EquipmentActivity activity;
	private LocalDateTime activationDate;
	private LocalDateTime assigmentDate;
}
