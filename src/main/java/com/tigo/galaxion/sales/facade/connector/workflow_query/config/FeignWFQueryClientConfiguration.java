package com.tigo.galaxion.sales.facade.connector.workflow_query.config;

import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.InMemoryOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientProviderBuilder;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.InMemoryClientRegistrationRepository;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;


public class FeignWFQueryClientConfiguration {

    @Value("${security.keycloak.url}")
    private String authServerUrl;

    @Value("${security.keycloak.realm}")
    private String realm;

    @Value("${security.keycloak.resource0}")
    private String clientId;

    @Value("${security.keycloak.secret}")
    private String clientSecret;

    @Bean
    public RequestInterceptor oAuth2RequestInterceptor() {
        return new FeignWFQueryOAuth2RequestInterceptor(authorizedClientManager());
    }

    protected AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager() {

        var clientRegistrationRepository = new InMemoryClientRegistrationRepository(keycloakClientRegistration());
        var authorizedClientService = new InMemoryOAuth2AuthorizedClientService(clientRegistrationRepository);

        var authorizedClientProvider = OAuth2AuthorizedClientProviderBuilder.builder()
                                                                            .clientCredentials()
                                                                            .build();

        var authorizedClientManager = new AuthorizedClientServiceOAuth2AuthorizedClientManager(clientRegistrationRepository, authorizedClientService);
        authorizedClientManager.setAuthorizedClientProvider(authorizedClientProvider);

        return authorizedClientManager;
    }

    private ClientRegistration keycloakClientRegistration() {
        return ClientRegistration.withRegistrationId("keycloak")
                                 .clientId(clientId)
                                 .clientSecret(clientSecret)
                                 .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                                 .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                                 .tokenUri(authServerUrl + "/realms/" + realm + "/protocol/openid-connect/token")
                                 .build();
    }    
}
