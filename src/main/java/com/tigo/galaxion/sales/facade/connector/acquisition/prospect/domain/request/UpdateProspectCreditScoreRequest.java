package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UpdateProspectCreditScoreRequest {

    private String creditScore;

    @JsonProperty("isBlacklisted") // <PERSON> doesn't know build correctly a boolean starting with "is"
    private boolean isBlacklisted;
}
