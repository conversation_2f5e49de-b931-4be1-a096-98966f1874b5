package com.tigo.galaxion.sales.facade.controller;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.JsonNode;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.request.ProspectLeadRequest;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponseByEntity;

import com.tigo.galaxion.sales.facade.services.ProspectLeadService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/prospects-lead")
public class ProspectLeadController {
    private final ProspectLeadService prospectLeadService;

    @GetMapping("/{prospectId}/all")
    @ApiOperation("Get all prospect information.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Success."),
            @ApiResponse(code = 400, message = "Error in input message."),
            @ApiResponse(code = 401, message = "Authentication error."),
            @ApiResponse(code = 404, message = "The requested resource does not exist."),
            @ApiResponse(code = 503, message = "There is no communication with the service."),
    })
    public ProspectLeadResponse getAllProspectInfo(
            @PathVariable("prospectId") String prospectId) {
        return prospectLeadService.getAllProspectInfo(prospectId);
    }

    @GetMapping("/{prospectId}")
    @ApiOperation("Get prospect information base by entity parameter.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Success."),
            @ApiResponse(code = 400, message = "Error in input message."),
            @ApiResponse(code = 401, message = "Authentication error."),
            @ApiResponse(code = 403, message = "Error querying database."),
            @ApiResponse(code = 404, message = "The requested resource does not exist."),
            @ApiResponse(code = 503, message = "There is no communication with the service."),
    })
    public ProspectLeadResponseByEntity getProspectInfoBaseByEntityParameter(
            @PathVariable("prospectId") String prospectId,
            @RequestParam(value = "entity") String entity,
            @RequestParam(value = "entityType", required = false) Integer entityType) {
        return prospectLeadService.getProspectInfoByEntityType(prospectId, entity, entityType);
    }

    @PatchMapping("/{prospectId}/all")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("Update all prospect information.")
    @ApiResponses(value = {
            @ApiResponse(code = 204, message = "Success."),
            @ApiResponse(code = 400, message = "Error in input message."),
            @ApiResponse(code = 401, message = "Authentication error."),
            @ApiResponse(code = 403, message = "Error querying database."),
            @ApiResponse(code = 404, message = "The requested resource does not exist."),
            @ApiResponse(code = 503, message = "There is no communication with the service."),
    })
    public void updateAllProspectInfo(
            @PathVariable("prospectId") String prospectId,
            @ApiParam(value = "Prospect Lead Request", required = true) @RequestBody ProspectLeadRequest prospectLeadRequest) {
        prospectLeadService.updateAllProspectInfo(prospectId, prospectLeadRequest);
    }

    @PatchMapping("/{prospectId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("Update prospect information base by entity parameter.")
    @ApiResponses(value = {
            @ApiResponse(code = 204, message = "Success."),
            @ApiResponse(code = 400, message = "Error in input message."),
            @ApiResponse(code = 401, message = "Authentication error."),
            @ApiResponse(code = 403, message = "Error querying database."),
            @ApiResponse(code = 404, message = "The requested resource does not exist."),
            @ApiResponse(code = 503, message = "There is no communication with the service."),
    })
    public void updateProspectInfoByEntityParameter(
            @PathVariable("prospectId") String prospectId,
            @RequestParam(value = "entity") String entity,
            @ApiParam(value = "Prospect Lead Request", required = true) @RequestBody JsonNode request) {
        prospectLeadService.updateProspectInfoByEntity(prospectId, entity, request);
    }
}
