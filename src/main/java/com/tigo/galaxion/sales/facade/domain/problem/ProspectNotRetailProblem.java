package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class ProspectNotRetailProblem extends AbstractThrowableProblem {

    public ProspectNotRetailProblem(String channelGroup) {
        super(null,
              "prospect-is-not-retail",
              Status.BAD_REQUEST,
              String.format("Expected channel group to be RETAIL, but it is %s", channelGroup)
        );
    }
}
