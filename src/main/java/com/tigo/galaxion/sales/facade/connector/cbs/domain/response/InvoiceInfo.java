package com.tigo.galaxion.sales.facade.connector.cbs.domain.response;

import java.util.List;

import lombok.Data;

@Data
public class InvoiceInfo {
	private String acctCode;
	private String acctKey;
	private String custKey;
	private String subKey;
	private String primaryIdentity;
	private String npe;
	private String transType;
	private String extTransType;
	private String invoiceType;
	private Long invoiceId;
	private String invoiceNo;
	private String extTransId;
	private String billCycleId;
	private String billCycleBeginTime;
	private String billCycleEndTime;
	private Long invoiceAmount;
	private Long taxAmount;
	private Long openAmount;
	private Long currencyId;
	private String invoiceDate;
	private String dueDate;
	private String settleDate;
	private String status;
	private String printingCorrelative;
	private String taxCorrelative;
	private List<InvoiceDetail> invoiceDetail;
	private Long openTaxAmount;
	private List<AdditionalProperty> additionalProperty;
	private List<TaxList> taxList;
}
