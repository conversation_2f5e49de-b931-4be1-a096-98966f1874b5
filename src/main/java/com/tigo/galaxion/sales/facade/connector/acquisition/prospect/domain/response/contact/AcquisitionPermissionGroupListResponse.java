package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.contact;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AcquisitionPermissionGroupListResponse {

    private boolean allowThirdParty;

    private List<AcquisitionPermissionGroupResponse> permissionGroups;
}
