package com.tigo.galaxion.sales.facade.connector.account;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.account.domain.problem.AccountProblem;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;
import org.zalando.problem.ThrowableProblem;

public class AccountErrorDecoder extends BaseProblemErrorDecoder {

    public AccountErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "accounts-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        return new AccountProblem(throwableProblem);
    }

}
