package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.account.AccountClient;
import com.tigo.galaxion.sales.facade.connector.account.domain.request.SearchAccountContactV3Request;
import com.tigo.galaxion.sales.facade.connector.contact.ContactClient;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactIdentityDocumentV2Response;
import com.tigo.galaxion.sales.facade.connector.credit.score.CreditScoreClient;
import com.tigo.galaxion.sales.facade.connector.credit.score.domain.request.UpdateCreditScoreRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellCreateCrossSellRequest;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.response.TigoCreditScoreResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum;
import com.tigo.galaxion.sales.facade.domain.problem.ContactOwnerNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.problem.OwnerContactIdentityDocumentNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.request.CreateCrossSellRequest;
import com.tigo.galaxion.sales.facade.domain.request.CreditScoreRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static com.tigo.galaxion.sales.facade.connector.account.domain.constant.ContactTypeConstant.OWNER_CONTACT_TYPE;

@Service
@RequiredArgsConstructor
public class CreateCrossSellService {

    private final CrossSellClient crossSellClient;
    private final AccountClient accountClient;
    private final ContactClient contactClient;
    private final CreditScoreClient creditScoreClient;
    private final EligibilityService eligibilityService;
    private final CreditScoreService creditScoreService;

    public String createCrossSell(
            String accountId,
            CreateCrossSellRequest request) {

        eligibilityService.checkCrossSellEligibility(accountId);

        var ownerContactUuid = getOwnerContactUuid(accountId);
        var identityDocument = getOwnerIdentityDocument(ownerContactUuid);
        updateCreditScore(identityDocument, ownerContactUuid);
        return buildAndCreateCrossSell(accountId, request);
    }

    private void updateCreditScore(
            ContactIdentityDocumentV2Response identityDocument,
            String ownerContactUuid) {

        var newCreditScore = getNewCreditScore(identityDocument);
        var updateCreditScoreRequest = UpdateCreditScoreRequest
                .builder()
                .contactUuid(ownerContactUuid)
                .creditScore(newCreditScore.getScore())
                .build();
        creditScoreClient.updateCreditScore(updateCreditScoreRequest);
    }

    private TigoCreditScoreResponse getNewCreditScore(
            ContactIdentityDocumentV2Response identityDocument) {

        var newCreditScoreRequest = CreditScoreRequest
                .builder()
                .documentId(identityDocument.getIdentifier())
                .nationality(identityDocument.getNationality())
                .documentType(IdDocumentTypeEnum.valueOf(identityDocument.getType()))
                .build();
        return creditScoreService.getTigoCreditScore(newCreditScoreRequest);
    }

    private ContactIdentityDocumentV2Response getOwnerIdentityDocument(
            String ownerContactUuid) {

        var identityDocuments = contactClient.getIdentityDocuments(ownerContactUuid).stream().findFirst();
        if (identityDocuments.isEmpty()) throw new OwnerContactIdentityDocumentNotFoundProblem(ownerContactUuid);
        return identityDocuments.get();
    }

    private String buildAndCreateCrossSell(
            String accountId,
            CreateCrossSellRequest request) {

        var crossSellRequest = CrossSellCreateCrossSellRequest
                .builder()
                .offerType(request.getOfferType().name())
                .channelCode(request.getChannelCode())
                .accountId(accountId)
                .accessory(request.isAccessory())
                .build();
        return crossSellClient.createCrossSell(crossSellRequest);
    }

    private String getOwnerContactUuid(
            String accountId) {

        var searchAccountContact = SearchAccountContactV3Request
                .builder().type(OWNER_CONTACT_TYPE)
                .accountId(accountId)
                .build();
        var contactResponse = accountClient.searchAccountContact(searchAccountContact).stream().findFirst();
        if (contactResponse.isEmpty()) throw new ContactOwnerNotFoundProblem(accountId);
        return contactResponse.get().getUuid();
    }
}
