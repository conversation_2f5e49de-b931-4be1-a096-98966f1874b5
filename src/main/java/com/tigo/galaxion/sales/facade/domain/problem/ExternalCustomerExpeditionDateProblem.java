package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class ExternalCustomerExpeditionDateProblem  extends AbstractThrowableProblem {
    public ExternalCustomerExpeditionDateProblem() {
        super(null,
              "external-customer-expedition-date-not-match",
              Status.BAD_REQUEST,
              "expeditionDateNotMatch");
    }
}
