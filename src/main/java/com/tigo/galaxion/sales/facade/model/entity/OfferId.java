package com.tigo.galaxion.sales.facade.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serial;
import java.io.Serializable;

@Embeddable
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class OfferId implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Column(name = "reference", nullable = false)
    private String reference;

    @Column(name = "offer_id", nullable = false)
    private Long offerId;
}
