package com.tigo.galaxion.sales.facade.connector.order.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class OrderResponse {

    @JsonProperty("@type")
    private String type;
    private String id;
    private String category;
    private String description;
    private String priority;
    private String requestedCompletionDate;
    private String requestedStartDate;
    private String completionDate;
    private String creationDate;
    private String state;
    private String requestedInitialState;
    private List<Note> note;
    private List<Channel> channel;
    private List<ExternalId> externalId;
    private List<ProductOrderItem> productOrderItem;
    private List<RelatedParty> relatedParty;
}
