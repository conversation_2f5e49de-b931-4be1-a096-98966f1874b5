package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.evident.EvidentClient;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentGenerateRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentIdentificationVerifyRequest;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentInitializeRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentQuestionRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentValidationRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentVerifyRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentGenerateResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentIdentificationVerifyResponse;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentInitializeResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentQuestionResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentValidationResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentVerifyResponseBody;

import lombok.RequiredArgsConstructor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class EvidentService {
     @Autowired
     private EvidentClient client;

     public EvidentValidationResponseBody validateIdentification(EvidentValidationRequestBody request) {
          System.out.println("Request Evident" + request);
          return client.validateIdentification(request);
     }

     public EvidentInitializeResponseBody initializeOtp(EvidentInitializeRequestBody request) {
          return client.initializeOtp(request);
     }

     public EvidentGenerateResponseBody generateOtp(EvidentGenerateRequestBody request) {
          return client.generateOtp(request);
     }

     public EvidentVerifyResponseBody verifyOtp(EvidentVerifyRequestBody request) {
          return client.verifyOtp(request);
     }

     public EvidentQuestionResponseBody questionIdentification(String identification, String validationRegister, String typeIdentification, String onlyQuestionnaire) {
          return client.questionIdentification(identification, validationRegister, typeIdentification, onlyQuestionnaire);
     }

     public EvidentIdentificationVerifyResponse verifyIdentification( EvidentIdentificationVerifyRequest request){
          return client.verifyIdentification(request);
     }

}
