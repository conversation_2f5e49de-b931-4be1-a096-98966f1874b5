package com.tigo.galaxion.sales.facade.connector.coverage.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class CoverageRequest {

    @JsonProperty("id_barrio")
    private String idBarrio;

    @JsonProperty("id_calle")
    private String idCalle;

    @JsonProperty("id_casa")
    private String idCasa;
}