package com.tigo.galaxion.sales.facade.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CartOfferRedundancyWithSubscriptionRequest {

    @NotNull
    @ApiModelProperty(value = "The cart offer id.", required = true, example = "1")
    private Long cartOfferId;

    @NotNull
    @ApiModelProperty(value = "The subscription id.", required = true, example = "1")
    private Long subscriptionId;
}
