package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.contact.ContactClient;
import com.tigo.galaxion.sales.facade.connector.contact.domain.enumeration.TypeEmailEnum;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.AddressV2Request;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.ContactV2Request;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.EmailV2Request;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PatchAddressRequest;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PatchEmailRequest;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PatchPhoneNumberRequest;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PhoneNumberV2Request;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.PatchCrossSellRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoCrossSellDeliveryContactRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CrossSellContactService {

    private final CrossSellClient crossSellClient;
    private final ContactClient contactClient;

    @Transactional
    public CrossSellResponse updateContact(
            String reference,
            TigoCrossSellDeliveryContactRequest request) {

        var crossSell = crossSellClient.getCrossSell(reference);
        if (crossSell.getContactDeliveryUuid() == null) {
            var contactRequest = createDeliveryContactRequestForCrossSell(request);
            var contactDeliveryUuid = contactClient.createContact(contactRequest);
            return crossSellClient.updateCrossSell(reference, new PatchCrossSellRequest(contactDeliveryUuid));
        }
        updateDeliveryContact(crossSell.getContactDeliveryUuid(), request);
        return crossSell;
    }

    private void updateDeliveryContact(String contactUuid, TigoCrossSellDeliveryContactRequest request) {
        var contact = contactClient.getContact(contactUuid);
        contact.getFirstMainEmail()
                .filter(contactEmail -> !contactEmail.getEmail().equals(request.getEmail()))
                .ifPresent(contactEmail -> contactClient.updateEmail(contactEmail.getId(), new PatchEmailRequest(request.getEmail())));

        contact.getFirstMobilePhoneNumber()
                .filter(contactPhoneNumber -> !contactPhoneNumber.getPhoneNumber().equals(request.getPhoneNumber()))
                .ifPresent(contactPhoneNumber -> contactClient.updatePhoneNumber(contactPhoneNumber.getId(), new PatchPhoneNumberRequest("MOBILE", request.getPhoneNumber())));

        contact.getFirstDeliveryAddress()
                .filter(contactDeliveryAddress -> !contactDeliveryAddress.isEquals(request.getDeliveryAddress()))
                .ifPresent(contactDeliveryAddress -> contactClient.updateOrCreateAddress(contactUuid, createPatchAddressRequest(request.getDeliveryAddress())));
    }

    private ContactV2Request createDeliveryContactRequestForCrossSell(
            TigoCrossSellDeliveryContactRequest request) {

        var email = EmailV2Request
                .builder()
                .email(request.getEmail())
                .type(TypeEmailEnum.MAIN)
                .build();
        var phoneNumber = PhoneNumberV2Request
                .builder()
                .phoneNumber(request.getPhoneNumber())
                .type("MOBILE")
                .build();
        var address = AddressV2Request
                .builder()
                .type(AddressTypeEnum.DELIVERY)
                .area(request.getDeliveryAddress().getArea())
                .town(request.getDeliveryAddress().getTown())
                .street(request.getDeliveryAddress().getStreetName())
                .streetNumber(request.getDeliveryAddress().getStreetNumber())
                .code(request.getDeliveryAddress().getPostCode())
                .addressLine1(request.getDeliveryAddress().getTown())
                .addressLine2(request.getDeliveryAddress().getStreetNumber() + " " + request.getDeliveryAddress().getStreetName())
                .build();
        return ContactV2Request
                .builder()
                .firstName(request.getFirstName())
                .lastName(request.getLastName())
                .email(email)
                .phoneNumber(phoneNumber)
                .address(address)
                .build();
    }

    private PatchAddressRequest createPatchAddressRequest(TigoAddressRequest addressRequest) {
        return PatchAddressRequest
                .builder()
                .type(AddressTypeEnum.DELIVERY)
                .area(addressRequest.getArea())
                .town(addressRequest.getTown())
                .street(addressRequest.getStreetName())
                .streetNumber(addressRequest.getStreetNumber())
                .code(addressRequest.getPostCode())
                .addressLine1(addressRequest.getTown())
                .addressLine2(addressRequest.getStreetNumber() + " " + addressRequest.getStreetName())
                .build();
    }
}
