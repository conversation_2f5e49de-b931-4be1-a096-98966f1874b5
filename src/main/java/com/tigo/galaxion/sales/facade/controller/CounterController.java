package com.tigo.galaxion.sales.facade.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.services.CounterService;

@Api
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1")
public class CounterController {
    private final CounterService counterService;

    @GetMapping("counter/{counterName}")
    @ApiOperation("Get the value of the counter by name and then increment it by 1 and update the value.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Counter value incremented."),
            @ApiResponse(code = 404, message = "Counter not found.")
    })
    public Long getIdAccount(
            @PathVariable(name = "counterName", required = true) String counterName) {
        return counterService.incrementCounter(counterName);
    }
}
