package com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CrossSellCreateCrossSellRequest {

    private String offerType;

    private String accountId;

    private boolean accessory;

    private String channelCode;
}
