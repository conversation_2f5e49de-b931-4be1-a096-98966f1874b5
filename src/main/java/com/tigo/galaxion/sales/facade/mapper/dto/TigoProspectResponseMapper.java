package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoProspectResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TigoProspectResponseMapper {

    public static TigoProspectResponse buildTigoProspectResponse(AcquisitionProspectResponse prospect) {
        return TigoProspectResponse.builder()
                                   .reference(prospect.getReference())
                                   .cartUuid(prospect.getCartUuid())
                                   .status(prospect.getStatus())
                                   .channelCode(prospect.getChannelCode())
                                   .channelGroup(prospect.getChannelGroup())
                                   .brand(prospect.getBrand())
                                   .offerType(prospect.getOfferType())
                                   .customerType(prospect.getCustomerType())
                                   .creditScore(prospect.getCreditScore())
                                   .contractFileId(prospect.getContractFileId())
                                   .contractFileName(prospect.getContractFileName())
                                   .csrAgentEmail(prospect.getCsrAgentEmail())
                                   .isBlacklisted(prospect.getIsBlacklisted())
                                   .paymentSettings(prospect.getPaymentSettings())
                                   .build();
    }

}
