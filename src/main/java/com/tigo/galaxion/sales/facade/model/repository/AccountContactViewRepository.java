package com.tigo.galaxion.sales.facade.model.repository;

import com.tigo.galaxion.sales.facade.model.entity.AccountContactView;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface AccountContactViewRepository extends JpaRepository<AccountContactView, String> {

    List<AccountContactView> findByIdentityDocumentIdentifierAndIdentityDocumentType(
            String identityDocumentIdentifier, String identityDocumentType);
}
