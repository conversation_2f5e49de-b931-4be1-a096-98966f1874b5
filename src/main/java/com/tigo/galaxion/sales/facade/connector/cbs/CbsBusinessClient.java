package com.tigo.galaxion.sales.facade.connector.cbs;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.tigo.galaxion.sales.facade.connector.cbs.domain.response.CbsAccountResponse;

@FeignClient(value = "cbs-business-service", url = "${environment.url.cbs-business-service}", configuration = CbsErrorDecoder.class)
public interface CbsBusinessClient {
    @GetMapping("/v1/accounts")
	public CbsAccountResponse queryCbsAccount(@RequestParam("traceId") String traceId, @RequestParam("accountKey") String accountKey);
}
