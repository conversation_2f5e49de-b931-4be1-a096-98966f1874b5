package com.tigo.galaxion.sales.facade.connector.georeference.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class GeoreferenceRequest {

    @NotBlank
    @Size(max = 6)
    private String countryCode;

    @NotBlank
    @Size(max = 6)
    private String departmentCode;

    @NotBlank
    @Size(max = 6)
    private String municipalityCode;

    @NotBlank
    private String naturalAddress;
}
