package com.tigo.galaxion.sales.facade.connector.contact.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ContactPermissionListV2Response {

    private List<ContactPermissionGroupV2Response> permissionGroups;

    private List<ContactPermissionV2Response> permissions;
}
