package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration;

public enum DirectoryPreferenceEnum {
    LISTED("Listed"),         // Available for Directory Enquiries and in the Phonebook
    UNLISTED("Unlisted"),       // Available for Directory Enquiries but not in the Phonebook
    EXDIRECTORY("Ex-directory");    // Not Available for Directory Enquiries and not in the Phonebook

    private final String name;

    DirectoryPreferenceEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
