package com.tigo.galaxion.sales.facade.connector.account.domain.response;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class EquipmentFinancingV3Response {
	private String activatedAt;
	@NotNull
	private Integer amountUpFrontChosen;
	@Valid
	private List<ChargeV3Response> charges;
	@NotNull
	private Integer id;
	@NotNull
	private Integer ocurrence;
	@NotNull
	private Integer remainingOcurrance;
	private String terminatedAt;
}
