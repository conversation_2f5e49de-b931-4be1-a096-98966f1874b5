package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Getter
@ToString
@Builder
@NoArgsConstructor
public class EligibilityResponse {

    @ApiModelProperty(value = "Service group", required = true, example = "TV")
    private String serviceGroup;

    @ApiModelProperty(value = "True means the account is eligible", required = true, example = "true")
    private boolean eligible;

    @ApiModelProperty(value = "Replacement reason service domain", required = true,
                      example = "[\"SUBSCRIPTION_WITH_THIS_SERVICE_GROUP_ALREADY_EXIST\"," +
                                "\"MAX_SUBSCRIPTION_REACHED\"," +
                                "\"PARENT_SUBSCRIPTION_NOT_ACTIVE\"," +
                                "\"PARENT_SUBSCRIPTION_SCHEDULED_FOR_TERMINATION\"," +
                                "\"NOT_COMPATIBLE_WITH_SUBSCRIPTION_OFFERS\"]")
    @Builder.Default
    private List<String> causes = new ArrayList<>();
}
