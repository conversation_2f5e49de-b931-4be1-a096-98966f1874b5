package com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.changeStatusFieldService;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Builder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class changeStatusFsRequest {
    @NotBlank
    @ApiModelProperty(value = "Transaction Id.", required = true, example = "8e515728cade4bfaad1a3dd17f9df8dd")
    @Size(max = 60)
    @JsonProperty("transactionId")
    private String transactionId;

    @NotBlank
    @ApiModelProperty(value = "New Status.", required = true, example = "En Proceso")
    @Size(max = 60)
    @JsonProperty("newStatus")
    private String newStatus;
}