package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.tigo.galaxion.sales.facade.connector.account.domain.enums.BillingType;
import com.tigo.galaxion.sales.facade.connector.account.domain.enums.CatalogItemType;

import lombok.Data;

@Data
public class CreateDiscountV3Request {
	@NotBlank
	private BillingType billingType;
	@NotBlank
	private String catalogCode;
	@NotBlank
	private CatalogItemType catalogItemType;
	private Boolean manual;
	@NotNull
	private Integer occurrence;
}
