package com.tigo.galaxion.sales.facade.connector.cbs;

import org.zalando.problem.ThrowableProblem;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.problem.CbsProblem;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;

public class CbsErrorDecoder extends BaseProblemErrorDecoder {

    public CbsErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "cbs-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        return new CbsProblem(throwableProblem);
    }

}
