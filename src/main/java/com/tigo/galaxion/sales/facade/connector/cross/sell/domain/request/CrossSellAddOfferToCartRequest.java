package com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request;

import com.tigo.galaxion.sales.facade.domain.enumeration.PortInTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CrossSellAddOfferToCartRequest {

    private String catalogOfferCode;

    private String catalogTariffPlanCode;

    private PortInTypeEnum portInType;

    private Long installationAddressId;

    private Long parentOfferId;

    private Long parentSubscriptionId;
}
