package com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WimaxCoverage {
    private String radioBase;
    private String sector;
    private String signal;
    private String priority;
    private String coverage;
}
