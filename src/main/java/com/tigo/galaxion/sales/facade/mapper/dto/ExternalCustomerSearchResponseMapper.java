package com.tigo.galaxion.sales.facade.mapper.dto;

import java.time.OffsetDateTime;
import java.util.Comparator;
import java.util.Optional;

import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.Address;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomer;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomerFullDetail;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExternalCustomerSearchResponseMapper {

  public static ExternalCustomer buildExternalCustomer(ExternalCustomerFullDetail externalCustomerFullDetail)
      throws Exception {

    String firstNames = externalCustomerFullDetail.basicInformation().firstName();
    if (externalCustomerFullDetail.basicInformation().secondName() != null
        && !externalCustomerFullDetail.basicInformation().secondName().equals("")) {
      firstNames = firstNames.concat(" ").concat(externalCustomerFullDetail.basicInformation().secondName());
    }
    String lastNames = externalCustomerFullDetail.basicInformation().firstLastName().concat(" ")
        .concat(externalCustomerFullDetail.basicInformation().secondLastName());

    // Colombia explicitly needs to use the information on the first item of the
    // Address
    Optional<Address> addressObject = externalCustomerFullDetail.addresses().stream()
        .filter(address -> address.lastReport() != null)
        .max(Comparator.comparing(address -> OffsetDateTime.parse(address.lastReport())))
        .or(() -> externalCustomerFullDetail.addresses().stream().findFirst());

    // TODO: Add translated message
    if (addressObject.isEmpty()) {
      throw new Exception("Address is empty");
    }
    // TODO: validar con JJ si agregaron un campo para no hacer el split
    // TODO: agregar runtime exception por si no viene ningun address

    // Colombian logic to extract de department code from the dane code
    String departamento = addressObject.get().daneCode().substring(0, 2);
    // Colombian logic to extract de municipal code from the dane code
    String municipio = addressObject.get().daneCode().substring(2, 5);

    return ExternalCustomer.builder()
        .nombre(firstNames)
        .apellidos(lastNames)
        .direccion(addressObject.get().data())
        .departamento(departamento)
        .municipio(municipio)
        .email(externalCustomerFullDetail.emails().email())
        .mobileNumber(externalCustomerFullDetail.cellPhones().mobileNumber())
        .expeditionDate(externalCustomerFullDetail.basicInformation().expeditionDate())
        .build();
  }
}
