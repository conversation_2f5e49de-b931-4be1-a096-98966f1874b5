package com.tigo.galaxion.sales.facade.services;

import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogEntity;
import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogId;

import com.tigo.galaxion.sales.facade.model.repository.ProspectusLogRepository;
import com.tigo.galaxion.sales.facade.domain.response.ProspectusLogResponse;
import com.tigo.galaxion.sales.facade.domain.request.ProspectusLogRequest;

@Service
@RequiredArgsConstructor
public class ProspectusLogService {
        private final ProspectusLogRepository prospectLogRepository;

        public ProspectusLogResponse createProspectusLog(ProspectusLogRequest prospectusLog, String ipAddress) {

                LocalDateTime createdAt = LocalDateTime.now();

                var prospectLogId = ProspectusLogId
                                .builder()
                                .reference(prospectusLog.getReference())
                                .customer_id_type(prospectusLog.getCustomer_id_type())
                                .customer_id_number(prospectusLog.getCustomer_id_number())
                                .build();

                ProspectusLogEntity prospectLog = ProspectusLogEntity
                                .builder()
                                .prospectusLogId(prospectLogId)
                                .user_id(prospectusLog.getUser_id())
                                .user_name(prospectusLog.getUser_name())
                                .ip_equipment(ipAddress)
                                .sales_channel(prospectusLog.getSales_channel())
                                .authentication_type(prospectusLog.getAuthentication_type())
                                .transaction(prospectusLog.getTransaction())
                                .createdAt(createdAt)
                                .build();

                prospectLogRepository.save(prospectLog);

                return ProspectusLogResponse
                                .builder()
                                .reference(prospectLog.getProspectusLogId().getReference())
                                .customer_id_type(prospectLog.getProspectusLogId().getCustomer_id_type())
                                .customer_id_number(prospectLog.getProspectusLogId().getCustomer_id_number())
                                .user_id(prospectLog.getUser_id())
                                .user_name(prospectLog.getUser_name())
                                .ip_equipment(prospectLog.getIp_equipment())
                                .sales_channel(prospectLog.getSales_channel())
                                .authentication_type(prospectLog.getAuthentication_type())
                                .transaction(prospectLog.getTransaction())
                                .created_at(prospectLog.getCreatedAt())
                                .build();
        }

        public ProspectusLogResponse getProspectReference(String prospectReference) {
                List<ProspectusLogEntity> prospectLog = prospectLogRepository
                                .findByProspectusLogId_ReferenceOrderByCreatedAtDesc(prospectReference);

                if (prospectLog.isEmpty()) {
                        throw new NoSuchElementException("No logs found for reference: " + prospectReference);
                }

                ProspectusLogEntity prospectLogEntity = prospectLog.get(0);

                return ProspectusLogResponse
                                .builder()
                                .reference(prospectLogEntity.getProspectusLogId().getReference())
                                .customer_id_type(prospectLogEntity.getProspectusLogId().getCustomer_id_type())
                                .customer_id_number(prospectLogEntity.getProspectusLogId().getCustomer_id_number())
                                .user_id(prospectLogEntity.getUser_id())
                                .user_name(prospectLogEntity.getUser_name())
                                .ip_equipment(prospectLogEntity.getIp_equipment())
                                .sales_channel(prospectLogEntity.getSales_channel())
                                .authentication_type(prospectLogEntity.getAuthentication_type())
                                .transaction(prospectLogEntity.getTransaction())
                                .created_at(prospectLogEntity.getCreatedAt())
                                .build();
        }
}