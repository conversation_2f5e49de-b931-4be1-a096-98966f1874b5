package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.payment_method;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.PaymentMethodTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ApiModel(description = "The payment sepa information.", parent = PaymentMethodRequest.class)
@JsonTypeName(PaymentMethodTypeEnum.Constant.DIRECT_DEBIT)
@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DirectDebitPaymentMethodRequest implements PaymentMethodRequest {

    private String holder;

    @ToString.Exclude
    private String iban;

    private String bankName;

    private String branchName;

    private String bic;

    @Override
    public PaymentMethodTypeEnum getPaymentMethodType() {
        return PaymentMethodTypeEnum.DIRECT_DEBIT;
    }
}
