package com.tigo.galaxion.sales.facade.domain.request;

import com.tigo.galaxion.sales.facade.domain.enumeration.AcquisitionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

@Getter
@EqualsAndHashCode
@ToString
@AllArgsConstructor
public class EligibilityRequest {

    private Long subscriptionId;
    private List<AcquisitionTypeEnum> acquisitionTypes;
}
