package com.tigo.galaxion.sales.facade.connector.prospectlead.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class ProspectLead{
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Address address[];
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Customer customer;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private CustomerReference customerReference[];
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Scoring scoring;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private ChannelNotification channelNotification[];
}
