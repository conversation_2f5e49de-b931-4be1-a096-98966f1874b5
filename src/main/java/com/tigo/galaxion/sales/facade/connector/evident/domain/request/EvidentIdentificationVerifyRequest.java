package com.tigo.galaxion.sales.facade.connector.evident.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.AnswerIdentificationVerify;
import com.tigo.galaxion.sales.facade.connector.evident.domain.Identification;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentIdentificationVerifyRequest {
    @JsonProperty("idQuestionary")
    private String idQuestionary; 

    @JsonProperty("regQuestionary")
    private String regQuestionary; 

    @JsonProperty("identification")
    private Identification Identification;
    
    @JsonProperty("onlyQuestionnaire")
    private String onlyQuestionnaire; 

    @JsonProperty("answer")
    private List<AnswerIdentificationVerify> answer;

}
