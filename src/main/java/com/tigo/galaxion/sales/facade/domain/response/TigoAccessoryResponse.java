package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoAccessoryResponse {

    @ApiModelProperty(value = "The accessory id", example = "123456789", required = true)
    private Long id;

    @ApiModelProperty(value = "The description", example = "Go More 2", required = true)
    private String description;

    @ApiModelProperty(value = "The accessory catalog code", example = "5008210_ACC", required = true)
    private String catalogCode;

    @ApiModelProperty(value = "The amount to be paid vat included", required = true)
    private TigoAmountResponse amountVatIncluded;

    @ApiModelProperty(value = "The amount to be paid vat excluded", required = true)
    private TigoAmountResponse amountVatExcluded;

    @ApiModelProperty(value = "The service domains of the accessory")
    private List<TigoServiceDomainResponse> serviceDomains;

}
