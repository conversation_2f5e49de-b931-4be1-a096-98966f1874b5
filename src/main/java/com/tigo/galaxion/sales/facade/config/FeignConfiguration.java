package com.tigo.galaxion.sales.facade.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import feign.RequestInterceptor;

@Configuration
public class FeignConfiguration { 

    @Value("${galaxion.user.identifier}")
    private String galaxionUserIdentifier;

    @Value("${galaxion.user.type}")
    private String galaxionUserType;

    @Bean
    public RequestInterceptor requestInterceptor() {
        return requestTemplate -> { 
            requestTemplate.header("galaxion-user-identifier", galaxionUserIdentifier);
            requestTemplate.header("galaxion-user-type", galaxionUserType);
        };
    }
}
