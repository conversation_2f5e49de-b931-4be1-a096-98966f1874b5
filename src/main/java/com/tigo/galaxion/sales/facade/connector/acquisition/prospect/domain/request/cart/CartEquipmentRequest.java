package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;

@Builder
public record CartEquipmentRequest(
  @ApiModelProperty(value = "The Equipment's catalog code", example = "GAPPS264R", required = true)
  @NotBlank(message = "Catalog Code can not be null or blank")
  String catalogCode,
  @ApiModelProperty(value = "Check tecrep catalog code ?", example = "true", required = false)
  Boolean checkTecrepCatalogCode,
  @ApiModelProperty(value = "The equipment's IMEI", example = "990000862471854", required = false)
  String imei
) {

}
