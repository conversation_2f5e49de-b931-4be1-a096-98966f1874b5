package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.contact;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AcquisitionPermissionRequest {

    @ApiModelProperty(value = "The permission of the contact.", example = "ALLOW_EMAIL_CONTACT", required = true)
    private String permission;

    @ApiModelProperty(value = "If the permission is enabled or not.", example = "true", required = true)
    private Boolean enabled;
}
