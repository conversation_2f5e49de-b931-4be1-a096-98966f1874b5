package com.tigo.galaxion.sales.facade.services.field_service.multiple_operations;

import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;

import com.tigo.galaxion.sales.facade.soap.multiple_operations.ExecuteMultipleOperations;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.ExecuteMultipleOperationsResponse;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MultipleOperationsClient extends WebServiceGatewaySupport {

  public ExecuteMultipleOperationsResponse multipleOperations(ExecuteMultipleOperations body) throws Exception {
    return (ExecuteMultipleOperationsResponse) getWebServiceTemplate().marshalSendAndReceive(body);
  }
}
