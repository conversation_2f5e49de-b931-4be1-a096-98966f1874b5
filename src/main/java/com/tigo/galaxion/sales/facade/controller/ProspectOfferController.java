package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.services.OfferMsisdnTypeChoiceService;
import com.tigo.galaxion.sales.facade.services.OfferSimDeliveryChoiceService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1")
public class ProspectOfferController {

    private final OfferSimDeliveryChoiceService offerSimDeliveryChoiceService;
    private final OfferMsisdnTypeChoiceService offerMsisdnTypeChoiceService;

    @PutMapping("/prospects/{prospect_reference}/carts/offers/{offer_id}/sims/delivery-type")
    public TigoCartResponse updateDeliverySimType(
            @PathVariable("prospect_reference") String prospectReference,
            @PathVariable("offer_id") Long offerId,
            @RequestParam String type) {
        return offerSimDeliveryChoiceService.updateForProspect(prospectReference, offerId, type);
    }

    @PutMapping("/prospects/{prospect_reference}/carts/offers/{offer_id}/msisdn/number-type")
    public TigoCartResponse updateNumberType(
            @PathVariable("prospect_reference") String prospectReference,
            @PathVariable("offer_id") Long offerId,
            @RequestParam String type) {
        return offerMsisdnTypeChoiceService.updateForProspect(prospectReference, offerId, type);
    }
}
