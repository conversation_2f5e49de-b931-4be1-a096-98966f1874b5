package com.tigo.galaxion.sales.facade;

import fr.njj.galaxion.lib.header.config.EnableGalaxionContext;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableFeignClients
@EnableCaching
@EnableScheduling
@EnableAsync
@EnableGalaxionContext
@PropertySource("classpath:stream.properties")
public class TigoSalesFacadeApplication {

    public static void main(String[] args) {
        SpringApplication.run(TigoSalesFacadeApplication.class, args);
    }

}
