package com.tigo.galaxion.sales.facade.connector.notification;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tigo.galaxion.sales.facade.connector.alfresco.config.FeignProxyConfiguration;
import com.tigo.galaxion.sales.facade.connector.alfresco.request.AlfrescoGenerateTokenRequest;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoAuthResponse;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.NotificationRequest;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseNotification;

import feign.Body;

/*@FeignClient(value = "tigo-notification-service", 
            url = "${environment.url.tigo-notification-service}",
            configuration = NotificationClientConfig.class)*/
@FeignClient(value = "notifications-service", 
            url = "${environment.url.notifications-service}")            
public interface NotificationClient {
    @PostMapping(value = "/api/v1/notifications", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseNotification sendNotification(@RequestBody NotificationRequest notificationRequest);
}

