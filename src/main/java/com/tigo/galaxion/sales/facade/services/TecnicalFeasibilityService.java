package com.tigo.galaxion.sales.facade.services;


import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.TecnicalFeasibilityClient;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.request.TecnicalFeasibilityRequestBody;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.response.TecnicalFeasibilityResponseBody;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


@Service
@RequiredArgsConstructor
public class TecnicalFeasibilityService {

    private final TecnicalFeasibilityClient client;

    public TecnicalFeasibilityResponseBody createTecnical( TecnicalFeasibilityRequestBody  request ) {
        return client.createTecnical(request);
    }


}
