package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;

@Builder
public record TigoRecurringAmountByOccurrenceResponse(
        @ApiModelProperty(value = "The vat included amount including all the discounts for the specified occurrence", example = "1999", required = true)
        long amountVatIncluded,

        @ApiModelProperty(value = "The vat excluded amount including all the discounts for the specified occurrence", example = "1888", required = true)
        long amountVatExcluded,

        @ApiModelProperty(value = "The number of occurrence for this amount (null means forever lasting)", example = "6")
        Integer occurrence) {
}
