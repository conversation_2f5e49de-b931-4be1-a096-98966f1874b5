package com.tigo.galaxion.sales.facade.services.order_notification;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.changeStatusFieldService.changeStatusFsRequest;
import com.tigo.galaxion.sales.facade.connector.workflow_query.WorkflowQueryClient;
import com.tigo.galaxion.sales.facade.connector.workflow_query.domain.response.BasicOrderInformacion;
import com.tigo.galaxion.sales.facade.connector.crm_api.workflow_engine_facade.WEFClient;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class FieldServiceChangeStatusService {

    @Value("${wfe.signal.upfrontpayment}")
    private String signalName;

    private final WorkflowQueryClient workflowQueryClient;
    private final WEFClient wefClient;
    BasicOrderInformacion basicOrderInformacion;

    public String changeStatus(changeStatusFsRequest request, String orderId) {

      var trackingNumber = UUID.randomUUID().toString();
      
          if(request.getNewStatus().equals("CANCELLED")){
            basicOrderInformacion = workflowQueryClient.getStatus(orderId);
            if(basicOrderInformacion.order().status().equals("RUNNING")){
                //wefClient.deleteOrder(orderId);
                workflowQueryClient.cancelOrder(orderId);

                return trackingNumber;
              }
            else return basicOrderInformacion.order().status() + "/";
          }
          else
            return "NO";
    }
}
