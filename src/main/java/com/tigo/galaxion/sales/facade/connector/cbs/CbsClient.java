package com.tigo.galaxion.sales.facade.connector.cbs;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.tigo.galaxion.sales.facade.connector.cbs.domain.response.CbsBalanceResponse;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.response.CbsInvoiceResponse;
import com.tigo.galaxion.sales.facade.connector.cbs.domain.response.CbsPaymentResponse;

@FeignClient(value = "cbs-service", url = "${environment.url.cbs-service}", configuration = CbsErrorDecoder.class)
public interface  CbsClient {
	@GetMapping("/v1/invoices")
	public CbsInvoiceResponse queryCbsInvoice(@RequestParam(required=true) String queryInvoice);

	@GetMapping("/v1/payments")
	public CbsPaymentResponse queryCbsPayment(@RequestParam(required=true) String queryPaymentLog);

	@GetMapping("/v1/balances")
	public CbsBalanceResponse queryCbsBalance(@RequestParam(required=true) String queryBalance);
}
