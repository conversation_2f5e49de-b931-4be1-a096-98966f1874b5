package com.tigo.galaxion.sales.facade.connector.account.domain.response;

import javax.validation.constraints.NotNull;

import com.tigo.galaxion.sales.facade.connector.account.domain.enums.BillingType;
import com.tigo.galaxion.sales.facade.connector.account.domain.enums.Status;

import lombok.Data;

@Data
public class ChargeV3Response {
	private String activatedAt;
	private Integer addonId;
	private String advanceArrears;
	private BillingType billingType;
	private String catalogCode;
	private Integer coolingOffPeriod;
	private String description;
	private Integer equipmentFinancingId;
	@NotNull
	private Integer id;
	private Integer overriddenPrice;
	private String pricePlanCatalogCode;
	private Integer serviceId;
	private Status status;
	private Integer subscriptionId;
	private String terminatedAt;
}
