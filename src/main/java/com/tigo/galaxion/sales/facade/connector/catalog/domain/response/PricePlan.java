
package com.tigo.galaxion.sales.facade.connector.catalog.domain.response;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.Generated;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "validFrom",
    "validTo",
    "code",
    "minPrice",
    "defaultPrice",
    "maxPrice",
    "minPriceVatExcluded",
    "defaultPriceVatExcluded",
    "maxPriceVatExcluded"
})
@Generated("jsonschema2pojo")
public class PricePlan {

    @JsonProperty("validFrom")
    private String validFrom;
    @JsonProperty("validTo")
    private Object validTo;
    @JsonProperty("code")
    private String code;
    @JsonProperty("minPrice")
    private Integer minPrice;
    @JsonProperty("defaultPrice")
    private Integer defaultPrice;
    @JsonProperty("maxPrice")
    private Integer maxPrice;
    @JsonProperty("minPriceVatExcluded")
    private Integer minPriceVatExcluded;
    @JsonProperty("defaultPriceVatExcluded")
    private Integer defaultPriceVatExcluded;
    @JsonProperty("maxPriceVatExcluded")
    private Integer maxPriceVatExcluded;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new LinkedHashMap<String, Object>();

    @JsonProperty("validFrom")
    public String getValidFrom() {
        return validFrom;
    }

    @JsonProperty("validFrom")
    public void setValidFrom(String validFrom) {
        this.validFrom = validFrom;
    }

    public PricePlan withValidFrom(String validFrom) {
        this.validFrom = validFrom;
        return this;
    }

    @JsonProperty("validTo")
    public Object getValidTo() {
        return validTo;
    }

    @JsonProperty("validTo")
    public void setValidTo(Object validTo) {
        this.validTo = validTo;
    }

    public PricePlan withValidTo(Object validTo) {
        this.validTo = validTo;
        return this;
    }

    @JsonProperty("code")
    public String getCode() {
        return code;
    }

    @JsonProperty("code")
    public void setCode(String code) {
        this.code = code;
    }

    public PricePlan withCode(String code) {
        this.code = code;
        return this;
    }

    @JsonProperty("minPrice")
    public Integer getMinPrice() {
        return minPrice;
    }

    @JsonProperty("minPrice")
    public void setMinPrice(Integer minPrice) {
        this.minPrice = minPrice;
    }

    public PricePlan withMinPrice(Integer minPrice) {
        this.minPrice = minPrice;
        return this;
    }

    @JsonProperty("defaultPrice")
    public Integer getDefaultPrice() {
        return defaultPrice;
    }

    @JsonProperty("defaultPrice")
    public void setDefaultPrice(Integer defaultPrice) {
        this.defaultPrice = defaultPrice;
    }

    public PricePlan withDefaultPrice(Integer defaultPrice) {
        this.defaultPrice = defaultPrice;
        return this;
    }

    @JsonProperty("maxPrice")
    public Integer getMaxPrice() {
        return maxPrice;
    }

    @JsonProperty("maxPrice")
    public void setMaxPrice(Integer maxPrice) {
        this.maxPrice = maxPrice;
    }

    public PricePlan withMaxPrice(Integer maxPrice) {
        this.maxPrice = maxPrice;
        return this;
    }

    @JsonProperty("minPriceVatExcluded")
    public Integer getMinPriceVatExcluded() {
        return minPriceVatExcluded;
    }

    @JsonProperty("minPriceVatExcluded")
    public void setMinPriceVatExcluded(Integer minPriceVatExcluded) {
        this.minPriceVatExcluded = minPriceVatExcluded;
    }

    public PricePlan withMinPriceVatExcluded(Integer minPriceVatExcluded) {
        this.minPriceVatExcluded = minPriceVatExcluded;
        return this;
    }

    @JsonProperty("defaultPriceVatExcluded")
    public Integer getDefaultPriceVatExcluded() {
        return defaultPriceVatExcluded;
    }

    @JsonProperty("defaultPriceVatExcluded")
    public void setDefaultPriceVatExcluded(Integer defaultPriceVatExcluded) {
        this.defaultPriceVatExcluded = defaultPriceVatExcluded;
    }

    public PricePlan withDefaultPriceVatExcluded(Integer defaultPriceVatExcluded) {
        this.defaultPriceVatExcluded = defaultPriceVatExcluded;
        return this;
    }

    @JsonProperty("maxPriceVatExcluded")
    public Integer getMaxPriceVatExcluded() {
        return maxPriceVatExcluded;
    }

    @JsonProperty("maxPriceVatExcluded")
    public void setMaxPriceVatExcluded(Integer maxPriceVatExcluded) {
        this.maxPriceVatExcluded = maxPriceVatExcluded;
    }

    public PricePlan withMaxPriceVatExcluded(Integer maxPriceVatExcluded) {
        this.maxPriceVatExcluded = maxPriceVatExcluded;
        return this;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    public PricePlan withAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
        return this;
    }

}
