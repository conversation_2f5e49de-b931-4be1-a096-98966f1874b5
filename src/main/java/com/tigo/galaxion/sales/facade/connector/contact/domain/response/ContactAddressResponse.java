package com.tigo.galaxion.sales.facade.connector.contact.domain.response;

import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoAddressRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Objects;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ContactAddressResponse {

    private AddressTypeEnum type;

    private String area;

    private String town;

    private String street;

    private String streetNumber;

    private String code;

    public boolean isEquals(TigoAddressRequest request) {
        return Objects.equals(area, request.getArea())
               && Objects.equals(town, request.getTown())
               && Objects.equals(street, request.getStreetName())
               && Objects.equals(streetNumber, request.getStreetNumber())
               && Objects.equals(code, request.getPostCode());
    }
}
