//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.06.17 at 02:45:10 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.time_interval;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ExtendedGetAppointment complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ExtendedGetAppointment"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Nodo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Task" type="{http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap}Task"/&gt;
 *         &lt;element name="Profile" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Period" type="{http://crmsaleforce.resourcemanager.millicom.com/unified}Period"/&gt;
 *         &lt;element name="ExcludeCurrentAppointment" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="MessageError" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExtendedGetAppointment", propOrder = {
    "nodo",
    "task",
    "profile",
    "period",
    "excludeCurrentAppointment",
    "messageError"
})
public class ExtendedGetAppointment {

    @XmlElement(name = "Nodo", required = true)
    protected String nodo;
    @XmlElement(name = "Task", required = true)
    protected Task task;
    @XmlElement(name = "Profile", required = true)
    protected String profile;
    @XmlElement(name = "Period", required = true)
    protected Period period;
    @XmlElement(name = "ExcludeCurrentAppointment")
    protected boolean excludeCurrentAppointment;
    @XmlElement(name = "MessageError", required = true)
    protected String messageError;

    /**
     * Gets the value of the nodo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNodo() {
        return nodo;
    }

    /**
     * Sets the value of the nodo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNodo(String value) {
        this.nodo = value;
    }

    /**
     * Gets the value of the task property.
     * 
     * @return
     *     possible object is
     *     {@link Task }
     *     
     */
    public Task getTask() {
        return task;
    }

    /**
     * Sets the value of the task property.
     * 
     * @param value
     *     allowed object is
     *     {@link Task }
     *     
     */
    public void setTask(Task value) {
        this.task = value;
    }

    /**
     * Gets the value of the profile property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProfile() {
        return profile;
    }

    /**
     * Sets the value of the profile property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProfile(String value) {
        this.profile = value;
    }

    /**
     * Gets the value of the period property.
     * 
     * @return
     *     possible object is
     *     {@link Period }
     *     
     */
    public Period getPeriod() {
        return period;
    }

    /**
     * Sets the value of the period property.
     * 
     * @param value
     *     allowed object is
     *     {@link Period }
     *     
     */
    public void setPeriod(Period value) {
        this.period = value;
    }

    /**
     * Gets the value of the excludeCurrentAppointment property.
     * 
     */
    public boolean isExcludeCurrentAppointment() {
        return excludeCurrentAppointment;
    }

    /**
     * Sets the value of the excludeCurrentAppointment property.
     * 
     */
    public void setExcludeCurrentAppointment(boolean value) {
        this.excludeCurrentAppointment = value;
    }

    /**
     * Gets the value of the messageError property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMessageError() {
        return messageError;
    }

    /**
     * Sets the value of the messageError property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMessageError(String value) {
        this.messageError = value;
    }

}
