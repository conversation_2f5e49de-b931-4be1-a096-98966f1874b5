package com.tigo.galaxion.sales.facade.connector.acquisition.prospect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.problem.AcquisitionProspectContactNotFoundProblem;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.problem.AcquisitionProspectProblem;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;
import org.zalando.problem.ThrowableProblem;

public class AcquisitionProspectErrorDecoder extends BaseProblemErrorDecoder {

    public AcquisitionProspectErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "acquisition-prospects-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        if ("contact-not-found".equals(throwableProblem.getTitle())) {
            return new AcquisitionProspectContactNotFoundProblem(throwableProblem);
        }
        return new AcquisitionProspectProblem(throwableProblem);
    }

}
