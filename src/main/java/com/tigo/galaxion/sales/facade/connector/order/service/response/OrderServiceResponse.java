package com.tigo.galaxion.sales.facade.connector.order.service.response;

import com.tigo.galaxion.sales.facade.connector.order.domain.response.GetOrdersResponse;
import lombok.*;

import java.util.List;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class OrderServiceResponse {
    private int page;
    private int size;
    private int total;
    private List<GetOrdersResponse> content;
}
