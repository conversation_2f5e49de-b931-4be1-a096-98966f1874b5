package com.tigo.galaxion.sales.facade.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@Getter
@ToString
@Builder
@NoArgsConstructor
public class AddressNormalizeResponseBody {    
@JsonProperty("georreferenceResponse")
private AddressNormalizeResponse addressNormalizeResponse;
}
