package com.tigo.galaxion.sales.facade.services.field_service.time_interval;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;
import com.tigo.galaxion.sales.facade.services.field_service.BaseClientConfig;

@Configuration
public class TimeIntervalClientConfig extends BaseClientConfig {

    @Value("${environment.url.time-interval-field-service}")
    private String serviceURL;

    @Bean(name = "marshallerTimeInterval")
    public Jaxb2Marshaller marshallerTimeInterval() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath("com.tigo.galaxion.sales.facade.soap.time_interval");
        return marshaller;
    }

    @Bean(name = "timeIntervalClient")
    public TimeIntervalClient timeIntervalClient(Jaxb2Marshaller marshallerTimeInterval) {
        TimeIntervalClient client = new TimeIntervalClient();

        WebServiceTemplate webServiceTemplate = createWebServiceTemplate(serviceURL, marshallerTimeInterval);

        client.setDefaultUri(serviceURL);
        client.setWebServiceTemplate(webServiceTemplate);

        return client;
    }
}