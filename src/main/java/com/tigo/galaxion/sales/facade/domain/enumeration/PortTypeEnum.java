package com.tigo.galaxion.sales.facade.domain.enumeration;

public enum PortTypeEnum {
    BILL_PAY_SINGLE_LINE("Bill Pay Single Line"),
    BILL_PAY_MULTI_LINE("Bill Pay Multi Line"),
    BILL_PAY_BULK("Bill Pay Bulk"),
    PAYG_REGISTERED("Payg registered"),
    PAYG_UNREGISTERED("Payg unregistered"),
    PAYG_MULTI_LINE("Payg Multi Line"),
    PAYG_REGISTERED_MULTI_LINE("Payg Registered Multi Line");

    private final String name;

    PortTypeEnum(final String name) {
        this.name = name;
    }

    public final String getName() {
        return name;
    }
}
