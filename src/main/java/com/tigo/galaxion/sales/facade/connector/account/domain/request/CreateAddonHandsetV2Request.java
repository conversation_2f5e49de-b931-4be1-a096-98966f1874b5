package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class CreateAddonHandsetV2Request {
	@NotBlank
	private String catalogCode;
	private Boolean deviceEnrolled;
	private String imei;
	@NotNull
	private Integer orderId;
	private Integer price;
	private String pricePlanCatalogCode;
	private String serialNumber;
}
