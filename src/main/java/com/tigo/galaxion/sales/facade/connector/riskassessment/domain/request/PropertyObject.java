package com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("A Property Object")
public class PropertyObject {
    @NotBlank
    @ApiModelProperty(value = "A key",required = true)
    private String key;
    @NotBlank
    @ApiModelProperty(value = "A value",required = true)
    private String value;
}
