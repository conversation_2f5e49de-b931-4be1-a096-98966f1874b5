package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
public class RecurringAmountResponse {
    @ApiModelProperty(value = "The recurring charge to be paid monthly", example = "3999", required = true) 
    private Long amount;

    @ApiModelProperty(value = "The discounted up-front charge to be paid monthly during the discount duration", example = "1999", required = true)
    private Long discountedAmount;

    @ApiModelProperty(value = "The frequency of the discount", example = "MONTHLY", required = false)
    private String discountFrequency;

    @ApiModelProperty(value = "The occurrence of the discount", example = "7", required = false)
    private Integer discountOccurrence;
}
