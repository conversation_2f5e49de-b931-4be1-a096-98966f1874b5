package com.tigo.galaxion.sales.facade.services.field_service.get_task;

import com.tigo.galaxion.sales.facade.services.field_service.BaseClientConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;

@Configuration
public class GetTaskClientConfig extends BaseClientConfig {
    @Value("${environment.url.get-task}")
    private String serviceURL;

    @Bean(name = "marshallerGetTask")
    public Jaxb2Marshaller marshallerGetTask() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath("com.tigo.galaxion.sales.facade.soap.get_task");
        return marshaller;
    }

    @Bean(name = "getTaskClient")
    public GetTaskClient getTaskClient(Jaxb2Marshaller marshallerGetTask) {
        GetTaskClient client = new GetTaskClient();

        WebServiceTemplate webServiceTemplate = createWebServiceTemplate(serviceURL, marshallerGetTask);

        client.setDefaultUri(serviceURL);
        client.setWebServiceTemplate(webServiceTemplate);

        return client;
    }

}
