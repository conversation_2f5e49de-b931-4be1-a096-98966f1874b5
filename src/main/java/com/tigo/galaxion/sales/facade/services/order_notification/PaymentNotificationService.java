package com.tigo.galaxion.sales.facade.services.order_notification;

import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.PaymentInformationRequest;
import com.tigo.galaxion.sales.facade.domain.request.PaymentNotificationRequest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.app.dto.VariableUpdateDTO;
import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;
import mc.monacotelecom.workflow.base.enumeration.ProcessType;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentNotificationService {

    @Value("${wfe.signal.upfrontpayment}")
    private String signalName;

    private final WorkflowEngineClient workflowEngineClient;

    public void notifyPayment(PaymentNotificationRequest request, String orderId) {

        var wfeRequest = WorkflowSignalRequestDTO.builder()
                .name(signalName)
                .orderId(orderId)
                .context(Map.of("PaymentStatus", request.getPaymentStatus(),
                        "PaymentDate", request.getPaymentDate()))
                .build();

        workflowEngineClient.processSignal(wfeRequest);
    }
    
    public void paymentInformation(PaymentInformationRequest request) {

    	log.info("Update DTO with payed amount: {}", request.toString());
        workflowEngineClient.updateConnectorData(request.getOrderId(),
                VariableUpdateDTO.builder()
                        .type(ProcessType.COM)
                        .data(request)
                        .variable("paymentInfo")
                        .build());

        log.info("send signal from: {} to externalRefId: {}; signalName: {}", request.getOrderId(), request.getExternalId(), signalName);

        var wfeRequest = WorkflowSignalRequestDTO.builder()
                .name(signalName)
                .orderId(request.getOrderId())
                .context(Map.of("PayedAmount", request.getPayedAmount(),
                		"Balance", request.getBalance(),
                        "PaymentDate", request.getPaymentDate()))
                .build();

        workflowEngineClient.processSignal(wfeRequest);
    }

}
