package com.tigo.galaxion.sales.facade.services.order_notification;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.v2.ServiceActivationV2Request;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.app.dto.VariableUpdateDTO;
import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;
import mc.monacotelecom.workflow.base.enumeration.ProcessType;

@Service
@RequiredArgsConstructor
@Slf4j
public class ServiceActivationV2Service {
    @Value("${wfe.signal.SP_WAIT_BB_PROV_REQUEST}")
    private String signalName;

    private final WorkflowEngineClient workflowEngineClient;

    public String sendNotification(String orderId, String serviceId, ServiceActivationV2Request request)
            throws Exception {
        log.info("Create field service v2: {}", orderId);
        workflowEngineClient.updateConnectorData(serviceId,
                VariableUpdateDTO.builder()
                        .type(ProcessType.SOM)
                        .data(request)
                        .variable("fieldservice")
                        .build());

        log.info("send signal from: {} to serviceId: {}; signalName: {}", orderId, serviceId, signalName);

        var trackingNumber = UUID.randomUUID().toString();

        var deviceParams = generateDeviceParamsMap(request);
        deviceParams.put("trackingNumber", trackingNumber);

        log.info("deviceParams: {}", deviceParams);

        workflowEngineClient.updateConnectorData(
                serviceId,
                VariableUpdateDTO.builder()
                        .type(ProcessType.SOM)
                        .data(deviceParams)
                        .variable("deviceParams")
                        .build());

        workflowEngineClient.processSignal(WorkflowSignalRequestDTO.builder()
                .name(signalName)
                .orderId(serviceId)
                .context(Map.of("fieldServiceResult", "A",
                        "transactionId", request.getProvisiongRequestID()))
                .build());

        return trackingNumber;
    }

    public String sendOrderNotification(String orderId, ServiceActivationV2Request request)
            throws Exception {
        log.info("Create order notification v2: {}", orderId);
        
        var trackingNumber = UUID.randomUUID().toString();
        
        // Process order notification logic here
        // This method handles order-level notifications without specific serviceId
        log.info("Processing order notification for orderId: {} with trackingNumber: {}", orderId, trackingNumber);
        
        // You can add specific order notification logic here
        // For now, returning the tracking number
        
        return trackingNumber;
    }

    private Map<String, Object> generateDeviceParamsMap(ServiceActivationV2Request request) {
        Map<String, Object> deviceParams = new HashMap<>();

        // Set the deviceParams properties
        deviceParams.put("type", request.getEquipmentName());
        deviceParams.put("deviceId", request.getEquipmentExternalRefID());
        deviceParams.put("smartcardId", null);
        deviceParams.put("vendorCas", request.getEquipmentName());
        deviceParams.put("provisioningId", request.getProvisiongRequestID());
        deviceParams.put("assetId", request.isAsset() ? "true" : "false");
        deviceParams.put("code", request.getEquipmentCode());
        deviceParams.put("inventorySerialNumber", request.getEquipmentSerialNumber());
        deviceParams.put("phoneNumber", request.getEquipmentSerialNumber());
        deviceParams.put("port", request.getEquipmentSerialNumber());
        deviceParams.put("ssid", request.getEquipmentSerialNumber());
        deviceParams.put("apName", request.getEquipmentSerialNumber());

        return deviceParams;
    }
}