package com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Address;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.ChannelNotification;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Customer;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.CustomerReference;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Scoring;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProspectLeadResponse{
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private Address address[];
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private Customer customer;
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private CustomerReference customerReference[];
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private Scoring scoring;
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private ChannelNotification channelNotification[];
}

