package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;
 
public class CustomerPreferenceCommunicationPreferenceIdProblem extends AbstractThrowableProblem {
    public CustomerPreferenceCommunicationPreferenceIdProblem(String Communication_preference_id){
        super(null,
        "customer-CommunicationPreferenceID-not-found",
        Status.NOT_FOUND,
        String.format("The customer Communication Preference ID %s not found.", Communication_preference_id));
    }
}
