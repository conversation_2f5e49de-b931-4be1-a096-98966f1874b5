package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.services.RiskAssessmentService;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request.RiskAssessmentRequestBody;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.response.RiskAssessmentResponseBody;

import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/risk-management")
public class RiskAssessmentController{

    private final RiskAssessmentService riskAssessmentService;

    @PostMapping("/risk")
    @ApiOperation("This interace will be called in order to make a Risk Evaluation.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Risk Evaluation."),
    })
    public RiskAssessmentResponseBody riskValidation(@Valid @RequestBody RiskAssessmentRequestBody  request) {
        return riskAssessmentService.riskValidation(request);
    }

}
