package com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)  // Ignora campos desconocidos durante la deserialización
public class ProspectLeadErrorResponse {
    @JsonProperty("errorCode")
    private String errorCode;

    @Valid
    @JsonProperty("errorMessage")
    private String errorMessage;
    }
