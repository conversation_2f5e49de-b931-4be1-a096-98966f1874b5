package com.tigo.galaxion.sales.facade.connector.riskassessment;

import com.tigo.galaxion.sales.facade.config.FeignRetryConfig;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request.RiskAssessmentRequestBody;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.response.RiskAssessmentResponseBody;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "tigo-riskassessment-service",
             url = "${environment.url.tigo-riskassessment-service}",
            configuration = { FeignRetryConfig.class, RiskAssessmentErrorDecoder.class }
             )
public interface RiskAssessmentClient{
    @PostMapping("/riskValidation")
    RiskAssessmentResponseBody riskValidation(@RequestBody RiskAssessmentRequestBody request);
}