package com.tigo.galaxion.sales.facade.controller;

import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentGenerateRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentIdentificationVerifyRequest;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentInitializeRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentValidationRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentVerifyRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentGenerateResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentIdentificationVerifyResponse;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentInitializeResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentQuestionResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentValidationResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentVerifyResponseBody;
import com.tigo.galaxion.sales.facade.services.EvidentService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

@Slf4j
@Api
@RestController
@RequiredArgsConstructor
public class EvidentController {

        private final EvidentService evidentService;

    @PostMapping("/api/evident/v1/identification/validation")
    @ApiOperation("Validation to identification")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Has been successfully evident identification")
    })
    public EvidentValidationResponseBody validateIdentification(
            @Valid @RequestBody EvidentValidationRequestBody request) {
                System.out.println("Controller Evident" + request);
        return evidentService.validateIdentification(request);
    }

        @PostMapping("/api/evident/v1/otp/initialize")
        @ApiOperation("Initialize OTP")
        @ApiResponses(value = {
                        @ApiResponse(code = 200, message = "Has been successfully evident Initialize")
        })
        public EvidentInitializeResponseBody initializeOtp(@Valid @RequestBody EvidentInitializeRequestBody request) {
                return evidentService.initializeOtp(request);
        }

        @PostMapping("/api/evident/v1/otp/generate")
        @ApiOperation("Generate OTP")
        @ApiResponses(value = {
                        @ApiResponse(code = 200, message = "Has been successfully evident generate")
        })
        public EvidentGenerateResponseBody generateOtp(@Valid @RequestBody EvidentGenerateRequestBody request) {
                return evidentService.generateOtp(request);
        }

        @PostMapping("/api/evident/v1/otp/verify")
        @ApiOperation("Verify OTP")
        @ApiResponses(value = {
                        @ApiResponse(code = 200, message = "Has been successfully evident generate")
        })
        public EvidentVerifyResponseBody verifyOtp(@Valid @RequestBody EvidentVerifyRequestBody request) {
                return evidentService.verifyOtp(request);
        }

        @GetMapping("/api/evident/v1/identification/question/{identification}/{onlyQuestionnaire}/{typeIdentification}/{validationRegister}")
        @ApiOperation("Question Identification")
        @ApiResponses(value = {
                        @ApiResponse(code = 200, message = "Has been successfully questioned")
        })
        public EvidentQuestionResponseBody questionIdentification(
                        @ApiParam(value = "Identification number", required = true, example = "32423423423") @PathVariable("identification") String identification,
                        @ApiParam(value = "Only questionnaire", required = true) @PathVariable("onlyQuestionnaire") String onlyQuestionnaire,
                        @ApiParam(value = "Type of identification", required = true, example = "CC") @PathVariable("typeIdentification") String typeIdentification,
                        @ApiParam(value = "Validation register", required = true, example = "32423423") @PathVariable("validationRegister") String validationRegister) {

                return evidentService.questionIdentification(identification, validationRegister, typeIdentification,
                                onlyQuestionnaire);
        }

        @PostMapping("/api/evident/v1/identification/verify")
        @ApiOperation("Verify Identification")
        @ApiResponses(value = {
                        @ApiResponse(code = 200, message = "Has been successfully verify identification")
        })
        public EvidentIdentificationVerifyResponse verifyIdentification(
                        @Valid @RequestBody EvidentIdentificationVerifyRequest request) {
                return evidentService.verifyIdentification(request);
        }

}
