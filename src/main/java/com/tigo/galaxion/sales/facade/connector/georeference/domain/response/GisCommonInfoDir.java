package com.tigo.galaxion.sales.facade.connector.georeference.domain.response;

import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class GisCommonInfoDir {
    @JsonProperty("addressCode")
    private String addressCode;

    @Size(max = 320)
    @JsonProperty("normalizedAddress")
    private String normalizedAddress;

    @JsonProperty("latitude")
    private String latitude;

    @JsonProperty("longitude")
    private String longitude;

    @Size(min = 1, max = 2)
    @JsonProperty("stratum")
    private String stratum;

    @JsonProperty("rural")
    private String rural;

    @JsonProperty("georeferencingStatus")
    private String georeferencingStatus;

    @JsonProperty("providerAddressCode")
    private String providerAddressCode;

    @JsonProperty("countryCode")
    private Number countryCode;

    @Size(max = 6)
    @JsonProperty("departmentCode")
    private String departmentCode;

    @Size(max = 6)
    @JsonProperty("municipalityCode")
    private String municipalityCode;

    @Size(max = 6)
    @JsonProperty("neighborhoodCode")
    private String neighborhoodCode;

    @Size(max = 320)
    @JsonProperty("daneBlockCode")
    private Long daneBlockCode;

    @JsonProperty("predialCode")
    private Long predialCode;

    @JsonProperty("neighborhoodName")
    private String neighborhoodName;
    
    @JsonProperty("microzone")
    private String microzone;

    @JsonProperty("descriptionAddress")
    private String descriptionAddress;
}
