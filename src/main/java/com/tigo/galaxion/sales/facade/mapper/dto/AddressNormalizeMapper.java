package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.georeference.domain.request.GeoreferenceRequest;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.request.GeoreferenceRequestBody;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.response.GeoreferenceResponseBody;
import com.tigo.galaxion.sales.facade.domain.request.AddressNormalizeRequestBody;
import com.tigo.galaxion.sales.facade.domain.response.AddressNormalizeResponse;
import com.tigo.galaxion.sales.facade.domain.response.AddressNormalizeResponseBody;
import com.tigo.galaxion.sales.facade.domain.response.AddressNormalizeGisCommonInfoDir;

public class AddressNormalizeMapper {

    private AddressNormalizeMapper() {
        throw new IllegalStateException("Utility class");
      }
    
    public static GeoreferenceRequestBody buildAddressRequest(AddressNormalizeRequestBody requestBody) {

        var request = requestBody.getAddressNormalizeRequest();
        var georeferenceRequest = GeoreferenceRequest.builder()
        .countryCode(request.getCountryCode())
        .departmentCode(request.getDepartmentCode())
        .municipalityCode(request.getMunicipalityCode())
        .naturalAddress(request.getNaturalAddress())
        .build();

        return GeoreferenceRequestBody
              .builder()
              .georreferenceRequest(georeferenceRequest)
              .build();
    }
    public static AddressNormalizeResponseBody buildAddressResponse(GeoreferenceResponseBody bodyResponse) {
      
      var response = bodyResponse.getGeorreferenceResponse();

       var responseDetail = AddressNormalizeGisCommonInfoDir
                .builder()
                .addressCode(response.getGisCommonInfoDir().getAddressCode())
                .normalizedAddress(response.getGisCommonInfoDir().getNormalizedAddress())
                .latitude(response.getGisCommonInfoDir().getLatitude())
                .longitude(response.getGisCommonInfoDir().getLongitude())
                .stratum(response.getGisCommonInfoDir().getStratum())
                .rural(response.getGisCommonInfoDir().getRural())
                .georeferencingStatus(response.getGisCommonInfoDir().getGeoreferencingStatus())
                .providerAddressCode(response.getGisCommonInfoDir().getProviderAddressCode())
                .countryCode(response.getGisCommonInfoDir().getCountryCode())
                .departmentCode(response.getGisCommonInfoDir().getDepartmentCode())
                .neighborhoodCode(response.getGisCommonInfoDir().getNeighborhoodCode())
                .municipalityCode(response.getGisCommonInfoDir().getMunicipalityCode())
                .daneBlockCode(response.getGisCommonInfoDir().getDaneBlockCode())
                .predialCode(response.getGisCommonInfoDir().getPredialCode())
                .neighborhoodName(response.getGisCommonInfoDir().getNeighborhoodName())
                .microzone(response.getGisCommonInfoDir().getMicrozone())
                .descriptionAddress(response.getGisCommonInfoDir().getDescriptionAddress())
                .build();
                
   var addressNormalizeResponse = AddressNormalizeResponse
                .builder()
                .naturalAddress(response.getNaturalAddress())
                .gisCommonInfoDir(responseDetail)
                .build();

   return AddressNormalizeResponseBody
                .builder()
                .addressNormalizeResponse(addressNormalizeResponse)
                .build();
    }
}
