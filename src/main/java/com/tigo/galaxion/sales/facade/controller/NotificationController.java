package com.tigo.galaxion.sales.facade.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseSaveNotification;
import com.tigo.galaxion.sales.facade.services.NotificationService;

import feign.Body;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("/api")
public class NotificationController {
    
}
