//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:48:22 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.process_task_ex;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for TaskRequest complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="TaskRequest"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="CallID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Number" type="{http://www.w3.org/2001/XMLSchema}Integer"/&gt;
 *         &lt;element name="Priority" type="{http://www.w3.org/2001/XMLSchema}Integer"/&gt;
 *         &lt;element name="Duration" type="{http://www.w3.org/2001/XMLSchema}Integer"/&gt;
 *         &lt;element name="EarlyStart" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="LateStart" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="DueDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="OpenDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Area" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Region" type="{http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap}Region"/&gt;
 *         &lt;element name="District" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Street" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="City" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CountryID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TDRequired" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="RequiredCrewSize" type="{http://www.w3.org/2001/XMLSchema}Integer" minOccurs="0"/&gt;
 *         &lt;element name="NumberOfRequiredEngineers" type="{http://www.w3.org/2001/XMLSchema}Integer" minOccurs="0"/&gt;
 *         &lt;element name="TaskTypeCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TaskType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCComment" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCCRMComment" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCContactEmail" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCCustomerCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCustomerPhoneNumber" type="{http://www.w3.org/2001/XMLSchema}Integer" minOccurs="0"/&gt;
 *         &lt;element name="MCStatusFCVToken" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="Customer" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ContactPhoneNumber" type="{http://www.w3.org/2001/XMLSchema}Integer" minOccurs="0"/&gt;
 *         &lt;element name="MCWorkPackageDescription" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCConnectionData" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCBillingAccountInfo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="AppointmentStart" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="AppointmentFinish" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Latitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Longitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCMVServices" type="{http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap}MCServices" minOccurs="0"/&gt;
 *         &lt;element name="MCMVAssets" type="{http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap}MCMVAssets" minOccurs="0"/&gt;
 *         &lt;element name="ContactName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCInfoCustomerSite" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCOpeningReason" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ContractType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCCustomerClass" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCServicePaid" type="{http://www.w3.org/2001/XMLSchema}Integer" minOccurs="0"/&gt;
 *         &lt;element name="MCCustomerIdentityNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCConfirmationStatus" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCCustomsSeal" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCRecurrentClient" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCTap" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCBoca" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TaskRequest", propOrder = {
    "callID",
    "number",
    "priority",
    "duration",
    "earlyStart",
    "lateStart",
    "dueDate",
    "openDate",
    "area",
    "region",
    "district",
    "street",
    "city",
    "mcState",
    "countryID",
    "tdRequired",
    "requiredCrewSize",
    "numberOfRequiredEngineers",
    "taskTypeCategory",
    "taskType",
    "mcComment",
    "mccrmComment",
    "mcContactEmail",
    "mcCustomerCode",
    "mcCustomerPhoneNumber",
    "mcStatusFCVToken",
    "customer",
    "contactPhoneNumber",
    "mcWorkPackageDescription",
    "mcConnectionData",
    "mcBillingAccountInfo",
    "appointmentStart",
    "appointmentFinish",
    "latitude",
    "longitude",
    "mcmvServices",
    "mcmvAssets",
    "contactName",
    "mcInfoCustomerSite",
    "mcOpeningReason",
    "contractType",
    "mcCustomerClass",
    "mcServicePaid",
    "mcCustomerIdentityNumber",
    "mcConfirmationStatus",
    "mcCustomsSeal",
    "mcRecurrentClient",
    "mcTap",
    "mcBoca"
})
public class TaskRequest {

    @XmlElement(name = "CallID", required = true)
    protected String callID;
    @XmlElement(name = "Number")
    protected Integer number;
    @XmlElement(name = "Priority")
    protected Integer priority;
    @XmlElement(name = "Duration")
    protected Integer duration;
    @XmlElement(name = "EarlyStart", required = true)
    @XmlSchemaType(name = "date")
    protected String earlyStart;
    @XmlElement(name = "LateStart", required = true)
    @XmlSchemaType(name = "date")
    protected String lateStart;
    @XmlElement(name = "DueDate", required = true)
    @XmlSchemaType(name = "date")
    protected String dueDate;
    @XmlElement(name = "OpenDate", required = true)
    @XmlSchemaType(name = "date")
    protected String openDate;
    @XmlElement(name = "Area", required = true)
    protected String area;
    @XmlElement(name = "Region", required = true)
    protected Region region;
    @XmlElement(name = "District", required = true)
    protected String district;
    @XmlElement(name = "Street", required = true)
    protected String street;
    @XmlElement(name = "City", required = true)
    protected String city;
    @XmlElement(name = "MCState", required = true)
    protected String mcState;
    @XmlElement(name = "CountryID", required = true)
    protected String countryID;
    @XmlElement(name = "TDRequired")
    protected boolean tdRequired;
    @XmlElement(name = "RequiredCrewSize")
    protected Integer requiredCrewSize;
    @XmlElement(name = "NumberOfRequiredEngineers")
    protected Integer numberOfRequiredEngineers;
    @XmlElement(name = "TaskTypeCategory", required = true)
    protected String taskTypeCategory;
    @XmlElement(name = "TaskType", required = true)
    protected String taskType;
    @XmlElement(name = "MCComment")
    protected String mcComment;
    @XmlElement(name = "MCCRMComment")
    protected String mccrmComment;
    @XmlElement(name = "MCContactEmail")
    protected String mcContactEmail;
    @XmlElement(name = "MCCustomerCode", required = true)
    protected String mcCustomerCode;
    @XmlElement(name = "MCCustomerPhoneNumber")
    protected Long mcCustomerPhoneNumber;
    @XmlElement(name = "MCStatusFCVToken")
    protected String mcStatusFCVToken;
    @XmlElement(name = "Customer", required = true)
    protected String customer;
    @XmlElement(name = "ContactPhoneNumber")
    protected Long contactPhoneNumber;
    @XmlElement(name = "MCWorkPackageDescription", required = true)
    protected String mcWorkPackageDescription;
    @XmlElement(name = "MCConnectionData", required = true)
    protected String mcConnectionData;
    @XmlElement(name = "MCBillingAccountInfo", required = true)
    protected String mcBillingAccountInfo;
    @XmlElement(name = "AppointmentStart", required = true)
    @XmlSchemaType(name = "date")
    protected String appointmentStart;
    @XmlElement(name = "AppointmentFinish", required = true)
    @XmlSchemaType(name = "date")
    protected String appointmentFinish;
    @XmlElement(name = "Latitude", required = true)
    protected String latitude;
    @XmlElement(name = "Longitude", required = true)
    protected String longitude;
    @XmlElement(name = "MCMVServices")
    protected MCServices mcmvServices;
    @XmlElement(name = "MCMVAssets")
    protected MCMVAssets mcmvAssets;
    @XmlElement(name = "ContactName")
    protected String contactName;
    @XmlElement(name = "MCInfoCustomerSite")
    protected String mcInfoCustomerSite;
    @XmlElement(name = "MCOpeningReason")
    protected String mcOpeningReason;
    @XmlElement(name = "ContractType")
    protected String contractType;
    @XmlElement(name = "MCCustomerClass")
    protected String mcCustomerClass;
    @XmlElement(name = "MCServicePaid")
    protected Integer mcServicePaid;
    @XmlElement(name = "MCCustomerIdentityNumber")
    protected String mcCustomerIdentityNumber;
    @XmlElement(name = "MCConfirmationStatus")
    protected String mcConfirmationStatus;
    @XmlElement(name = "MCCustomsSeal")
    protected String mcCustomsSeal;
    @XmlElement(name = "MCRecurrentClient")
    protected String mcRecurrentClient;
    @XmlElement(name = "MCTap", required = true)
    protected String mcTap;
    @XmlElement(name = "MCBoca", required = true)
    protected String mcBoca;

    /**
     * Gets the value of the callID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCallID() {
        return callID;
    }

    /**
     * Sets the value of the callID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCallID(String value) {
        this.callID = value;
    }

    /**
     * Gets the value of the number property.
     * 
     */
    public Integer getNumber() {
        return number;
    }

    /**
     * Sets the value of the number property.
     * 
     */
    public void setNumber(Integer value) {
        this.number = value;
    }

    /**
     * Gets the value of the priority property.
     * 
     */
    public Integer getPriority() {
        return priority;
    }

    /**
     * Sets the value of the priority property.
     * 
     */
    public void setPriority(Integer value) {
        this.priority = value;
    }

    /**
     * Gets the value of the duration property.
     * 
     */
    public Integer getDuration() {
        return duration;
    }

    /**
     * Sets the value of the duration property.
     * 
     */
    public void setDuration(Integer value) {
        this.duration = value;
    }

    /**
     * Gets the value of the earlyStart property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEarlyStart() {
        return earlyStart;
    }

    /**
     * Sets the value of the earlyStart property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEarlyStart(String value) {
        this.earlyStart = value;
    }

    /**
     * Gets the value of the lateStart property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLateStart() {
        return lateStart;
    }

    /**
     * Sets the value of the lateStart property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLateStart(String value) {
        this.lateStart = value;
    }

    /**
     * Gets the value of the dueDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDueDate() {
        return dueDate;
    }

    /**
     * Sets the value of the dueDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDueDate(String value) {
        this.dueDate = value;
    }

    /**
     * Gets the value of the openDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOpenDate() {
        return openDate;
    }

    /**
     * Sets the value of the openDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOpenDate(String value) {
        this.openDate = value;
    }

    /**
     * Gets the value of the area property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArea() {
        return area;
    }

    /**
     * Sets the value of the area property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArea(String value) {
        this.area = value;
    }

    /**
     * Gets the value of the region property.
     * 
     * @return
     *     possible object is
     *     {@link Region }
     *     
     */
    public Region getRegion() {
        return region;
    }

    /**
     * Sets the value of the region property.
     * 
     * @param value
     *     allowed object is
     *     {@link Region }
     *     
     */
    public void setRegion(Region value) {
        this.region = value;
    }

    /**
     * Gets the value of the district property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDistrict() {
        return district;
    }

    /**
     * Sets the value of the district property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDistrict(String value) {
        this.district = value;
    }

    /**
     * Gets the value of the street property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStreet() {
        return street;
    }

    /**
     * Sets the value of the street property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStreet(String value) {
        this.street = value;
    }

    /**
     * Gets the value of the city property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the city property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * Gets the value of the mcState property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCState() {
        return mcState;
    }

    /**
     * Sets the value of the mcState property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCState(String value) {
        this.mcState = value;
    }

    /**
     * Gets the value of the countryID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCountryID() {
        return countryID;
    }

    /**
     * Sets the value of the countryID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCountryID(String value) {
        this.countryID = value;
    }

    /**
     * Gets the value of the tdRequired property.
     * 
     */
    public boolean isTDRequired() {
        return tdRequired;
    }

    /**
     * Sets the value of the tdRequired property.
     * 
     */
    public void setTDRequired(boolean value) {
        this.tdRequired = value;
    }

    /**
     * Gets the value of the requiredCrewSize property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getRequiredCrewSize() {
        return requiredCrewSize;
    }

    /**
     * Sets the value of the requiredCrewSize property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setRequiredCrewSize(Integer value) {
        this.requiredCrewSize = value;
    }

    /**
     * Gets the value of the numberOfRequiredEngineers property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNumberOfRequiredEngineers() {
        return numberOfRequiredEngineers;
    }

    /**
     * Sets the value of the numberOfRequiredEngineers property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNumberOfRequiredEngineers(Integer value) {
        this.numberOfRequiredEngineers = value;
    }

    /**
     * Gets the value of the taskTypeCategory property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskTypeCategory() {
        return taskTypeCategory;
    }

    /**
     * Sets the value of the taskTypeCategory property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskTypeCategory(String value) {
        this.taskTypeCategory = value;
    }

    /**
     * Gets the value of the taskType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskType() {
        return taskType;
    }

    /**
     * Sets the value of the taskType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskType(String value) {
        this.taskType = value;
    }

    /**
     * Gets the value of the mcComment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCComment() {
        return mcComment;
    }

    /**
     * Sets the value of the mcComment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCComment(String value) {
        this.mcComment = value;
    }

    /**
     * Gets the value of the mccrmComment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCRMComment() {
        return mccrmComment;
    }

    /**
     * Sets the value of the mccrmComment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCRMComment(String value) {
        this.mccrmComment = value;
    }

    /**
     * Gets the value of the mcContactEmail property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCContactEmail() {
        return mcContactEmail;
    }

    /**
     * Sets the value of the mcContactEmail property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCContactEmail(String value) {
        this.mcContactEmail = value;
    }

    /**
     * Gets the value of the mcCustomerCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomerCode() {
        return mcCustomerCode;
    }

    /**
     * Sets the value of the mcCustomerCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomerCode(String value) {
        this.mcCustomerCode = value;
    }

    /**
     * Gets the value of the mcCustomerPhoneNumber property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getMCCustomerPhoneNumber() {
        return mcCustomerPhoneNumber;
    }

    /**
     * Sets the value of the mcCustomerPhoneNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setMCCustomerPhoneNumber(Long value) {
        this.mcCustomerPhoneNumber = value;
    }

    /**
     * Gets the value of the mcStatusFCVToken property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCStatusFCVToken() {
        return mcStatusFCVToken;
    }

    /**
     * Sets the value of the mcStatusFCVToken property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCStatusFCVToken(String value) {
        this.mcStatusFCVToken = value;
    }

    /**
     * Gets the value of the customer property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomer() {
        return customer;
    }

    /**
     * Sets the value of the customer property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomer(String value) {
        this.customer = value;
    }

    /**
     * Gets the value of the contactPhoneNumber property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getContactPhoneNumber() {
        return contactPhoneNumber;
    }

    /**
     * Sets the value of the contactPhoneNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setContactPhoneNumber(Long value) {
        this.contactPhoneNumber = value;
    }

    /**
     * Gets the value of the mcWorkPackageDescription property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCWorkPackageDescription() {
        return mcWorkPackageDescription;
    }

    /**
     * Sets the value of the mcWorkPackageDescription property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCWorkPackageDescription(String value) {
        this.mcWorkPackageDescription = value;
    }

    /**
     * Gets the value of the mcConnectionData property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCConnectionData() {
        return mcConnectionData;
    }

    /**
     * Sets the value of the mcConnectionData property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCConnectionData(String value) {
        this.mcConnectionData = value;
    }

    /**
     * Gets the value of the mcBillingAccountInfo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCBillingAccountInfo() {
        return mcBillingAccountInfo;
    }

    /**
     * Sets the value of the mcBillingAccountInfo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCBillingAccountInfo(String value) {
        this.mcBillingAccountInfo = value;
    }

    /**
     * Gets the value of the appointmentStart property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAppointmentStart() {
        return appointmentStart;
    }

    /**
     * Sets the value of the appointmentStart property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAppointmentStart(String value) {
        this.appointmentStart = value;
    }

    /**
     * Gets the value of the appointmentFinish property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAppointmentFinish() {
        return appointmentFinish;
    }

    /**
     * Sets the value of the appointmentFinish property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAppointmentFinish(String value) {
        this.appointmentFinish = value;
    }

    /**
     * Gets the value of the latitude property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLatitude() {
        return latitude;
    }

    /**
     * Sets the value of the latitude property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLatitude(String value) {
        this.latitude = value;
    }

    /**
     * Gets the value of the longitude property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLongitude() {
        return longitude;
    }

    /**
     * Sets the value of the longitude property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLongitude(String value) {
        this.longitude = value;
    }

    /**
     * Gets the value of the mcmvServices property.
     * 
     * @return
     *     possible object is
     *     {@link MCServices }
     *     
     */
    public MCServices getMCMVServices() {
        return mcmvServices;
    }

    /**
     * Sets the value of the mcmvServices property.
     * 
     * @param value
     *     allowed object is
     *     {@link MCServices }
     *     
     */
    public void setMCMVServices(MCServices value) {
        this.mcmvServices = value;
    }

    /**
     * Gets the value of the mcmvAssets property.
     * 
     * @return
     *     possible object is
     *     {@link MCMVAssets }
     *     
     */
    public MCMVAssets getMCMVAssets() {
        return mcmvAssets;
    }

    /**
     * Sets the value of the mcmvAssets property.
     * 
     * @param value
     *     allowed object is
     *     {@link MCMVAssets }
     *     
     */
    public void setMCMVAssets(MCMVAssets value) {
        this.mcmvAssets = value;
    }

    /**
     * Gets the value of the contactName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactName() {
        return contactName;
    }

    /**
     * Sets the value of the contactName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactName(String value) {
        this.contactName = value;
    }

    /**
     * Gets the value of the mcInfoCustomerSite property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCInfoCustomerSite() {
        return mcInfoCustomerSite;
    }

    /**
     * Sets the value of the mcInfoCustomerSite property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCInfoCustomerSite(String value) {
        this.mcInfoCustomerSite = value;
    }

    /**
     * Gets the value of the mcOpeningReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCOpeningReason() {
        return mcOpeningReason;
    }

    /**
     * Sets the value of the mcOpeningReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCOpeningReason(String value) {
        this.mcOpeningReason = value;
    }

    /**
     * Gets the value of the contractType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContractType() {
        return contractType;
    }

    /**
     * Sets the value of the contractType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContractType(String value) {
        this.contractType = value;
    }

    /**
     * Gets the value of the mcCustomerClass property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomerClass() {
        return mcCustomerClass;
    }

    /**
     * Sets the value of the mcCustomerClass property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomerClass(String value) {
        this.mcCustomerClass = value;
    }

    /**
     * Gets the value of the mcServicePaid property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getMCServicePaid() {
        return mcServicePaid;
    }

    /**
     * Sets the value of the mcServicePaid property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setMCServicePaid(Integer value) {
        this.mcServicePaid = value;
    }

    /**
     * Gets the value of the mcCustomerIdentityNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomerIdentityNumber() {
        return mcCustomerIdentityNumber;
    }

    /**
     * Sets the value of the mcCustomerIdentityNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomerIdentityNumber(String value) {
        this.mcCustomerIdentityNumber = value;
    }

    /**
     * Gets the value of the mcConfirmationStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCConfirmationStatus() {
        return mcConfirmationStatus;
    }

    /**
     * Sets the value of the mcConfirmationStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCConfirmationStatus(String value) {
        this.mcConfirmationStatus = value;
    }

    /**
     * Gets the value of the mcCustomsSeal property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomsSeal() {
        return mcCustomsSeal;
    }

    /**
     * Sets the value of the mcCustomsSeal property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomsSeal(String value) {
        this.mcCustomsSeal = value;
    }

    /**
     * Gets the value of the mcRecurrentClient property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCRecurrentClient() {
        return mcRecurrentClient;
    }

    /**
     * Sets the value of the mcRecurrentClient property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCRecurrentClient(String value) {
        this.mcRecurrentClient = value;
    }

    /**
     * Gets the value of the mcTap property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCTap() {
        return mcTap;
    }

    /**
     * Sets the value of the mcTap property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCTap(String value) {
        this.mcTap = value;
    }

    /**
     * Gets the value of the mcBoca property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCBoca() {
        return mcBoca;
    }

    /**
     * Sets the value of the mcBoca property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCBoca(String value) {
        this.mcBoca = value;
    }

}
