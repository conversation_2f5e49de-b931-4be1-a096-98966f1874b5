package com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.enumeration;

public enum IdentificationTypeEnum {
    /*
     * Certificate Type.
     * 
     * CC: Citizenship Card
     * NIT: Tax identification number
     * NE: NIT immigration
     * CE: Foreigner ID
     * PEP: Special Residence Permit
     * PP: Passport
     */
    CC("CC"),
    NIT("NIT"),
    NE("NE"),
    CE("CE"),
    PEP("PEP"),
    PP("PP");

    private final String name;

    IdentificationTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
