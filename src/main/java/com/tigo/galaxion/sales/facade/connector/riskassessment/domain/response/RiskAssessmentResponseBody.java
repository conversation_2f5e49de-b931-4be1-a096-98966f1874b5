package com.tigo.galaxion.sales.facade.connector.riskassessment.domain.response;

import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request.PropertyObject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class RiskAssessmentResponseBody {
    private String code;
    private String message;
    private Score score;
    private Integer availableCredit;
    private PropertyObject[] properties;
}
