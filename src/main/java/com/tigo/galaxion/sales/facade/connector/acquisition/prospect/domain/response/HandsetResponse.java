package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Set;

@NoArgsConstructor
@SuperBuilder
@AllArgsConstructor
@Getter
public class HandsetResponse extends EquipmentResponse {

    private String capacity;

    private String networkCompatibility;

    private Set<String> simCardTypes;

}
