package com.tigo.galaxion.sales.facade.connector.collection;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(value = "collection-service", url = "${environment.url.collections-service}", configuration = CollectionErrorDecoder.class)
public interface CollectionClient {

    @GetMapping("/collection/{account_id}/is-in-collection")
    Boolean checkAccountIsInCollection(@PathVariable("account_id") String accountId);
}
