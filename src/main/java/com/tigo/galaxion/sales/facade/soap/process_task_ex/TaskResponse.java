//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:48:22 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.process_task_ex;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for TaskResponse complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="TaskResponse"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Key" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Revision" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Stamp" type="{http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap}Stamp"/&gt;
 *         &lt;element name="CallID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Number" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="EarlyStart" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="DueDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="LateStart" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Priority" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Customer" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Calendar" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Region" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="District" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Postcode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PreferredEngineers" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ContractType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OpenDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="ContactDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="ConfirmationDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="TaskType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Duration" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="RequiredEngineers" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="NumberOfRequiredEngineers" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="RequiredSkills1" type="{hhttp://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap}TaskRequiredSkill1"/&gt;
 *         &lt;element name="RequiredSkills2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="EngineerType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="RequiredEngineerTools" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Critical" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="TimeDependencies" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="EngineerDependencies" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="AppointmentStart" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="AppointmentFinish" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="ContactName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ContactPhoneNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="BinaryData" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Latitude" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Longitude" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="GISDataSource" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Street" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="City" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="State" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TaskStatusContext" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="IsCrewTask" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="CountryID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="EngineerRequirements" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsScheduled" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="CustomerEmail" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ExcludedEngineers" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="RequiredCrewSize" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="InJeopardy" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Pinned" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="JeopardyState" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="DisplayStatus" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="DispatchDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="ScheduleDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="DisplayDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="OnSiteDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CustomerReference" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="StateSubdivision" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CitySubdivision" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Team" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Signature" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ExternalRefID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PartsUsed" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Assets" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BackReportings" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TaskTypeCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="WorkOrderItem" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="User_CustomerAccount" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsAppointment" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="TravelDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="CompletionDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="Attachments" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CancellationDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="IsMegatask" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="IsBundled" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="IsManuallyBundled" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="Megatask" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MegataskPureDuration" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="BundlerConfiguration" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Subtasks" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SkillsDuration" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CustomerAccount" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Area" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IncompleteReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCWorkPackageDescription" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCComment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCRMComment" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MCContactEmail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCustomerCode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCCustomerPhoneNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCSaldoPending" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCBillingAccountInfo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCConnectionData" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LastRejectedEngineer" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="RejectedDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="RejectionReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CancellationReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCConfirmationStatus" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCMVPartsRequired" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCMVMaterialUsed" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCMVEquipmentUsed" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCMVEquipmentCollected" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCNoMaterialUsed" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCNoEquipmentUsed" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCCauseReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCLaboresUsed" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCustomsSeal" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCodeBobina1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCodeBobina2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCMVServices" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="DynamicPriority" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ExternalRefIDExtension" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ServiceSummary" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="StreetSmartJob" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="RecurrenceTask" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="RTIsRecurringTask" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="RTUpdateAllRecurrences" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="RTIsPrime" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ArchiveStatus" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="AssignedEngineerName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MobileKey" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="Unit" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SupervisorStatusGroup" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsSingleTask" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="FieldCommentEng" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCustomerClass" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCOpeningReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCZonaRamal" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCTap" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCBoca" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCInfoCustomerSite" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCTaskClosureLongitude" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCTaskClosureLatitude" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCTaskClosureGeoToken" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCTaskClosureGeoTokenEntered" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="URLAuditInfo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="URLSurvey" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCBuildingType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCServicesSignature" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCPlaca" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCustomerIdentityNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsDESent" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ReturnVerification" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IsETASent" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="IsReminder24HourSent" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="IsSurveySent" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ScheduleLowerBound" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="ScheduleUpperBound" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="SurveyAnswer" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SurveyComment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ServiceAccepted" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCGoogleLatitude" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="MCGoogleLongitude" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="MCCustomerAssets" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCPTWorkingArea" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCPTFilter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCTPPolygonFilter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="WorkingArea" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCDispatchOMSent" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCDEComment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCEMAttachments" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCContactPhoneNumbers" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="TaskAppointmentTime" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TaskAppointmentUpdated" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCServicePaid" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCUnscheduledForNotPaid" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCCEMABCancellationNotification" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCUnscheduledForNotPaidCounter" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCTaskUnpaidReminderSent" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCCEMNoMsgOnDispatcherReschedule" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCIsNonABTaskScheduledNotificationSent" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCNewTaskAssigned" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCWifiCertService" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCShowCertificateWifiInitiate" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCCertificateResultWifi" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCertificationActionType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCGenerateCertificateWifi" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCCertificateWifiRequired" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="MCTAPHighFrecuency" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCTAPLowFrecuency" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCSplitterHighFrecuency" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCSplitterLowFrecuency" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCMHighFrecuency" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCMLowFrecuency" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCResendClosureOM" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TaskResponse", propOrder = {
    "key",
    "revision",
    "stamp",
    "callID",
    "number",
    "earlyStart",
    "dueDate",
    "lateStart",
    "priority",
    "status",
    "customer",
    "calendar",
    "region",
    "district",
    "postcode",
    "preferredEngineers",
    "contractType",
    "openDate",
    "contactDate",
    "confirmationDate",
    "taskType",
    "duration",
    "requiredEngineers",
    "numberOfRequiredEngineers",
    "requiredSkills1",
    "requiredSkills2",
    "engineerType",
    "requiredEngineerTools",
    "critical",
    "timeDependencies",
    "engineerDependencies",
    "appointmentStart",
    "appointmentFinish",
    "contactName",
    "contactPhoneNumber",
    "binaryData",
    "latitude",
    "longitude",
    "gisDataSource",
    "street",
    "city",
    "mcState",
    "state",
    "taskStatusContext",
    "isCrewTask",
    "countryID",
    "engineerRequirements",
    "isScheduled",
    "customerEmail",
    "excludedEngineers",
    "requiredCrewSize",
    "inJeopardy",
    "pinned",
    "jeopardyState",
    "displayStatus",
    "dispatchDate",
    "scheduleDate",
    "displayDate",
    "onSiteDate",
    "comment",
    "customerReference",
    "stateSubdivision",
    "citySubdivision",
    "team",
    "signature",
    "externalRefID",
    "partsUsed",
    "assets",
    "backReportings",
    "taskTypeCategory",
    "workOrderItem",
    "userCustomerAccount",
    "isAppointment",
    "travelDate",
    "completionDate",
    "attachments",
    "cancellationDate",
    "isMegatask",
    "isBundled",
    "isManuallyBundled",
    "megatask",
    "megataskPureDuration",
    "bundlerConfiguration",
    "subtasks",
    "skillsDuration",
    "customerAccount",
    "area",
    "incompleteReason",
    "mcWorkPackageDescription",
    "mcComment",
    "mccrmComment",
    "mcContactEmail",
    "mcCustomerCode",
    "mcCustomerPhoneNumber",
    "mcSaldoPending",
    "mcBillingAccountInfo",
    "mcConnectionData",
    "lastRejectedEngineer",
    "rejectedDate",
    "rejectionReason",
    "cancellationReason",
    "mcConfirmationStatus",
    "mcmvPartsRequired",
    "mcmvMaterialUsed",
    "mcmvEquipmentUsed",
    "mcmvEquipmentCollected",
    "mcNoMaterialUsed",
    "mcNoEquipmentUsed",
    "mcCauseReason",
    "mcLaboresUsed",
    "mcCustomsSeal",
    "mcCodeBobina1",
    "mcCodeBobina2",
    "mcmvServices",
    "dynamicPriority",
    "externalRefIDExtension",
    "serviceSummary",
    "streetSmartJob",
    "recurrenceTask",
    "rtIsRecurringTask",
    "rtUpdateAllRecurrences",
    "rtIsPrime",
    "archiveStatus",
    "assignedEngineerName",
    "mobileKey",
    "unit",
    "supervisorStatusGroup",
    "isSingleTask",
    "fieldCommentEng",
    "mcCustomerClass",
    "mcOpeningReason",
    "mcZonaRamal",
    "mcTap",
    "mcBoca",
    "mcInfoCustomerSite",
    "mcTaskClosureLongitude",
    "mcTaskClosureLatitude",
    "mcTaskClosureGeoToken",
    "mcTaskClosureGeoTokenEntered",
    "urlAuditInfo",
    "urlSurvey",
    "mcBuildingType",
    "mcServicesSignature",
    "mcPlaca",
    "mcCustomerIdentityNumber",
    "isDESent",
    "returnVerification",
    "isETASent",
    "isReminder24HourSent",
    "isSurveySent",
    "scheduleLowerBound",
    "scheduleUpperBound",
    "surveyAnswer",
    "surveyComment",
    "serviceAccepted",
    "mcGoogleLatitude",
    "mcGoogleLongitude",
    "mcCustomerAssets",
    "mcptWorkingArea",
    "mcptFilter",
    "mctpPolygonFilter",
    "workingArea",
    "mcDispatchOMSent",
    "mcdeComment",
    "mccemAttachments",
    "mcContactPhoneNumbers",
    "taskAppointmentTime",
    "taskAppointmentUpdated",
    "mcServicePaid",
    "mcUnscheduledForNotPaid",
    "mccemabCancellationNotification",
    "mcUnscheduledForNotPaidCounter",
    "mcTaskUnpaidReminderSent",
    "mccemNoMsgOnDispatcherReschedule",
    "mcIsNonABTaskScheduledNotificationSent",
    "mcNewTaskAssigned",
    "mcWifiCertService",
    "mcShowCertificateWifiInitiate",
    "mcCertificateResultWifi",
    "mcCertificationActionType",
    "mcGenerateCertificateWifi",
    "mcCertificateWifiRequired",
    "mctapHighFrecuency",
    "mctapLowFrecuency",
    "mcSplitterHighFrecuency",
    "mcSplitterLowFrecuency",
    "mccmHighFrecuency",
    "mccmLowFrecuency",
    "mcResendClosureOM"
})
public class TaskResponse {

    @XmlElement(name = "Key")
    protected int key;
    @XmlElement(name = "Revision")
    protected int revision;
    @XmlElement(name = "Stamp", required = true)
    protected Stamp stamp;
    @XmlElement(name = "CallID", required = true)
    protected String callID;
    @XmlElement(name = "Number")
    protected int number;
    @XmlElement(name = "EarlyStart", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar earlyStart;
    @XmlElement(name = "DueDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar dueDate;
    @XmlElement(name = "LateStart", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lateStart;
    @XmlElement(name = "Priority")
    protected int priority;
    @XmlElement(name = "Status", required = true)
    protected String status;
    @XmlElement(name = "Customer", required = true)
    protected String customer;
    @XmlElement(name = "Calendar", required = true)
    protected String calendar;
    @XmlElement(name = "Region", required = true)
    protected String region;
    @XmlElement(name = "District", required = true)
    protected String district;
    @XmlElement(name = "Postcode", required = true)
    protected String postcode;
    @XmlElement(name = "PreferredEngineers", required = true)
    protected String preferredEngineers;
    @XmlElement(name = "ContractType", required = true)
    protected String contractType;
    @XmlElement(name = "OpenDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar openDate;
    @XmlElement(name = "ContactDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar contactDate;
    @XmlElement(name = "ConfirmationDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar confirmationDate;
    @XmlElement(name = "TaskType", required = true)
    protected String taskType;
    @XmlElement(name = "Duration")
    protected int duration;
    @XmlElement(name = "RequiredEngineers", required = true)
    protected String requiredEngineers;
    @XmlElement(name = "NumberOfRequiredEngineers")
    protected int numberOfRequiredEngineers;
    @XmlElement(name = "RequiredSkills1", required = true)
    protected TaskRequiredSkill1 requiredSkills1;
    @XmlElement(name = "RequiredSkills2", required = true)
    protected String requiredSkills2;
    @XmlElement(name = "EngineerType", required = true)
    protected String engineerType;
    @XmlElement(name = "RequiredEngineerTools", required = true)
    protected String requiredEngineerTools;
    @XmlElement(name = "Critical")
    protected int critical;
    @XmlElement(name = "TimeDependencies", required = true)
    protected String timeDependencies;
    @XmlElement(name = "EngineerDependencies", required = true)
    protected String engineerDependencies;
    @XmlElement(name = "AppointmentStart", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar appointmentStart;
    @XmlElement(name = "AppointmentFinish", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar appointmentFinish;
    @XmlElement(name = "ContactName", required = true)
    protected String contactName;
    @XmlElement(name = "ContactPhoneNumber")
    protected int contactPhoneNumber;
    @XmlElement(name = "BinaryData", required = true)
    protected String binaryData;
    @XmlElement(name = "Latitude")
    protected int latitude;
    @XmlElement(name = "Longitude")
    protected int longitude;
    @XmlElement(name = "GISDataSource")
    protected int gisDataSource;
    @XmlElement(name = "Street", required = true)
    protected String street;
    @XmlElement(name = "City", required = true)
    protected String city;
    @XmlElement(name = "MCState", required = true)
    protected String mcState;
    @XmlElement(name = "State", required = true)
    protected String state;
    @XmlElement(name = "TaskStatusContext")
    protected int taskStatusContext;
    @XmlElement(name = "IsCrewTask")
    protected int isCrewTask;
    @XmlElement(name = "CountryID", required = true)
    protected String countryID;
    @XmlElement(name = "EngineerRequirements", required = true)
    protected String engineerRequirements;
    @XmlElement(name = "IsScheduled")
    protected int isScheduled;
    @XmlElement(name = "CustomerEmail")
    protected int customerEmail;
    @XmlElement(name = "ExcludedEngineers")
    protected int excludedEngineers;
    @XmlElement(name = "RequiredCrewSize")
    protected int requiredCrewSize;
    @XmlElement(name = "InJeopardy")
    protected int inJeopardy;
    @XmlElement(name = "Pinned")
    protected int pinned;
    @XmlElement(name = "JeopardyState")
    protected int jeopardyState;
    @XmlElement(name = "DisplayStatus")
    protected int displayStatus;
    @XmlElement(name = "DispatchDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar dispatchDate;
    @XmlElement(name = "ScheduleDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar scheduleDate;
    @XmlElement(name = "DisplayDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar displayDate;
    @XmlElement(name = "OnSiteDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar onSiteDate;
    @XmlElement(name = "Comment", required = true)
    protected String comment;
    @XmlElement(name = "CustomerReference", required = true)
    protected String customerReference;
    @XmlElement(name = "StateSubdivision", required = true)
    protected String stateSubdivision;
    @XmlElement(name = "CitySubdivision", required = true)
    protected String citySubdivision;
    @XmlElement(name = "Team", required = true)
    protected String team;
    @XmlElement(name = "Signature", required = true)
    protected String signature;
    @XmlElement(name = "ExternalRefID", required = true)
    protected String externalRefID;
    @XmlElement(name = "PartsUsed", required = true)
    protected String partsUsed;
    @XmlElement(name = "Assets", required = true)
    protected String assets;
    @XmlElement(name = "BackReportings", required = true)
    protected String backReportings;
    @XmlElement(name = "TaskTypeCategory", required = true)
    protected String taskTypeCategory;
    @XmlElement(name = "WorkOrderItem", required = true)
    protected String workOrderItem;
    @XmlElement(name = "User_CustomerAccount", required = true)
    protected String userCustomerAccount;
    @XmlElement(name = "IsAppointment")
    protected int isAppointment;
    @XmlElement(name = "TravelDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar travelDate;
    @XmlElement(name = "CompletionDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar completionDate;
    @XmlElement(name = "Attachments", required = true)
    protected String attachments;
    @XmlElement(name = "CancellationDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar cancellationDate;
    @XmlElement(name = "IsMegatask")
    protected int isMegatask;
    @XmlElement(name = "IsBundled")
    protected int isBundled;
    @XmlElement(name = "IsManuallyBundled")
    protected int isManuallyBundled;
    @XmlElement(name = "Megatask", required = true)
    protected String megatask;
    @XmlElement(name = "MegataskPureDuration")
    protected int megataskPureDuration;
    @XmlElement(name = "BundlerConfiguration", required = true)
    protected String bundlerConfiguration;
    @XmlElement(name = "Subtasks", required = true)
    protected String subtasks;
    @XmlElement(name = "SkillsDuration", required = true)
    protected String skillsDuration;
    @XmlElement(name = "CustomerAccount", required = true)
    protected String customerAccount;
    @XmlElement(name = "Area", required = true)
    protected String area;
    @XmlElement(name = "IncompleteReason", required = true)
    protected String incompleteReason;
    @XmlElement(name = "MCWorkPackageDescription", required = true)
    protected String mcWorkPackageDescription;
    @XmlElement(name = "MCComment", required = true)
    protected String mcComment;
    @XmlElement(name = "MCCRMComment")
    protected String mccrmComment;
    @XmlElement(name = "MCContactEmail", required = true)
    protected String mcContactEmail;
    @XmlElement(name = "MCCustomerCode")
    protected int mcCustomerCode;
    @XmlElement(name = "MCCustomerPhoneNumber")
    protected int mcCustomerPhoneNumber;
    @XmlElement(name = "MCSaldoPending", required = true)
    protected String mcSaldoPending;
    @XmlElement(name = "MCBillingAccountInfo", required = true)
    protected String mcBillingAccountInfo;
    @XmlElement(name = "MCConnectionData", required = true)
    protected String mcConnectionData;
    @XmlElement(name = "LastRejectedEngineer", required = true)
    protected String lastRejectedEngineer;
    @XmlElement(name = "RejectedDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar rejectedDate;
    @XmlElement(name = "RejectionReason", required = true)
    protected String rejectionReason;
    @XmlElement(name = "CancellationReason", required = true)
    protected String cancellationReason;
    @XmlElement(name = "MCConfirmationStatus", required = true)
    protected String mcConfirmationStatus;
    @XmlElement(name = "MCMVPartsRequired", required = true)
    protected String mcmvPartsRequired;
    @XmlElement(name = "MCMVMaterialUsed", required = true)
    protected String mcmvMaterialUsed;
    @XmlElement(name = "MCMVEquipmentUsed", required = true)
    protected String mcmvEquipmentUsed;
    @XmlElement(name = "MCMVEquipmentCollected")
    protected int mcmvEquipmentCollected;
    @XmlElement(name = "MCNoMaterialUsed")
    protected int mcNoMaterialUsed;
    @XmlElement(name = "MCNoEquipmentUsed")
    protected int mcNoEquipmentUsed;
    @XmlElement(name = "MCCauseReason", required = true)
    protected String mcCauseReason;
    @XmlElement(name = "MCLaboresUsed", required = true)
    protected String mcLaboresUsed;
    @XmlElement(name = "MCCustomsSeal", required = true)
    protected String mcCustomsSeal;
    @XmlElement(name = "MCCodeBobina1", required = true)
    protected String mcCodeBobina1;
    @XmlElement(name = "MCCodeBobina2", required = true)
    protected String mcCodeBobina2;
    @XmlElement(name = "MCMVServices", required = true)
    protected String mcmvServices;
    @XmlElement(name = "DynamicPriority")
    protected int dynamicPriority;
    @XmlElement(name = "ExternalRefIDExtension")
    protected int externalRefIDExtension;
    @XmlElement(name = "ServiceSummary", required = true)
    protected String serviceSummary;
    @XmlElement(name = "StreetSmartJob")
    protected int streetSmartJob;
    @XmlElement(name = "RecurrenceTask", required = true)
    protected String recurrenceTask;
    @XmlElement(name = "RTIsRecurringTask")
    protected int rtIsRecurringTask;
    @XmlElement(name = "RTUpdateAllRecurrences")
    protected int rtUpdateAllRecurrences;
    @XmlElement(name = "RTIsPrime")
    protected int rtIsPrime;
    @XmlElement(name = "ArchiveStatus", required = true)
    protected String archiveStatus;
    @XmlElement(name = "AssignedEngineerName", required = true)
    protected String assignedEngineerName;
    @XmlElement(name = "MobileKey", required = true)
    protected String mobileKey;
    @XmlElement(name = "Unit", required = true)
    protected String unit;
    @XmlElement(name = "SupervisorStatusGroup", required = true)
    protected String supervisorStatusGroup;
    @XmlElement(name = "IsSingleTask")
    protected int isSingleTask;
    @XmlElement(name = "FieldCommentEng", required = true)
    protected String fieldCommentEng;
    @XmlElement(name = "MCCustomerClass", required = true)
    protected String mcCustomerClass;
    @XmlElement(name = "MCOpeningReason", required = true)
    protected String mcOpeningReason;
    @XmlElement(name = "MCZonaRamal", required = true)
    protected String mcZonaRamal;
    @XmlElement(name = "MCTap", required = true)
    protected String mcTap;
    @XmlElement(name = "MCBoca", required = true)
    protected String mcBoca;
    @XmlElement(name = "MCInfoCustomerSite", required = true)
    protected String mcInfoCustomerSite;
    @XmlElement(name = "MCTaskClosureLongitude")
    protected int mcTaskClosureLongitude;
    @XmlElement(name = "MCTaskClosureLatitude")
    protected int mcTaskClosureLatitude;
    @XmlElement(name = "MCTaskClosureGeoToken", required = true)
    protected String mcTaskClosureGeoToken;
    @XmlElement(name = "MCTaskClosureGeoTokenEntered", required = true)
    protected String mcTaskClosureGeoTokenEntered;
    @XmlElement(name = "URLAuditInfo", required = true)
    protected String urlAuditInfo;
    @XmlElement(name = "URLSurvey", required = true)
    protected String urlSurvey;
    @XmlElement(name = "MCBuildingType", required = true)
    protected String mcBuildingType;
    @XmlElement(name = "MCServicesSignature", required = true)
    protected String mcServicesSignature;
    @XmlElement(name = "MCPlaca", required = true)
    protected String mcPlaca;
    @XmlElement(name = "MCCustomerIdentityNumber", required = true)
    protected String mcCustomerIdentityNumber;
    @XmlElement(name = "IsDESent")
    protected int isDESent;
    @XmlElement(name = "ReturnVerification", required = true)
    protected String returnVerification;
    @XmlElement(name = "IsETASent")
    protected int isETASent;
    @XmlElement(name = "IsReminder24HourSent")
    protected int isReminder24HourSent;
    @XmlElement(name = "IsSurveySent")
    protected int isSurveySent;
    @XmlElement(name = "ScheduleLowerBound", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar scheduleLowerBound;
    @XmlElement(name = "ScheduleUpperBound", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar scheduleUpperBound;
    @XmlElement(name = "SurveyAnswer", required = true)
    protected String surveyAnswer;
    @XmlElement(name = "SurveyComment", required = true)
    protected String surveyComment;
    @XmlElement(name = "ServiceAccepted")
    protected int serviceAccepted;
    @XmlElement(name = "MCGoogleLatitude")
    protected float mcGoogleLatitude;
    @XmlElement(name = "MCGoogleLongitude")
    protected float mcGoogleLongitude;
    @XmlElement(name = "MCCustomerAssets", required = true)
    protected String mcCustomerAssets;
    @XmlElement(name = "MCPTWorkingArea", required = true)
    protected String mcptWorkingArea;
    @XmlElement(name = "MCPTFilter", required = true)
    protected String mcptFilter;
    @XmlElement(name = "MCTPPolygonFilter", required = true)
    protected String mctpPolygonFilter;
    @XmlElement(name = "WorkingArea", required = true)
    protected String workingArea;
    @XmlElement(name = "MCDispatchOMSent")
    protected int mcDispatchOMSent;
    @XmlElement(name = "MCDEComment", required = true)
    protected String mcdeComment;
    @XmlElement(name = "MCCEMAttachments", required = true)
    protected String mccemAttachments;
    @XmlElement(name = "MCContactPhoneNumbers")
    protected int mcContactPhoneNumbers;
    @XmlElement(name = "TaskAppointmentTime", required = true)
    protected String taskAppointmentTime;
    @XmlElement(name = "TaskAppointmentUpdated")
    protected int taskAppointmentUpdated;
    @XmlElement(name = "MCServicePaid")
    protected int mcServicePaid;
    @XmlElement(name = "MCUnscheduledForNotPaid")
    protected int mcUnscheduledForNotPaid;
    @XmlElement(name = "MCCEMABCancellationNotification")
    protected int mccemabCancellationNotification;
    @XmlElement(name = "MCUnscheduledForNotPaidCounter")
    protected int mcUnscheduledForNotPaidCounter;
    @XmlElement(name = "MCTaskUnpaidReminderSent")
    protected int mcTaskUnpaidReminderSent;
    @XmlElement(name = "MCCEMNoMsgOnDispatcherReschedule")
    protected int mccemNoMsgOnDispatcherReschedule;
    @XmlElement(name = "MCIsNonABTaskScheduledNotificationSent")
    protected int mcIsNonABTaskScheduledNotificationSent;
    @XmlElement(name = "MCNewTaskAssigned")
    protected int mcNewTaskAssigned;
    @XmlElement(name = "MCWifiCertService", required = true)
    protected String mcWifiCertService;
    @XmlElement(name = "MCShowCertificateWifiInitiate")
    protected int mcShowCertificateWifiInitiate;
    @XmlElement(name = "MCCertificateResultWifi", required = true)
    protected String mcCertificateResultWifi;
    @XmlElement(name = "MCCertificationActionType", required = true)
    protected String mcCertificationActionType;
    @XmlElement(name = "MCGenerateCertificateWifi")
    protected int mcGenerateCertificateWifi;
    @XmlElement(name = "MCCertificateWifiRequired")
    protected int mcCertificateWifiRequired;
    @XmlElement(name = "MCTAPHighFrecuency", required = true)
    protected String mctapHighFrecuency;
    @XmlElement(name = "MCTAPLowFrecuency", required = true)
    protected String mctapLowFrecuency;
    @XmlElement(name = "MCSplitterHighFrecuency", required = true)
    protected String mcSplitterHighFrecuency;
    @XmlElement(name = "MCSplitterLowFrecuency", required = true)
    protected String mcSplitterLowFrecuency;
    @XmlElement(name = "MCCMHighFrecuency", required = true)
    protected String mccmHighFrecuency;
    @XmlElement(name = "MCCMLowFrecuency", required = true)
    protected String mccmLowFrecuency;
    @XmlElement(name = "MCResendClosureOM")
    protected int mcResendClosureOM;

    /**
     * Gets the value of the key property.
     * 
     */
    public int getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     */
    public void setKey(int value) {
        this.key = value;
    }

    /**
     * Gets the value of the revision property.
     * 
     */
    public int getRevision() {
        return revision;
    }

    /**
     * Sets the value of the revision property.
     * 
     */
    public void setRevision(int value) {
        this.revision = value;
    }

    /**
     * Gets the value of the stamp property.
     * 
     * @return
     *     possible object is
     *     {@link Stamp }
     *     
     */
    public Stamp getStamp() {
        return stamp;
    }

    /**
     * Sets the value of the stamp property.
     * 
     * @param value
     *     allowed object is
     *     {@link Stamp }
     *     
     */
    public void setStamp(Stamp value) {
        this.stamp = value;
    }

    /**
     * Gets the value of the callID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCallID() {
        return callID;
    }

    /**
     * Sets the value of the callID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCallID(String value) {
        this.callID = value;
    }

    /**
     * Gets the value of the number property.
     * 
     */
    public int getNumber() {
        return number;
    }

    /**
     * Sets the value of the number property.
     * 
     */
    public void setNumber(int value) {
        this.number = value;
    }

    /**
     * Gets the value of the earlyStart property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getEarlyStart() {
        return earlyStart;
    }

    /**
     * Sets the value of the earlyStart property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setEarlyStart(XMLGregorianCalendar value) {
        this.earlyStart = value;
    }

    /**
     * Gets the value of the dueDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDueDate() {
        return dueDate;
    }

    /**
     * Sets the value of the dueDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDueDate(XMLGregorianCalendar value) {
        this.dueDate = value;
    }

    /**
     * Gets the value of the lateStart property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLateStart() {
        return lateStart;
    }

    /**
     * Sets the value of the lateStart property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLateStart(XMLGregorianCalendar value) {
        this.lateStart = value;
    }

    /**
     * Gets the value of the priority property.
     * 
     */
    public int getPriority() {
        return priority;
    }

    /**
     * Sets the value of the priority property.
     * 
     */
    public void setPriority(int value) {
        this.priority = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the customer property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomer() {
        return customer;
    }

    /**
     * Sets the value of the customer property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomer(String value) {
        this.customer = value;
    }

    /**
     * Gets the value of the calendar property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCalendar() {
        return calendar;
    }

    /**
     * Sets the value of the calendar property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCalendar(String value) {
        this.calendar = value;
    }

    /**
     * Gets the value of the region property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRegion() {
        return region;
    }

    /**
     * Sets the value of the region property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRegion(String value) {
        this.region = value;
    }

    /**
     * Gets the value of the district property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDistrict() {
        return district;
    }

    /**
     * Sets the value of the district property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDistrict(String value) {
        this.district = value;
    }

    /**
     * Gets the value of the postcode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPostcode() {
        return postcode;
    }

    /**
     * Sets the value of the postcode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPostcode(String value) {
        this.postcode = value;
    }

    /**
     * Gets the value of the preferredEngineers property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPreferredEngineers() {
        return preferredEngineers;
    }

    /**
     * Sets the value of the preferredEngineers property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPreferredEngineers(String value) {
        this.preferredEngineers = value;
    }

    /**
     * Gets the value of the contractType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContractType() {
        return contractType;
    }

    /**
     * Sets the value of the contractType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContractType(String value) {
        this.contractType = value;
    }

    /**
     * Gets the value of the openDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getOpenDate() {
        return openDate;
    }

    /**
     * Sets the value of the openDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setOpenDate(XMLGregorianCalendar value) {
        this.openDate = value;
    }

    /**
     * Gets the value of the contactDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getContactDate() {
        return contactDate;
    }

    /**
     * Sets the value of the contactDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setContactDate(XMLGregorianCalendar value) {
        this.contactDate = value;
    }

    /**
     * Gets the value of the confirmationDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getConfirmationDate() {
        return confirmationDate;
    }

    /**
     * Sets the value of the confirmationDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setConfirmationDate(XMLGregorianCalendar value) {
        this.confirmationDate = value;
    }

    /**
     * Gets the value of the taskType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskType() {
        return taskType;
    }

    /**
     * Sets the value of the taskType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskType(String value) {
        this.taskType = value;
    }

    /**
     * Gets the value of the duration property.
     * 
     */
    public int getDuration() {
        return duration;
    }

    /**
     * Sets the value of the duration property.
     * 
     */
    public void setDuration(int value) {
        this.duration = value;
    }

    /**
     * Gets the value of the requiredEngineers property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequiredEngineers() {
        return requiredEngineers;
    }

    /**
     * Sets the value of the requiredEngineers property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequiredEngineers(String value) {
        this.requiredEngineers = value;
    }

    /**
     * Gets the value of the numberOfRequiredEngineers property.
     * 
     */
    public int getNumberOfRequiredEngineers() {
        return numberOfRequiredEngineers;
    }

    /**
     * Sets the value of the numberOfRequiredEngineers property.
     * 
     */
    public void setNumberOfRequiredEngineers(int value) {
        this.numberOfRequiredEngineers = value;
    }

    /**
     * Gets the value of the requiredSkills1 property.
     * 
     * @return
     *     possible object is
     *     {@link TaskRequiredSkill1 }
     *     
     */
    public TaskRequiredSkill1 getRequiredSkills1() {
        return requiredSkills1;
    }

    /**
     * Sets the value of the requiredSkills1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link TaskRequiredSkill1 }
     *     
     */
    public void setRequiredSkills1(TaskRequiredSkill1 value) {
        this.requiredSkills1 = value;
    }

    /**
     * Gets the value of the requiredSkills2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequiredSkills2() {
        return requiredSkills2;
    }

    /**
     * Sets the value of the requiredSkills2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequiredSkills2(String value) {
        this.requiredSkills2 = value;
    }

    /**
     * Gets the value of the engineerType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEngineerType() {
        return engineerType;
    }

    /**
     * Sets the value of the engineerType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEngineerType(String value) {
        this.engineerType = value;
    }

    /**
     * Gets the value of the requiredEngineerTools property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequiredEngineerTools() {
        return requiredEngineerTools;
    }

    /**
     * Sets the value of the requiredEngineerTools property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequiredEngineerTools(String value) {
        this.requiredEngineerTools = value;
    }

    /**
     * Gets the value of the critical property.
     * 
     */
    public int getCritical() {
        return critical;
    }

    /**
     * Sets the value of the critical property.
     * 
     */
    public void setCritical(int value) {
        this.critical = value;
    }

    /**
     * Gets the value of the timeDependencies property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTimeDependencies() {
        return timeDependencies;
    }

    /**
     * Sets the value of the timeDependencies property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTimeDependencies(String value) {
        this.timeDependencies = value;
    }

    /**
     * Gets the value of the engineerDependencies property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEngineerDependencies() {
        return engineerDependencies;
    }

    /**
     * Sets the value of the engineerDependencies property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEngineerDependencies(String value) {
        this.engineerDependencies = value;
    }

    /**
     * Gets the value of the appointmentStart property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAppointmentStart() {
        return appointmentStart;
    }

    /**
     * Sets the value of the appointmentStart property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAppointmentStart(XMLGregorianCalendar value) {
        this.appointmentStart = value;
    }

    /**
     * Gets the value of the appointmentFinish property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAppointmentFinish() {
        return appointmentFinish;
    }

    /**
     * Sets the value of the appointmentFinish property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAppointmentFinish(XMLGregorianCalendar value) {
        this.appointmentFinish = value;
    }

    /**
     * Gets the value of the contactName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactName() {
        return contactName;
    }

    /**
     * Sets the value of the contactName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactName(String value) {
        this.contactName = value;
    }

    /**
     * Gets the value of the contactPhoneNumber property.
     * 
     */
    public int getContactPhoneNumber() {
        return contactPhoneNumber;
    }

    /**
     * Sets the value of the contactPhoneNumber property.
     * 
     */
    public void setContactPhoneNumber(int value) {
        this.contactPhoneNumber = value;
    }

    /**
     * Gets the value of the binaryData property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBinaryData() {
        return binaryData;
    }

    /**
     * Sets the value of the binaryData property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBinaryData(String value) {
        this.binaryData = value;
    }

    /**
     * Gets the value of the latitude property.
     * 
     */
    public int getLatitude() {
        return latitude;
    }

    /**
     * Sets the value of the latitude property.
     * 
     */
    public void setLatitude(int value) {
        this.latitude = value;
    }

    /**
     * Gets the value of the longitude property.
     * 
     */
    public int getLongitude() {
        return longitude;
    }

    /**
     * Sets the value of the longitude property.
     * 
     */
    public void setLongitude(int value) {
        this.longitude = value;
    }

    /**
     * Gets the value of the gisDataSource property.
     * 
     */
    public int getGISDataSource() {
        return gisDataSource;
    }

    /**
     * Sets the value of the gisDataSource property.
     * 
     */
    public void setGISDataSource(int value) {
        this.gisDataSource = value;
    }

    /**
     * Gets the value of the street property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStreet() {
        return street;
    }

    /**
     * Sets the value of the street property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStreet(String value) {
        this.street = value;
    }

    /**
     * Gets the value of the city property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the city property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * Gets the value of the mcState property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCState() {
        return mcState;
    }

    /**
     * Sets the value of the mcState property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCState(String value) {
        this.mcState = value;
    }

    /**
     * Gets the value of the state property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getState() {
        return state;
    }

    /**
     * Sets the value of the state property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setState(String value) {
        this.state = value;
    }

    /**
     * Gets the value of the taskStatusContext property.
     * 
     */
    public int getTaskStatusContext() {
        return taskStatusContext;
    }

    /**
     * Sets the value of the taskStatusContext property.
     * 
     */
    public void setTaskStatusContext(int value) {
        this.taskStatusContext = value;
    }

    /**
     * Gets the value of the isCrewTask property.
     * 
     */
    public int getIsCrewTask() {
        return isCrewTask;
    }

    /**
     * Sets the value of the isCrewTask property.
     * 
     */
    public void setIsCrewTask(int value) {
        this.isCrewTask = value;
    }

    /**
     * Gets the value of the countryID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCountryID() {
        return countryID;
    }

    /**
     * Sets the value of the countryID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCountryID(String value) {
        this.countryID = value;
    }

    /**
     * Gets the value of the engineerRequirements property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEngineerRequirements() {
        return engineerRequirements;
    }

    /**
     * Sets the value of the engineerRequirements property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEngineerRequirements(String value) {
        this.engineerRequirements = value;
    }

    /**
     * Gets the value of the isScheduled property.
     * 
     */
    public int getIsScheduled() {
        return isScheduled;
    }

    /**
     * Sets the value of the isScheduled property.
     * 
     */
    public void setIsScheduled(int value) {
        this.isScheduled = value;
    }

    /**
     * Gets the value of the customerEmail property.
     * 
     */
    public int getCustomerEmail() {
        return customerEmail;
    }

    /**
     * Sets the value of the customerEmail property.
     * 
     */
    public void setCustomerEmail(int value) {
        this.customerEmail = value;
    }

    /**
     * Gets the value of the excludedEngineers property.
     * 
     */
    public int getExcludedEngineers() {
        return excludedEngineers;
    }

    /**
     * Sets the value of the excludedEngineers property.
     * 
     */
    public void setExcludedEngineers(int value) {
        this.excludedEngineers = value;
    }

    /**
     * Gets the value of the requiredCrewSize property.
     * 
     */
    public int getRequiredCrewSize() {
        return requiredCrewSize;
    }

    /**
     * Sets the value of the requiredCrewSize property.
     * 
     */
    public void setRequiredCrewSize(int value) {
        this.requiredCrewSize = value;
    }

    /**
     * Gets the value of the inJeopardy property.
     * 
     */
    public int getInJeopardy() {
        return inJeopardy;
    }

    /**
     * Sets the value of the inJeopardy property.
     * 
     */
    public void setInJeopardy(int value) {
        this.inJeopardy = value;
    }

    /**
     * Gets the value of the pinned property.
     * 
     */
    public int getPinned() {
        return pinned;
    }

    /**
     * Sets the value of the pinned property.
     * 
     */
    public void setPinned(int value) {
        this.pinned = value;
    }

    /**
     * Gets the value of the jeopardyState property.
     * 
     */
    public int getJeopardyState() {
        return jeopardyState;
    }

    /**
     * Sets the value of the jeopardyState property.
     * 
     */
    public void setJeopardyState(int value) {
        this.jeopardyState = value;
    }

    /**
     * Gets the value of the displayStatus property.
     * 
     */
    public int getDisplayStatus() {
        return displayStatus;
    }

    /**
     * Sets the value of the displayStatus property.
     * 
     */
    public void setDisplayStatus(int value) {
        this.displayStatus = value;
    }

    /**
     * Gets the value of the dispatchDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDispatchDate() {
        return dispatchDate;
    }

    /**
     * Sets the value of the dispatchDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDispatchDate(XMLGregorianCalendar value) {
        this.dispatchDate = value;
    }

    /**
     * Gets the value of the scheduleDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getScheduleDate() {
        return scheduleDate;
    }

    /**
     * Sets the value of the scheduleDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setScheduleDate(XMLGregorianCalendar value) {
        this.scheduleDate = value;
    }

    /**
     * Gets the value of the displayDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDisplayDate() {
        return displayDate;
    }

    /**
     * Sets the value of the displayDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDisplayDate(XMLGregorianCalendar value) {
        this.displayDate = value;
    }

    /**
     * Gets the value of the onSiteDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getOnSiteDate() {
        return onSiteDate;
    }

    /**
     * Sets the value of the onSiteDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setOnSiteDate(XMLGregorianCalendar value) {
        this.onSiteDate = value;
    }

    /**
     * Gets the value of the comment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the customerReference property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerReference() {
        return customerReference;
    }

    /**
     * Sets the value of the customerReference property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerReference(String value) {
        this.customerReference = value;
    }

    /**
     * Gets the value of the stateSubdivision property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStateSubdivision() {
        return stateSubdivision;
    }

    /**
     * Sets the value of the stateSubdivision property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStateSubdivision(String value) {
        this.stateSubdivision = value;
    }

    /**
     * Gets the value of the citySubdivision property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCitySubdivision() {
        return citySubdivision;
    }

    /**
     * Sets the value of the citySubdivision property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCitySubdivision(String value) {
        this.citySubdivision = value;
    }

    /**
     * Gets the value of the team property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTeam() {
        return team;
    }

    /**
     * Sets the value of the team property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTeam(String value) {
        this.team = value;
    }

    /**
     * Gets the value of the signature property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSignature() {
        return signature;
    }

    /**
     * Sets the value of the signature property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSignature(String value) {
        this.signature = value;
    }

    /**
     * Gets the value of the externalRefID property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExternalRefID() {
        return externalRefID;
    }

    /**
     * Sets the value of the externalRefID property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExternalRefID(String value) {
        this.externalRefID = value;
    }

    /**
     * Gets the value of the partsUsed property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPartsUsed() {
        return partsUsed;
    }

    /**
     * Sets the value of the partsUsed property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPartsUsed(String value) {
        this.partsUsed = value;
    }

    /**
     * Gets the value of the assets property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAssets() {
        return assets;
    }

    /**
     * Sets the value of the assets property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAssets(String value) {
        this.assets = value;
    }

    /**
     * Gets the value of the backReportings property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBackReportings() {
        return backReportings;
    }

    /**
     * Sets the value of the backReportings property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBackReportings(String value) {
        this.backReportings = value;
    }

    /**
     * Gets the value of the taskTypeCategory property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskTypeCategory() {
        return taskTypeCategory;
    }

    /**
     * Sets the value of the taskTypeCategory property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskTypeCategory(String value) {
        this.taskTypeCategory = value;
    }

    /**
     * Gets the value of the workOrderItem property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkOrderItem() {
        return workOrderItem;
    }

    /**
     * Sets the value of the workOrderItem property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkOrderItem(String value) {
        this.workOrderItem = value;
    }

    /**
     * Gets the value of the userCustomerAccount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserCustomerAccount() {
        return userCustomerAccount;
    }

    /**
     * Sets the value of the userCustomerAccount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserCustomerAccount(String value) {
        this.userCustomerAccount = value;
    }

    /**
     * Gets the value of the isAppointment property.
     * 
     */
    public int getIsAppointment() {
        return isAppointment;
    }

    /**
     * Sets the value of the isAppointment property.
     * 
     */
    public void setIsAppointment(int value) {
        this.isAppointment = value;
    }

    /**
     * Gets the value of the travelDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTravelDate() {
        return travelDate;
    }

    /**
     * Sets the value of the travelDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTravelDate(XMLGregorianCalendar value) {
        this.travelDate = value;
    }

    /**
     * Gets the value of the completionDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getCompletionDate() {
        return completionDate;
    }

    /**
     * Sets the value of the completionDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setCompletionDate(XMLGregorianCalendar value) {
        this.completionDate = value;
    }

    /**
     * Gets the value of the attachments property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttachments() {
        return attachments;
    }

    /**
     * Sets the value of the attachments property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttachments(String value) {
        this.attachments = value;
    }

    /**
     * Gets the value of the cancellationDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getCancellationDate() {
        return cancellationDate;
    }

    /**
     * Sets the value of the cancellationDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setCancellationDate(XMLGregorianCalendar value) {
        this.cancellationDate = value;
    }

    /**
     * Gets the value of the isMegatask property.
     * 
     */
    public int getIsMegatask() {
        return isMegatask;
    }

    /**
     * Sets the value of the isMegatask property.
     * 
     */
    public void setIsMegatask(int value) {
        this.isMegatask = value;
    }

    /**
     * Gets the value of the isBundled property.
     * 
     */
    public int getIsBundled() {
        return isBundled;
    }

    /**
     * Sets the value of the isBundled property.
     * 
     */
    public void setIsBundled(int value) {
        this.isBundled = value;
    }

    /**
     * Gets the value of the isManuallyBundled property.
     * 
     */
    public int getIsManuallyBundled() {
        return isManuallyBundled;
    }

    /**
     * Sets the value of the isManuallyBundled property.
     * 
     */
    public void setIsManuallyBundled(int value) {
        this.isManuallyBundled = value;
    }

    /**
     * Gets the value of the megatask property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMegatask() {
        return megatask;
    }

    /**
     * Sets the value of the megatask property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMegatask(String value) {
        this.megatask = value;
    }

    /**
     * Gets the value of the megataskPureDuration property.
     * 
     */
    public int getMegataskPureDuration() {
        return megataskPureDuration;
    }

    /**
     * Sets the value of the megataskPureDuration property.
     * 
     */
    public void setMegataskPureDuration(int value) {
        this.megataskPureDuration = value;
    }

    /**
     * Gets the value of the bundlerConfiguration property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBundlerConfiguration() {
        return bundlerConfiguration;
    }

    /**
     * Sets the value of the bundlerConfiguration property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBundlerConfiguration(String value) {
        this.bundlerConfiguration = value;
    }

    /**
     * Gets the value of the subtasks property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSubtasks() {
        return subtasks;
    }

    /**
     * Sets the value of the subtasks property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSubtasks(String value) {
        this.subtasks = value;
    }

    /**
     * Gets the value of the skillsDuration property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkillsDuration() {
        return skillsDuration;
    }

    /**
     * Sets the value of the skillsDuration property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkillsDuration(String value) {
        this.skillsDuration = value;
    }

    /**
     * Gets the value of the customerAccount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerAccount() {
        return customerAccount;
    }

    /**
     * Sets the value of the customerAccount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerAccount(String value) {
        this.customerAccount = value;
    }

    /**
     * Gets the value of the area property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArea() {
        return area;
    }

    /**
     * Sets the value of the area property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArea(String value) {
        this.area = value;
    }

    /**
     * Gets the value of the incompleteReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIncompleteReason() {
        return incompleteReason;
    }

    /**
     * Sets the value of the incompleteReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIncompleteReason(String value) {
        this.incompleteReason = value;
    }

    /**
     * Gets the value of the mcWorkPackageDescription property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCWorkPackageDescription() {
        return mcWorkPackageDescription;
    }

    /**
     * Sets the value of the mcWorkPackageDescription property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCWorkPackageDescription(String value) {
        this.mcWorkPackageDescription = value;
    }

    /**
     * Gets the value of the mcComment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCComment() {
        return mcComment;
    }

    /**
     * Sets the value of the mcComment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCComment(String value) {
        this.mcComment = value;
    }

    /**
     * Gets the value of the mccrmComment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCRMComment() {
        return mccrmComment;
    }

    /**
     * Sets the value of the mccrmComment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCRMComment(String value) {
        this.mccrmComment = value;
    }

    /**
     * Gets the value of the mcContactEmail property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCContactEmail() {
        return mcContactEmail;
    }

    /**
     * Sets the value of the mcContactEmail property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCContactEmail(String value) {
        this.mcContactEmail = value;
    }

    /**
     * Gets the value of the mcCustomerCode property.
     * 
     */
    public int getMCCustomerCode() {
        return mcCustomerCode;
    }

    /**
     * Sets the value of the mcCustomerCode property.
     * 
     */
    public void setMCCustomerCode(int value) {
        this.mcCustomerCode = value;
    }

    /**
     * Gets the value of the mcCustomerPhoneNumber property.
     * 
     */
    public int getMCCustomerPhoneNumber() {
        return mcCustomerPhoneNumber;
    }

    /**
     * Sets the value of the mcCustomerPhoneNumber property.
     * 
     */
    public void setMCCustomerPhoneNumber(int value) {
        this.mcCustomerPhoneNumber = value;
    }

    /**
     * Gets the value of the mcSaldoPending property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCSaldoPending() {
        return mcSaldoPending;
    }

    /**
     * Sets the value of the mcSaldoPending property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCSaldoPending(String value) {
        this.mcSaldoPending = value;
    }

    /**
     * Gets the value of the mcBillingAccountInfo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCBillingAccountInfo() {
        return mcBillingAccountInfo;
    }

    /**
     * Sets the value of the mcBillingAccountInfo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCBillingAccountInfo(String value) {
        this.mcBillingAccountInfo = value;
    }

    /**
     * Gets the value of the mcConnectionData property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCConnectionData() {
        return mcConnectionData;
    }

    /**
     * Sets the value of the mcConnectionData property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCConnectionData(String value) {
        this.mcConnectionData = value;
    }

    /**
     * Gets the value of the lastRejectedEngineer property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLastRejectedEngineer() {
        return lastRejectedEngineer;
    }

    /**
     * Sets the value of the lastRejectedEngineer property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastRejectedEngineer(String value) {
        this.lastRejectedEngineer = value;
    }

    /**
     * Gets the value of the rejectedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getRejectedDate() {
        return rejectedDate;
    }

    /**
     * Sets the value of the rejectedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setRejectedDate(XMLGregorianCalendar value) {
        this.rejectedDate = value;
    }

    /**
     * Gets the value of the rejectionReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRejectionReason() {
        return rejectionReason;
    }

    /**
     * Sets the value of the rejectionReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRejectionReason(String value) {
        this.rejectionReason = value;
    }

    /**
     * Gets the value of the cancellationReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCancellationReason() {
        return cancellationReason;
    }

    /**
     * Sets the value of the cancellationReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCancellationReason(String value) {
        this.cancellationReason = value;
    }

    /**
     * Gets the value of the mcConfirmationStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCConfirmationStatus() {
        return mcConfirmationStatus;
    }

    /**
     * Sets the value of the mcConfirmationStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCConfirmationStatus(String value) {
        this.mcConfirmationStatus = value;
    }

    /**
     * Gets the value of the mcmvPartsRequired property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCMVPartsRequired() {
        return mcmvPartsRequired;
    }

    /**
     * Sets the value of the mcmvPartsRequired property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCMVPartsRequired(String value) {
        this.mcmvPartsRequired = value;
    }

    /**
     * Gets the value of the mcmvMaterialUsed property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCMVMaterialUsed() {
        return mcmvMaterialUsed;
    }

    /**
     * Sets the value of the mcmvMaterialUsed property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCMVMaterialUsed(String value) {
        this.mcmvMaterialUsed = value;
    }

    /**
     * Gets the value of the mcmvEquipmentUsed property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCMVEquipmentUsed() {
        return mcmvEquipmentUsed;
    }

    /**
     * Sets the value of the mcmvEquipmentUsed property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCMVEquipmentUsed(String value) {
        this.mcmvEquipmentUsed = value;
    }

    /**
     * Gets the value of the mcmvEquipmentCollected property.
     * 
     */
    public int getMCMVEquipmentCollected() {
        return mcmvEquipmentCollected;
    }

    /**
     * Sets the value of the mcmvEquipmentCollected property.
     * 
     */
    public void setMCMVEquipmentCollected(int value) {
        this.mcmvEquipmentCollected = value;
    }

    /**
     * Gets the value of the mcNoMaterialUsed property.
     * 
     */
    public int getMCNoMaterialUsed() {
        return mcNoMaterialUsed;
    }

    /**
     * Sets the value of the mcNoMaterialUsed property.
     * 
     */
    public void setMCNoMaterialUsed(int value) {
        this.mcNoMaterialUsed = value;
    }

    /**
     * Gets the value of the mcNoEquipmentUsed property.
     * 
     */
    public int getMCNoEquipmentUsed() {
        return mcNoEquipmentUsed;
    }

    /**
     * Sets the value of the mcNoEquipmentUsed property.
     * 
     */
    public void setMCNoEquipmentUsed(int value) {
        this.mcNoEquipmentUsed = value;
    }

    /**
     * Gets the value of the mcCauseReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCauseReason() {
        return mcCauseReason;
    }

    /**
     * Sets the value of the mcCauseReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCauseReason(String value) {
        this.mcCauseReason = value;
    }

    /**
     * Gets the value of the mcLaboresUsed property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCLaboresUsed() {
        return mcLaboresUsed;
    }

    /**
     * Sets the value of the mcLaboresUsed property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCLaboresUsed(String value) {
        this.mcLaboresUsed = value;
    }

    /**
     * Gets the value of the mcCustomsSeal property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomsSeal() {
        return mcCustomsSeal;
    }

    /**
     * Sets the value of the mcCustomsSeal property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomsSeal(String value) {
        this.mcCustomsSeal = value;
    }

    /**
     * Gets the value of the mcCodeBobina1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCodeBobina1() {
        return mcCodeBobina1;
    }

    /**
     * Sets the value of the mcCodeBobina1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCodeBobina1(String value) {
        this.mcCodeBobina1 = value;
    }

    /**
     * Gets the value of the mcCodeBobina2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCodeBobina2() {
        return mcCodeBobina2;
    }

    /**
     * Sets the value of the mcCodeBobina2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCodeBobina2(String value) {
        this.mcCodeBobina2 = value;
    }

    /**
     * Gets the value of the mcmvServices property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCMVServices() {
        return mcmvServices;
    }

    /**
     * Sets the value of the mcmvServices property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCMVServices(String value) {
        this.mcmvServices = value;
    }

    /**
     * Gets the value of the dynamicPriority property.
     * 
     */
    public int getDynamicPriority() {
        return dynamicPriority;
    }

    /**
     * Sets the value of the dynamicPriority property.
     * 
     */
    public void setDynamicPriority(int value) {
        this.dynamicPriority = value;
    }

    /**
     * Gets the value of the externalRefIDExtension property.
     * 
     */
    public int getExternalRefIDExtension() {
        return externalRefIDExtension;
    }

    /**
     * Sets the value of the externalRefIDExtension property.
     * 
     */
    public void setExternalRefIDExtension(int value) {
        this.externalRefIDExtension = value;
    }

    /**
     * Gets the value of the serviceSummary property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServiceSummary() {
        return serviceSummary;
    }

    /**
     * Sets the value of the serviceSummary property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServiceSummary(String value) {
        this.serviceSummary = value;
    }

    /**
     * Gets the value of the streetSmartJob property.
     * 
     */
    public int getStreetSmartJob() {
        return streetSmartJob;
    }

    /**
     * Sets the value of the streetSmartJob property.
     * 
     */
    public void setStreetSmartJob(int value) {
        this.streetSmartJob = value;
    }

    /**
     * Gets the value of the recurrenceTask property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRecurrenceTask() {
        return recurrenceTask;
    }

    /**
     * Sets the value of the recurrenceTask property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRecurrenceTask(String value) {
        this.recurrenceTask = value;
    }

    /**
     * Gets the value of the rtIsRecurringTask property.
     * 
     */
    public int getRTIsRecurringTask() {
        return rtIsRecurringTask;
    }

    /**
     * Sets the value of the rtIsRecurringTask property.
     * 
     */
    public void setRTIsRecurringTask(int value) {
        this.rtIsRecurringTask = value;
    }

    /**
     * Gets the value of the rtUpdateAllRecurrences property.
     * 
     */
    public int getRTUpdateAllRecurrences() {
        return rtUpdateAllRecurrences;
    }

    /**
     * Sets the value of the rtUpdateAllRecurrences property.
     * 
     */
    public void setRTUpdateAllRecurrences(int value) {
        this.rtUpdateAllRecurrences = value;
    }

    /**
     * Gets the value of the rtIsPrime property.
     * 
     */
    public int getRTIsPrime() {
        return rtIsPrime;
    }

    /**
     * Sets the value of the rtIsPrime property.
     * 
     */
    public void setRTIsPrime(int value) {
        this.rtIsPrime = value;
    }

    /**
     * Gets the value of the archiveStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArchiveStatus() {
        return archiveStatus;
    }

    /**
     * Sets the value of the archiveStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArchiveStatus(String value) {
        this.archiveStatus = value;
    }

    /**
     * Gets the value of the assignedEngineerName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAssignedEngineerName() {
        return assignedEngineerName;
    }

    /**
     * Sets the value of the assignedEngineerName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAssignedEngineerName(String value) {
        this.assignedEngineerName = value;
    }

    /**
     * Gets the value of the mobileKey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMobileKey() {
        return mobileKey;
    }

    /**
     * Sets the value of the mobileKey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMobileKey(String value) {
        this.mobileKey = value;
    }

    /**
     * Gets the value of the unit property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnit() {
        return unit;
    }

    /**
     * Sets the value of the unit property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnit(String value) {
        this.unit = value;
    }

    /**
     * Gets the value of the supervisorStatusGroup property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSupervisorStatusGroup() {
        return supervisorStatusGroup;
    }

    /**
     * Sets the value of the supervisorStatusGroup property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSupervisorStatusGroup(String value) {
        this.supervisorStatusGroup = value;
    }

    /**
     * Gets the value of the isSingleTask property.
     * 
     */
    public int getIsSingleTask() {
        return isSingleTask;
    }

    /**
     * Sets the value of the isSingleTask property.
     * 
     */
    public void setIsSingleTask(int value) {
        this.isSingleTask = value;
    }

    /**
     * Gets the value of the fieldCommentEng property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFieldCommentEng() {
        return fieldCommentEng;
    }

    /**
     * Sets the value of the fieldCommentEng property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFieldCommentEng(String value) {
        this.fieldCommentEng = value;
    }

    /**
     * Gets the value of the mcCustomerClass property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomerClass() {
        return mcCustomerClass;
    }

    /**
     * Sets the value of the mcCustomerClass property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomerClass(String value) {
        this.mcCustomerClass = value;
    }

    /**
     * Gets the value of the mcOpeningReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCOpeningReason() {
        return mcOpeningReason;
    }

    /**
     * Sets the value of the mcOpeningReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCOpeningReason(String value) {
        this.mcOpeningReason = value;
    }

    /**
     * Gets the value of the mcZonaRamal property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCZonaRamal() {
        return mcZonaRamal;
    }

    /**
     * Sets the value of the mcZonaRamal property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCZonaRamal(String value) {
        this.mcZonaRamal = value;
    }

    /**
     * Gets the value of the mcTap property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCTap() {
        return mcTap;
    }

    /**
     * Sets the value of the mcTap property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCTap(String value) {
        this.mcTap = value;
    }

    /**
     * Gets the value of the mcBoca property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCBoca() {
        return mcBoca;
    }

    /**
     * Sets the value of the mcBoca property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCBoca(String value) {
        this.mcBoca = value;
    }

    /**
     * Gets the value of the mcInfoCustomerSite property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCInfoCustomerSite() {
        return mcInfoCustomerSite;
    }

    /**
     * Sets the value of the mcInfoCustomerSite property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCInfoCustomerSite(String value) {
        this.mcInfoCustomerSite = value;
    }

    /**
     * Gets the value of the mcTaskClosureLongitude property.
     * 
     */
    public int getMCTaskClosureLongitude() {
        return mcTaskClosureLongitude;
    }

    /**
     * Sets the value of the mcTaskClosureLongitude property.
     * 
     */
    public void setMCTaskClosureLongitude(int value) {
        this.mcTaskClosureLongitude = value;
    }

    /**
     * Gets the value of the mcTaskClosureLatitude property.
     * 
     */
    public int getMCTaskClosureLatitude() {
        return mcTaskClosureLatitude;
    }

    /**
     * Sets the value of the mcTaskClosureLatitude property.
     * 
     */
    public void setMCTaskClosureLatitude(int value) {
        this.mcTaskClosureLatitude = value;
    }

    /**
     * Gets the value of the mcTaskClosureGeoToken property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCTaskClosureGeoToken() {
        return mcTaskClosureGeoToken;
    }

    /**
     * Sets the value of the mcTaskClosureGeoToken property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCTaskClosureGeoToken(String value) {
        this.mcTaskClosureGeoToken = value;
    }

    /**
     * Gets the value of the mcTaskClosureGeoTokenEntered property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCTaskClosureGeoTokenEntered() {
        return mcTaskClosureGeoTokenEntered;
    }

    /**
     * Sets the value of the mcTaskClosureGeoTokenEntered property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCTaskClosureGeoTokenEntered(String value) {
        this.mcTaskClosureGeoTokenEntered = value;
    }

    /**
     * Gets the value of the urlAuditInfo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getURLAuditInfo() {
        return urlAuditInfo;
    }

    /**
     * Sets the value of the urlAuditInfo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setURLAuditInfo(String value) {
        this.urlAuditInfo = value;
    }

    /**
     * Gets the value of the urlSurvey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getURLSurvey() {
        return urlSurvey;
    }

    /**
     * Sets the value of the urlSurvey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setURLSurvey(String value) {
        this.urlSurvey = value;
    }

    /**
     * Gets the value of the mcBuildingType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCBuildingType() {
        return mcBuildingType;
    }

    /**
     * Sets the value of the mcBuildingType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCBuildingType(String value) {
        this.mcBuildingType = value;
    }

    /**
     * Gets the value of the mcServicesSignature property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCServicesSignature() {
        return mcServicesSignature;
    }

    /**
     * Sets the value of the mcServicesSignature property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCServicesSignature(String value) {
        this.mcServicesSignature = value;
    }

    /**
     * Gets the value of the mcPlaca property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCPlaca() {
        return mcPlaca;
    }

    /**
     * Sets the value of the mcPlaca property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCPlaca(String value) {
        this.mcPlaca = value;
    }

    /**
     * Gets the value of the mcCustomerIdentityNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomerIdentityNumber() {
        return mcCustomerIdentityNumber;
    }

    /**
     * Sets the value of the mcCustomerIdentityNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomerIdentityNumber(String value) {
        this.mcCustomerIdentityNumber = value;
    }

    /**
     * Gets the value of the isDESent property.
     * 
     */
    public int getIsDESent() {
        return isDESent;
    }

    /**
     * Sets the value of the isDESent property.
     * 
     */
    public void setIsDESent(int value) {
        this.isDESent = value;
    }

    /**
     * Gets the value of the returnVerification property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReturnVerification() {
        return returnVerification;
    }

    /**
     * Sets the value of the returnVerification property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReturnVerification(String value) {
        this.returnVerification = value;
    }

    /**
     * Gets the value of the isETASent property.
     * 
     */
    public int getIsETASent() {
        return isETASent;
    }

    /**
     * Sets the value of the isETASent property.
     * 
     */
    public void setIsETASent(int value) {
        this.isETASent = value;
    }

    /**
     * Gets the value of the isReminder24HourSent property.
     * 
     */
    public int getIsReminder24HourSent() {
        return isReminder24HourSent;
    }

    /**
     * Sets the value of the isReminder24HourSent property.
     * 
     */
    public void setIsReminder24HourSent(int value) {
        this.isReminder24HourSent = value;
    }

    /**
     * Gets the value of the isSurveySent property.
     * 
     */
    public int getIsSurveySent() {
        return isSurveySent;
    }

    /**
     * Sets the value of the isSurveySent property.
     * 
     */
    public void setIsSurveySent(int value) {
        this.isSurveySent = value;
    }

    /**
     * Gets the value of the scheduleLowerBound property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getScheduleLowerBound() {
        return scheduleLowerBound;
    }

    /**
     * Sets the value of the scheduleLowerBound property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setScheduleLowerBound(XMLGregorianCalendar value) {
        this.scheduleLowerBound = value;
    }

    /**
     * Gets the value of the scheduleUpperBound property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getScheduleUpperBound() {
        return scheduleUpperBound;
    }

    /**
     * Sets the value of the scheduleUpperBound property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setScheduleUpperBound(XMLGregorianCalendar value) {
        this.scheduleUpperBound = value;
    }

    /**
     * Gets the value of the surveyAnswer property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSurveyAnswer() {
        return surveyAnswer;
    }

    /**
     * Sets the value of the surveyAnswer property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSurveyAnswer(String value) {
        this.surveyAnswer = value;
    }

    /**
     * Gets the value of the surveyComment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSurveyComment() {
        return surveyComment;
    }

    /**
     * Sets the value of the surveyComment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSurveyComment(String value) {
        this.surveyComment = value;
    }

    /**
     * Gets the value of the serviceAccepted property.
     * 
     */
    public int getServiceAccepted() {
        return serviceAccepted;
    }

    /**
     * Sets the value of the serviceAccepted property.
     * 
     */
    public void setServiceAccepted(int value) {
        this.serviceAccepted = value;
    }

    /**
     * Gets the value of the mcGoogleLatitude property.
     * 
     */
    public float getMCGoogleLatitude() {
        return mcGoogleLatitude;
    }

    /**
     * Sets the value of the mcGoogleLatitude property.
     * 
     */
    public void setMCGoogleLatitude(float value) {
        this.mcGoogleLatitude = value;
    }

    /**
     * Gets the value of the mcGoogleLongitude property.
     * 
     */
    public float getMCGoogleLongitude() {
        return mcGoogleLongitude;
    }

    /**
     * Sets the value of the mcGoogleLongitude property.
     * 
     */
    public void setMCGoogleLongitude(float value) {
        this.mcGoogleLongitude = value;
    }

    /**
     * Gets the value of the mcCustomerAssets property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCustomerAssets() {
        return mcCustomerAssets;
    }

    /**
     * Sets the value of the mcCustomerAssets property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCustomerAssets(String value) {
        this.mcCustomerAssets = value;
    }

    /**
     * Gets the value of the mcptWorkingArea property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCPTWorkingArea() {
        return mcptWorkingArea;
    }

    /**
     * Sets the value of the mcptWorkingArea property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCPTWorkingArea(String value) {
        this.mcptWorkingArea = value;
    }

    /**
     * Gets the value of the mcptFilter property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCPTFilter() {
        return mcptFilter;
    }

    /**
     * Sets the value of the mcptFilter property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCPTFilter(String value) {
        this.mcptFilter = value;
    }

    /**
     * Gets the value of the mctpPolygonFilter property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCTPPolygonFilter() {
        return mctpPolygonFilter;
    }

    /**
     * Sets the value of the mctpPolygonFilter property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCTPPolygonFilter(String value) {
        this.mctpPolygonFilter = value;
    }

    /**
     * Gets the value of the workingArea property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkingArea() {
        return workingArea;
    }

    /**
     * Sets the value of the workingArea property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkingArea(String value) {
        this.workingArea = value;
    }

    /**
     * Gets the value of the mcDispatchOMSent property.
     * 
     */
    public int getMCDispatchOMSent() {
        return mcDispatchOMSent;
    }

    /**
     * Sets the value of the mcDispatchOMSent property.
     * 
     */
    public void setMCDispatchOMSent(int value) {
        this.mcDispatchOMSent = value;
    }

    /**
     * Gets the value of the mcdeComment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCDEComment() {
        return mcdeComment;
    }

    /**
     * Sets the value of the mcdeComment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCDEComment(String value) {
        this.mcdeComment = value;
    }

    /**
     * Gets the value of the mccemAttachments property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCEMAttachments() {
        return mccemAttachments;
    }

    /**
     * Sets the value of the mccemAttachments property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCEMAttachments(String value) {
        this.mccemAttachments = value;
    }

    /**
     * Gets the value of the mcContactPhoneNumbers property.
     * 
     */
    public int getMCContactPhoneNumbers() {
        return mcContactPhoneNumbers;
    }

    /**
     * Sets the value of the mcContactPhoneNumbers property.
     * 
     */
    public void setMCContactPhoneNumbers(int value) {
        this.mcContactPhoneNumbers = value;
    }

    /**
     * Gets the value of the taskAppointmentTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskAppointmentTime() {
        return taskAppointmentTime;
    }

    /**
     * Sets the value of the taskAppointmentTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskAppointmentTime(String value) {
        this.taskAppointmentTime = value;
    }

    /**
     * Gets the value of the taskAppointmentUpdated property.
     * 
     */
    public int getTaskAppointmentUpdated() {
        return taskAppointmentUpdated;
    }

    /**
     * Sets the value of the taskAppointmentUpdated property.
     * 
     */
    public void setTaskAppointmentUpdated(int value) {
        this.taskAppointmentUpdated = value;
    }

    /**
     * Gets the value of the mcServicePaid property.
     * 
     */
    public int getMCServicePaid() {
        return mcServicePaid;
    }

    /**
     * Sets the value of the mcServicePaid property.
     * 
     */
    public void setMCServicePaid(int value) {
        this.mcServicePaid = value;
    }

    /**
     * Gets the value of the mcUnscheduledForNotPaid property.
     * 
     */
    public int getMCUnscheduledForNotPaid() {
        return mcUnscheduledForNotPaid;
    }

    /**
     * Sets the value of the mcUnscheduledForNotPaid property.
     * 
     */
    public void setMCUnscheduledForNotPaid(int value) {
        this.mcUnscheduledForNotPaid = value;
    }

    /**
     * Gets the value of the mccemabCancellationNotification property.
     * 
     */
    public int getMCCEMABCancellationNotification() {
        return mccemabCancellationNotification;
    }

    /**
     * Sets the value of the mccemabCancellationNotification property.
     * 
     */
    public void setMCCEMABCancellationNotification(int value) {
        this.mccemabCancellationNotification = value;
    }

    /**
     * Gets the value of the mcUnscheduledForNotPaidCounter property.
     * 
     */
    public int getMCUnscheduledForNotPaidCounter() {
        return mcUnscheduledForNotPaidCounter;
    }

    /**
     * Sets the value of the mcUnscheduledForNotPaidCounter property.
     * 
     */
    public void setMCUnscheduledForNotPaidCounter(int value) {
        this.mcUnscheduledForNotPaidCounter = value;
    }

    /**
     * Gets the value of the mcTaskUnpaidReminderSent property.
     * 
     */
    public int getMCTaskUnpaidReminderSent() {
        return mcTaskUnpaidReminderSent;
    }

    /**
     * Sets the value of the mcTaskUnpaidReminderSent property.
     * 
     */
    public void setMCTaskUnpaidReminderSent(int value) {
        this.mcTaskUnpaidReminderSent = value;
    }

    /**
     * Gets the value of the mccemNoMsgOnDispatcherReschedule property.
     * 
     */
    public int getMCCEMNoMsgOnDispatcherReschedule() {
        return mccemNoMsgOnDispatcherReschedule;
    }

    /**
     * Sets the value of the mccemNoMsgOnDispatcherReschedule property.
     * 
     */
    public void setMCCEMNoMsgOnDispatcherReschedule(int value) {
        this.mccemNoMsgOnDispatcherReschedule = value;
    }

    /**
     * Gets the value of the mcIsNonABTaskScheduledNotificationSent property.
     * 
     */
    public int getMCIsNonABTaskScheduledNotificationSent() {
        return mcIsNonABTaskScheduledNotificationSent;
    }

    /**
     * Sets the value of the mcIsNonABTaskScheduledNotificationSent property.
     * 
     */
    public void setMCIsNonABTaskScheduledNotificationSent(int value) {
        this.mcIsNonABTaskScheduledNotificationSent = value;
    }

    /**
     * Gets the value of the mcNewTaskAssigned property.
     * 
     */
    public int getMCNewTaskAssigned() {
        return mcNewTaskAssigned;
    }

    /**
     * Sets the value of the mcNewTaskAssigned property.
     * 
     */
    public void setMCNewTaskAssigned(int value) {
        this.mcNewTaskAssigned = value;
    }

    /**
     * Gets the value of the mcWifiCertService property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCWifiCertService() {
        return mcWifiCertService;
    }

    /**
     * Sets the value of the mcWifiCertService property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCWifiCertService(String value) {
        this.mcWifiCertService = value;
    }

    /**
     * Gets the value of the mcShowCertificateWifiInitiate property.
     * 
     */
    public int getMCShowCertificateWifiInitiate() {
        return mcShowCertificateWifiInitiate;
    }

    /**
     * Sets the value of the mcShowCertificateWifiInitiate property.
     * 
     */
    public void setMCShowCertificateWifiInitiate(int value) {
        this.mcShowCertificateWifiInitiate = value;
    }

    /**
     * Gets the value of the mcCertificateResultWifi property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCertificateResultWifi() {
        return mcCertificateResultWifi;
    }

    /**
     * Sets the value of the mcCertificateResultWifi property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCertificateResultWifi(String value) {
        this.mcCertificateResultWifi = value;
    }

    /**
     * Gets the value of the mcCertificationActionType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCertificationActionType() {
        return mcCertificationActionType;
    }

    /**
     * Sets the value of the mcCertificationActionType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCertificationActionType(String value) {
        this.mcCertificationActionType = value;
    }

    /**
     * Gets the value of the mcGenerateCertificateWifi property.
     * 
     */
    public int getMCGenerateCertificateWifi() {
        return mcGenerateCertificateWifi;
    }

    /**
     * Sets the value of the mcGenerateCertificateWifi property.
     * 
     */
    public void setMCGenerateCertificateWifi(int value) {
        this.mcGenerateCertificateWifi = value;
    }

    /**
     * Gets the value of the mcCertificateWifiRequired property.
     * 
     */
    public int getMCCertificateWifiRequired() {
        return mcCertificateWifiRequired;
    }

    /**
     * Sets the value of the mcCertificateWifiRequired property.
     * 
     */
    public void setMCCertificateWifiRequired(int value) {
        this.mcCertificateWifiRequired = value;
    }

    /**
     * Gets the value of the mctapHighFrecuency property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCTAPHighFrecuency() {
        return mctapHighFrecuency;
    }

    /**
     * Sets the value of the mctapHighFrecuency property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCTAPHighFrecuency(String value) {
        this.mctapHighFrecuency = value;
    }

    /**
     * Gets the value of the mctapLowFrecuency property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCTAPLowFrecuency() {
        return mctapLowFrecuency;
    }

    /**
     * Sets the value of the mctapLowFrecuency property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCTAPLowFrecuency(String value) {
        this.mctapLowFrecuency = value;
    }

    /**
     * Gets the value of the mcSplitterHighFrecuency property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCSplitterHighFrecuency() {
        return mcSplitterHighFrecuency;
    }

    /**
     * Sets the value of the mcSplitterHighFrecuency property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCSplitterHighFrecuency(String value) {
        this.mcSplitterHighFrecuency = value;
    }

    /**
     * Gets the value of the mcSplitterLowFrecuency property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCSplitterLowFrecuency() {
        return mcSplitterLowFrecuency;
    }

    /**
     * Sets the value of the mcSplitterLowFrecuency property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCSplitterLowFrecuency(String value) {
        this.mcSplitterLowFrecuency = value;
    }

    /**
     * Gets the value of the mccmHighFrecuency property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCMHighFrecuency() {
        return mccmHighFrecuency;
    }

    /**
     * Sets the value of the mccmHighFrecuency property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCMHighFrecuency(String value) {
        this.mccmHighFrecuency = value;
    }

    /**
     * Gets the value of the mccmLowFrecuency property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCMLowFrecuency() {
        return mccmLowFrecuency;
    }

    /**
     * Sets the value of the mccmLowFrecuency property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCMLowFrecuency(String value) {
        this.mccmLowFrecuency = value;
    }

    /**
     * Gets the value of the mcResendClosureOM property.
     * 
     */
    public int getMCResendClosureOM() {
        return mcResendClosureOM;
    }

    /**
     * Sets the value of the mcResendClosureOM property.
     * 
     */
    public void setMCResendClosureOM(int value) {
        this.mcResendClosureOM = value;
    }

}
