package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.contact;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AcquisitionContactSecurityQuestionRequest {

    @ApiModelProperty(value = "The question code", example = "MEMORABLE_DATE", required = true)
    private String questionCode;

    @ApiModelProperty(value = "The response to the question", example = "the response", required = true)
    private String response;
}
