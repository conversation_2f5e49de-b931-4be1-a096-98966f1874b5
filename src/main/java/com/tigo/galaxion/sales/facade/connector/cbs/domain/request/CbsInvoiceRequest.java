package com.tigo.galaxion.sales.facade.connector.cbs.domain.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class CbsInvoiceRequest {
	@NotBlank
	private String traceId;
	private Integer retrieveDetail;
	@NotNull
	private Integer totalRowNum;
	@NotNull
	private Integer beginRowNum;
	@NotNull
	private Integer fetchRowNum;
	private QueryObj queryObj;
	private InvoiceHeaderFilter invoiceHeaderFilter;
}
