package com.tigo.galaxion.sales.facade.services;

import com.github.fge.jsonpatch.mergepatch.JsonMergePatch;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart.CartAddOnRequest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart.CartEquipmentRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.mapper.dto.TigoCartResponseMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CartService {

    private final AcquisitionProspectClient acquisitionProspectClient;
    private final CrossSellClient crossSellClient;

    private final TigoCartResponseMapper tigoCartResponseMapper;

    public TigoCartResponse getCartForProspect(String prospectReference) {
        var cartResponse = acquisitionProspectClient.getProspectCart(prospectReference);
        return tigoCartResponseMapper.buildTigoCartResponse(cartResponse);
    }

    public TigoCartResponse getCartForCrossSell(String crossSellReference) {
        var cartResponse = crossSellClient.getCrossSellCart(crossSellReference);
        return tigoCartResponseMapper.buildTigoCartResponse(cartResponse);
    }

    public TigoCartResponse updateOffer(String prospectReference, long offerId, JsonMergePatch jsonMergePatch){
        var cartResponse = acquisitionProspectClient.updateOfferCart(prospectReference, offerId, jsonMergePatch);
        return tigoCartResponseMapper.buildTigoCartResponse(cartResponse);
    }

    public TigoCartResponse addAddOnToCart(String prospectReference, Long offerId, CartAddOnRequest addOn){
        var cartResponse = acquisitionProspectClient.addAddOnToOffer(prospectReference, offerId, addOn);
        return tigoCartResponseMapper.buildTigoCartResponse(cartResponse);
    }

    public TigoCartResponse deleteAddOnFromCart(String prospectReference, Long offerId, Long addOnId){
        var cartResponse = acquisitionProspectClient.deleteAddOnFromOffer(prospectReference, offerId, addOnId);
        return tigoCartResponseMapper.buildTigoCartResponse(cartResponse);
    }

    public TigoCartResponse addEquipmentToCart(String prospectReference, Long offerId, CartEquipmentRequest equipment){
        var cartResponse = acquisitionProspectClient.addEquipmentToOffer(prospectReference, offerId, equipment);
        return tigoCartResponseMapper.buildTigoCartResponse(cartResponse);
    }

    public TigoCartResponse deleteEquipmentFromCart(String prospectReference, Long offerId, Long equipmentId){
        var cartResponse = acquisitionProspectClient.deleteEquipmentFromOffer(prospectReference, offerId, equipmentId);
        return tigoCartResponseMapper.buildTigoCartResponse(cartResponse);
    }
}
