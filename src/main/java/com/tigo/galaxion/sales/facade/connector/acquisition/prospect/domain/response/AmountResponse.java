package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AmountResponse {

    private Long oneOffAmount;

    private Long discountedOneOffAmount;

    private Long upFrontAmount;

    private Long discountedUpFrontAmount;
    
    private RecurringAmountResponse recurringAmount;
}
