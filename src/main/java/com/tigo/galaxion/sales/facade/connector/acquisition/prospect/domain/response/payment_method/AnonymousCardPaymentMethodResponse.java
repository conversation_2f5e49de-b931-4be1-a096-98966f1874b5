package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.payment_method;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.PaymentMethodTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import static com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.PaymentMethodTypeEnum.ANONYMOUS_CARD;


@ApiModel(description = "The payment card information.", parent = PaymentMethodResponse.class)
@JsonTypeName(PaymentMethodTypeEnum.Constant.ANONYMOUS_CARD)
@SuperBuilder
@ToString
@NoArgsConstructor
public class AnonymousCardPaymentMethodResponse extends PaymentMethodResponse {
    @Override
    public PaymentMethodTypeEnum getPaymentMethodType() {
        return ANONYMOUS_CARD;
    }
}
