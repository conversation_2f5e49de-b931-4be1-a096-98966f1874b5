
package com.tigo.galaxion.sales.facade.connector.catalog.domain.response;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "status",
    "pricePlans"
})
@Generated("jsonschema2pojo")
public class ActivationFee {

    @JsonProperty("status")
    private String status;
    @JsonProperty("pricePlans")
    private List<PricePlan> pricePlans;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new LinkedHashMap<String, Object>();

    @JsonProperty("status")
    public String getStatus() {
        return status;
    }

    @JsonProperty("status")
    public void setStatus(String status) {
        this.status = status;
    }

    public ActivationFee withStatus(String status) {
        this.status = status;
        return this;
    }

    @JsonProperty("pricePlans")
    public List<PricePlan> getPricePlans() {
        return pricePlans;
    }

    @JsonProperty("pricePlans")
    public void setPricePlans(List<PricePlan> pricePlans) {
        this.pricePlans = pricePlans;
    }

    public ActivationFee withPricePlans(List<PricePlan> pricePlans) {
        this.pricePlans = pricePlans;
        return this;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    public ActivationFee withAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
        return this;
    }

}
