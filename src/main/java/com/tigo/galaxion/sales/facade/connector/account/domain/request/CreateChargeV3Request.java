package com.tigo.galaxion.sales.facade.connector.account.domain.request;

import javax.validation.constraints.NotBlank;

import com.tigo.galaxion.sales.facade.connector.account.domain.enums.BillingType;

import lombok.Data;

@Data
public class CreateChargeV3Request {
	private String advanceArrears;
	@NotBlank
	private BillingType billingType;
	private String catalogCode;
	private Integer coolingOffPeriod;
	private String description;
	private Integer overriddenPrice;
}
