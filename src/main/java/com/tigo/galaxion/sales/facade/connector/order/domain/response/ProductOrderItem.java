package com.tigo.galaxion.sales.facade.connector.order.domain.response;

import lombok.*;

import java.util.List;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class ProductOrderItem {
    private String id;
    private int quantity;
    private String action;
    private List<ItemPrice> itemPrice;
    private List<ItemTerm> itemTerm;
    private List<PaymentRef> payment;
    private Product product;
    private ProductOffering productOffering;
}

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@Data
class ItemPrice {
    private String id;
    private String description;
    private Price price;
    private String priceType;
}

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@Data
class Price {
    private String id;
    private String unit;
    private double value;
    private double taxRate;
}

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@Data
class ItemTerm {
    private String id;
    private String description;
    private Duration duration;
    private String name;
}

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@Data
class Duration {
    private int amount;
    private String units;
}
