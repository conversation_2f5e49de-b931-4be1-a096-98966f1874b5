package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.SearchAcquisitionProspectsOffersRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellOfferSearchRequest;
import com.tigo.galaxion.sales.facade.domain.request.GetOfferForAcquisitionProspectRequest;
import com.tigo.galaxion.sales.facade.domain.request.GetOfferForCrossSellRequest;
import com.tigo.galaxion.sales.facade.domain.request.GetTariffPlansForAnOfferRequest;
import com.tigo.galaxion.sales.facade.domain.response.ItemGroupWithAddOnsResponse;
import com.tigo.galaxion.sales.facade.domain.response.OffersAndConditionalDiscountsResponse;
import com.tigo.galaxion.sales.facade.domain.response.ProspectCatalogInclusiveEquipmentGroupResponse;
import com.tigo.galaxion.sales.facade.domain.response.TariffPlansAndConditionalDiscountsResponse;

import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CatalogService {

    private final AcquisitionProspectClient acquisitionProspectClient;
    private final CrossSellClient crossSellClient;

    @Transactional(readOnly = true)
    public OffersAndConditionalDiscountsResponse getOffersForAcquisition(String prospectReference,
                                                                         GetOfferForAcquisitionProspectRequest request) {
        var searchAcquisitionProspectsOffersRequest = SearchAcquisitionProspectsOffersRequest
                .builder()
                .serviceGroup(request.getServiceGroup())
                .parentOfferId(request.getParentOfferId())
                .build();
        return acquisitionProspectClient.getOffers(prospectReference, searchAcquisitionProspectsOffersRequest);
    }

    @Transactional(readOnly = true)
    public OffersAndConditionalDiscountsResponse getOffersForCrossSell(String crossSellReference,
                                                                       GetOfferForCrossSellRequest request) {
        var crossSellOfferSearchRequest = CrossSellOfferSearchRequest
                .builder()
                .serviceGroup(request.getServiceGroup())
                .parentOfferCode(request.getParentOfferCode())
                .build();

        return crossSellClient.getOffers(crossSellReference, crossSellOfferSearchRequest);
    }

    @Transactional(readOnly = true)
    public TariffPlansAndConditionalDiscountsResponse getTariffsPlansForAnOffer(
        String prospectReference,
        String offerCode,
        GetTariffPlansForAnOfferRequest request
    ){
        return acquisitionProspectClient.getTariffsPlansForAnOffer(prospectReference, offerCode, request);
    }

    @Transactional(readOnly = true)
    public List<ItemGroupWithAddOnsResponse> getAddOnsForAnOffer(
        String prospectReference,
        Long offerId
    ){
        return acquisitionProspectClient.getAddOnsForAnOffer(prospectReference, offerId);
    }

    @Transactional(readOnly = true)
    public List<ProspectCatalogInclusiveEquipmentGroupResponse> getEquipmentsForAnOffer(
        String prospectReference,
        Long offerId
    ){
        return acquisitionProspectClient.getEquipmentForAnOffer(prospectReference, offerId);
    }
}
