package com.tigo.galaxion.sales.facade.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Getter
@Builder
@ToString
@AllArgsConstructor
@ApiModel("Get offer request")
public class GetOfferForCrossSellRequest {

    @NotBlank
    @ApiModelProperty(value = "The service group")
    private String serviceGroup;

    @ApiModelProperty(value = "The parent offer code")
    private String parentOfferCode;
}
