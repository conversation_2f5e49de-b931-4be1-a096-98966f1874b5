package com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeasibilityServicesRequest {

    @JsonProperty("addressCode")
    private String addressCode;

    @JsonProperty("latitude")
    private String latitude;

    @JsonProperty("longitude")
    private String longitude;

    @JsonProperty("municipalityCode")
    private String municipalityCode;

    @JsonProperty("requestCode")
    private String requestCode;
}
