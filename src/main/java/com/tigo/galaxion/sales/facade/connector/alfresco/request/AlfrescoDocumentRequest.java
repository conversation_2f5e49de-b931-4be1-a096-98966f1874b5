package com.tigo.galaxion.sales.facade.connector.alfresco.request;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlfrescoDocumentRequest {
    @ApiModelProperty(value = "id Tramite", required = true, example = "31")
    @JsonProperty("idTramite")
    private int idTramite;
    
    @ApiModelProperty(value = "id Canal", required = true, example = "1")
    @JsonProperty("idCanal")
    private int idCanal;

    @ApiModelProperty(value = "Correo", required = true, example = "<EMAIL>")
    @JsonProperty("correo")
    private String correo;

    @ApiModelProperty(value = "Requiere Firma Remota", required = true, example = "false")
    @JsonProperty("reqFirmaRemota")
    private String reqFirmaRemota;

    @ApiModelProperty(value = "Requiere Firma Manuscrita", required = true, example = "true")
    @JsonProperty("reqFirmaManuscrita")
    private String reqFirmaManuscrita;

    @ApiModelProperty(value = "Requiere Firma Electrónica", required = true, example = "false")
    @JsonProperty("reqFirmaElectronica")
    private String reqFirmaElectronica;
    
    @ApiModelProperty(value = "URL Respuesta", required = true, example = "http://crm.devdmstigo.co:8080/servicioUrl")
    @JsonProperty("urlRespuesta")
    private String urlRespuesta;

    @ApiModelProperty(value = "Metadata con información variada para diversos documentos.", required = true, example = "[\r\n" + //
                "        \"{\\\"idFormato\\\":\\\"9\\\",\\\"mail\\\":\\\"<EMAIL>\\\",\\\"fecha\\\":\\\"14/03/2023\\\",\\\"departamento1\\\":\\\"Distrito Capital\\\",\\\"mes\\\":\\\"marzo\\\",\\\"dia\\\":\\\"14\\\",\\\"ano\\\":\\\"2023\\\",\\\"aceptaConsulta\\\":\\\"SI\\\",\\\"ciudad\\\":\\\"Bogotá D.C\\\",\\\"nombres\\\":\\\"Nicolas Wagner\\\",\\\"tipoDocumento\\\":\\\"Cédula de Ciudadanía\\\",\\\"numeroDocumento\\\":\\\"45000014\\\",\\\"direccion\\\":\\\"Calle 123 # 4 56\\\",\\\"telefono\\\":\\\"300120000\\\",\\\"firma\\\":\\\"https://upload.wikimedia.org/wikipedia/commons/8/8b/Firma_de_Harold.jpg\\\",\\\"noAceptaConsulta\\\":\\\"N/A\\\",\\\"ciudad1\\\":\\\"Bogotá D.C\\\"}\",\r\n" + //
                "        \"{\\\"idFormato\\\":42,\\\"idVersion\\\":\\\"1.1\\\",\\\"valorCB\\\":\\\"120.000\\\",\\\"renovacion\\\":\\\"SI\\\",\\\"telefonia\\\":\\\"SI\\\",\\\"internet\\\":\\\"SI\\\",\\\"Television\\\":\\\"SI\\\",\\\"otros\\\":\\\"Satélite\\\",\\\"fechaActivacion\\\":\\\"03/12/2021\\\",\\\"nombres\\\":\\\"Nicolas Wagner\\\",\\\"mail\\\":\\\"<EMAIL>\\\",\\\"telefono\\\":\\\"321300012\\\",\\\"direccionserv\\\":\\\"Calle 123 # 4 56\\\",\\\"estrato\\\":\\\"4\\\",\\\"departamento\\\":\\\"Bogotá D.C\\\",\\\"ciudad\\\":\\\"Bogotá D.C\\\",\\\"dirSuscriptor\\\":\\\"Calle 123 # 4 56\\\",\\\"planTelefonia\\\":\\\"El más alto\\\",\\\"VlrPlanTO\\\":\\\"50.000\\\",\\\"serviciosAdicTO\\\":\\\"5.000\\\",\\\"planTelevision\\\":\\\"Ultra\\\",\\\"VlrPlanTV\\\":\\\"40.000\\\",\\\"serviciosAdicTV\\\":\\\"NO\\\",\\\"DecosAd\\\":\\\"1\\\",\\\"planBandaAncha\\\":\\\"UltramegaSatelital\\\",\\\"VlrPlanBA\\\":\\\"30.000\\\",\\\"velocidad\\\":\\\"ADSL Ultra\\\",\\\"cantidadExtensorWifi\\\":\\\"2\\\",\\\"ObservacionInternet\\\":\\\"Ninguna\\\",\\\"permanenciaProducto1\\\":\\\"5 años\\\",\\\"permanenciaProducto2\\\":\\\"5 años\\\",\\\"permanenciaProducto3\\\":\\\"5 años\\\",\\\"valorMinimo\\\":\\\"120.000\\\",\\\"costoReconexion\\\":\\\"0\\\",\\\"permanenciamin\\\":\\\"SI\\\",\\\"Vlrconexion\\\":\\\"0\\\",\\\"descuentoconexion\\\":\\\"0\\\",\\\"iniciaPermanencia\\\":\\\"01/12/2021\\\",\\\"finPermanencia\\\":\\\"01/12/2026\\\",\\\"mes1\\\":\\\"10000\\\",\\\"mes2\\\":\\\"10000\\\",\\\"mes3\\\":\\\"10000\\\",\\\"mes4\\\":\\\"10000\\\",\\\"mes5\\\":\\\"10000\\\",\\\"mes6\\\":\\\"10000\\\",\\\"mes12\\\":\\\"10000\\\",\\\"mes11\\\":\\\"10000\\\",\\\"mes10\\\":\\\"10000\\\",\\\"mes9\\\":\\\"10000\\\",\\\"mes8\\\":\\\"10000\\\",\\\"mes7\\\":\\\"10000\\\",\\\"tipoDocumento\\\":\\\"Cédula Ciudadanía\\\",\\\"numeroDocumento\\\":\\\"45000014\\\",\\\"fecha\\\":\\\"01/12/2021\\\",\\\"firma\\\":\\\"https://upload.wikimedia.org/wikipedia/commons/8/8b/Firma_de_Harold.jpg\\\",\\\"productosPaquete\\\": \\\"SI\\\",\\\"serviciosAdicInternet\\\":\\\"\\\",\\\"observacionesTo\\\":\\\"Alguna observación\\\",\\\"cantidadExtensorWifiMesh\\\":\\\"\\\",\\\"observacionesTv\\\":\\\"Otra Obervación\\\" }\",\r\n" + //
                "        \"{\\\"idFormato\\\":\\\"8\\\",\\\"nombres\\\":\\\"LUZ HELENA TORO HINESTROZA\\\",\\\"tipoDocumento\\\":\\\"Cédula de Ciudadanía\\\",\\\"numeroDocumento\\\":\\\"67016426\\\",\\\"fecha\\\":\\\"01/04/2024\\\",\\\"firma\\\":\\\"https://upload.wikimedia.org/wikipedia/commons/8/8b/Firma_de_Harold.jpg\\\"}\"\r\n" + //
                "    ]")    
    @JsonProperty("metadata")
    private List<String> metadata;
    
    @ApiModelProperty(value = "Información variada de propiedades.", required = true, example = "\"{\r\n\"numeroCuenta\": \"\",\"segmento_Clientes\": \"HOME\",\"Fechadocumento_OperacionesClientes\": \"2023-08-23T09:07:00-05:00\",\"DocumentoIdentidad_Clientes\":\"1416151213\",\"TipoDocumentoidentificacion_Cliente\":\"Cédula de Ciudadanía\",\"msisdn\": \"321300012\",\"idFactura\": \"\"}\"")
    @JsonProperty("properties")
    private String properties;
}
