package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
public class TigoAddonResponse {

    @ApiModelProperty(value = "The add-on id", example = "123456789", required = true)
    private Long id;

    @ApiModelProperty(value = "The description", example = "Go More 2", required = true)
    private String description;

    @ApiModelProperty(value = "Display order, lowest numbers displayed first", example = "10", required = true)
    private Long displayOrder;

    @Builder.Default
    @ApiModelProperty(value = "The set of usages", required = true)
    private List<TigoUsageResponse> usages = new ArrayList<>();

    @ApiModelProperty(value = "The add-on catalog code", example = "REPLACEMENT_SIM", required = true)
    private String catalogCode;

    @ApiModelProperty(value = "The amount to be paid vat included", required = true)
    private TigoAmountResponse amountVatIncluded;

    @ApiModelProperty(value = "The amount to be paid vat excluded", required = true)
    private TigoAmountResponse amountVatExcluded;

    @ApiModelProperty(value = "the max quantity of this addon per offer", example = "2", required = true)
    private Long max;

    @ApiModelProperty(value = "The item group catalog id", example = "SUBSCRIPTION", required = true)
    private String itemGroup;

    @ApiModelProperty(value = "Recurring amounts by occurrence")
    @Builder.Default
    private List<TigoRecurringAmountByOccurrenceResponse> recurringAmountsByOccurrence = new ArrayList<>();
}
