package com.tigo.galaxion.sales.facade.connector.prospectlead.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CustomerReference {
    @ApiModelProperty(value = "Reference Type", required = true, example = "1")
    @JsonProperty("referentType")
    private int referenceType;

    @ApiModelProperty(value = "Document Type", required = true, example = "CC")
    @JsonProperty("documentType")
    private String documentType;

    @ApiModelProperty(value = "Document Id", required = true, example = "12321113")
    @JsonProperty("documentId")
    private String documentId;

    @ApiModelProperty(value = "Names", required = true, example = "Juan")
    @JsonProperty("names")
    private String names;
    
    @ApiModelProperty(value = "Last Name", required = true, example = "Perez")
    @JsonProperty("lastName")
    private String lastName;

    @ApiModelProperty(value = "Email", required = false, example = "<EMAIL>")
    @JsonProperty("email")    
    private String email;
    
    @ApiModelProperty(value = "Phone", required = false, example = "57 ************")
    @JsonProperty("phone")    
    private String phone;
}
