package com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Getter
@ToString
@Builder
@NoArgsConstructor
public class CrossSellEligibilityResponse {

    private String serviceGroup;

    @Setter
    private boolean eligible;

    @Builder.Default
    private List<String> causes = new ArrayList<>();

    public void addCause(String cause) {
        causes.add(cause);
    }
}
