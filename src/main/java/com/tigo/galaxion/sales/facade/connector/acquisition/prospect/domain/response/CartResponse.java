package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import java.util.ArrayList;
import java.util.List;

import com.tigo.galaxion.sales.facade.domain.enumeration.AcquisitionTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
@ToString
public class CartResponse {

    private String uuid;

    private AcquisitionTypeEnum acquisitionType;

    private String creditScore;

    private Boolean agreedTermsAndConditions;

    private AmountResponse amountVatIncluded;

    private AmountResponse amountVatExcluded;

    @Builder.Default
    private List<OfferResponse> offers = new ArrayList<>();

    private String channelGroup;

    @Builder.Default
    private List<AdditionalAllowedOfferResponse> numberAdditionalAllowedOffers = new ArrayList<>();

    @Builder.Default
    private List<RecurringAmountByOccurrenceResponse> recurringAmountsByOccurrence = new ArrayList<>();
}
