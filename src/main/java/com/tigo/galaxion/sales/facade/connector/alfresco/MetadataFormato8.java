package com.tigo.galaxion.sales.facade.connector.alfresco;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetadataFormato8 {

    private static final String ID_FORMATO = "8";

    private String idFormato;
    private String nombres;
    private String tipoDocumento;
    private String numeroDocumento;
    private String fecha;
    private String firma;

    public MetadataFormato8(ProspectLeadResponse p) {

        LocalDate fechaActual = LocalDate.now();

        setIdFormato(ID_FORMATO);
        setNombres(p.getCustomer().getNames().concat(" ").concat(p.getCustomer().getLastName()));
        setTipoDocumento(this.getTypeDocument(p.getCustomer().getDocumentType()));
        setNumeroDocumento(p.getCustomer().getDocumentId());
        setFecha(fechaActual.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        setFirma(p.getCustomer().getCallId());

    }

    public String toMetadataString() {
        //String idFormato8 = "{\"idFormato\":\"8\",\"nombres\":\"LUZ HELENA TORO HINESTROZA\",\"tipoDocumento\":\"Cédula de Ciudadanía\",\"numeroDocumento\":\"67016426\",\"fecha\":\"01/04/2024\",\"firma\":\"https://upload.wikimedia.org/wikipedia/commons/8/8b/Firma_de_Harold.jpg\"}";

        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.valueToTree(this);
        
        return jsonNode.toString();
    }

    private String getTypeDocument(String documentType){
        String typeDocument="";
        switch (documentType){
            case "CC":
            case "Cédula de Ciudadanía":
                typeDocument = "Cédula de Ciudadanía";
                break;
            case "CE":
            case "Cédula de Extranjería":
                typeDocument = "Cédula de Extranjería";
                break;
            case "Numero de Identificacion Tributaria":
            case "NIT":
                typeDocument = "NIT";
                break;
            case "Pasaporte":
                typeDocument = documentType;
                break;
            default:
                typeDocument = "Do not exists this Document Type";
        }
        return typeDocument;
    }
}
