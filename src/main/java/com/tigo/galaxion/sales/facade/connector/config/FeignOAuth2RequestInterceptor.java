package com.tigo.galaxion.sales.facade.connector.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import fr.itsf.keycloak.helper.KeycloakAuthenticationTokenHelper;

import static java.lang.String.format;

public class FeignOAuth2RequestInterceptor implements RequestInterceptor {

    protected static final String DEFAULT_AUTHORIZATION_HEADER = "Authorization";
    protected static final String AUTH_ROUTE_PATTERN = "/auth/";

    protected static final String DEFAULT_BEARER_TOKEN_PATTERN = "Bearer %s";

    @Override
    public void apply(RequestTemplate requestTemplate) {
        if (requestTemplate.path().contains(AUTH_ROUTE_PATTERN)) {
            final var token = KeycloakAuthenticationTokenHelper.getTokenString();
            if (token != null) {
                requestTemplate.header(DEFAULT_AUTHORIZATION_HEADER, format(DEFAULT_BEARER_TOKEN_PATTERN, token));
            }
        }
    }
    
}
