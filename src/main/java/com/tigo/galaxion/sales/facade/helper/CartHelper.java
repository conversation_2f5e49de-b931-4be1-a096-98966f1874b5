package com.tigo.galaxion.sales.facade.helper;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.OfferResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CartHelper {

    public static final String SERVICE_GROUP_MOBILE = "MOBILE";

    public static boolean checkIfCartContainsMobileOfferWithTariffPlanNotSimOnly(CartResponse cartResponse) {
        return cartResponse.getOffers()
                           .stream()
                           .map(OfferResponse::getBaseOffer)
                           .toList()
                           .stream()
                           .anyMatch(baseOfferResponse -> baseOfferResponse.getServiceGroup().equals(SERVICE_GROUP_MOBILE) && baseOfferResponse.getSimOnly().equals(false));
    }
}
