package com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RitelCoverage {
    private String ritel;
    private String requiresRitel;
    private String ritelCompliance;
}
