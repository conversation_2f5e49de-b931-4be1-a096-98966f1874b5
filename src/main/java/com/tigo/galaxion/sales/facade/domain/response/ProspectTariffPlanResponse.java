package com.tigo.galaxion.sales.facade.domain.response;

import java.util.ArrayList;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AmountResponse;

public record ProspectTariffPlanResponse(
  AmountResponse activationFeesVatExcluded,
  AmountResponse activationFeesVatIncluded,
  int amountVatExcluded,
  int amountVatIncluded,
  String category,
  String code,
  String commitmentDuration,
  String description,
  String discountFrequency,
  int discountOccurrence,
  AmountResponse discountedActivationFeesVatExcluded,
  AmountResponse discountedActivationFeesVatIncluded,
  int discountedAmountVatExcluded,
  int discountedAmountVatIncluded,
  ArrayList<ProspectDiscountResponse> discounts,
  int displayOrder,
  boolean portIn,
  ArrayList<String> portInTypes,
  ArrayList<TariffPlanRecurringAmountResponse> recurringAmountsByOccurrence,
  boolean simOnly
) {

}
