package com.tigo.galaxion.sales.facade.services.order_notification;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.symphonica.SymphonicaRequest;
import com.tigo.galaxion.sales.facade.services.field_service.multiple_operations.MultipleOperationsClient;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.BaseObject;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.ExecuteMultipleOperations;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.ExecuteMultipleOperationsResponse;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.MCCustomerAsset;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.MCMaterialTypeReference;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.MCPartsInStock;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.MCServicesReference;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.StandardOperation;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.StandardOperations;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.app.dto.VariableUpdateDTO;
import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;
import mc.monacotelecom.workflow.base.enumeration.ProcessType;
import mc.monacotelecom.workflow.order.dto.CancelOrderRequestDTO;

@Service
@RequiredArgsConstructor
@Slf4j
public class SymphonicaOrderNotificacionService {
    @Value("${wfe.signal.SP_DEVICE_REGISTRATION_BB_SYMPHONICA}")
    private String signalName;

    @Value("${symphonica.statuses}")
    private List<String> statusList;

    private final WorkflowEngineClient workflowEngineClient;
    private final MultipleOperationsClient multipleOperationsClient;

    public void processNotification(SymphonicaRequest request) {
        log.info("SymphonicaOrderNotificacionService :: orderId: {}; signalName: {}",
                request.getPayload().getExternalId(), signalName);

        // se registra la señal para todos los estados, en una variable dentro de wfe
        try {
            workflowEngineClient.updateConnectorData(
                    request.getPayload().getExternalId(),
                    VariableUpdateDTO.builder()
                            .type(ProcessType.SOM)
                            .data(request)
                            .variable("symphonica")
                            .build());
        } catch (Exception e) {
            log.info(signalName, request, e);
        }

        // solo si es COMPLETED se manda la señal
        if (request.getPayload().getState().equals("COMPLETED")) {
            workflowEngineClient.processSignal(WorkflowSignalRequestDTO.builder()
                    .name(signalName)
                    .orderId(request.getPayload().getExternalId())
                    .context(Map.of("symphonicaResult", request.getPayload().getState()))
                    .build());
            // si aparecen en la lista, se cancela el proceso
        } else if (statusList != null && statusList.contains(request.getPayload().getState())) {
            workflowEngineClient.cancelOrder(CancelOrderRequestDTO.builder()
                    .type(ProcessType.SOM)
                    .orderId(request.getPayload().getExternalId())
                    .build());
            notifyFieldService(request);
        }
    }

    private void notifyFieldService(SymphonicaRequest request) {

        var wfeVariable = workflowEngineClient.getConnectorData(
                request.getPayload().getExternalId(),
                ProcessType.SOM,
                null,
                List.of("deviceParams"));
        var deviceParams = (Map<String, Object>) wfeVariable.get("deviceParams");

        log.info("provisioningId: {}", deviceParams.get("provisioningId"));

        var service_1 = new MCServicesReference();
        service_1.setExternalRefID(request.getPayload().getExternalId());

        var mcPartsInStock_1 = new MCPartsInStock();
        mcPartsInStock_1.setExternalRefID(deviceParams.get("provisioningId").toString());
        mcPartsInStock_1.setProvisioningComment("Provisioning failed");
        mcPartsInStock_1.setProvisioningStatus("ERROR");
        mcPartsInStock_1.setService(service_1);

        var object_1 = new BaseObject();
        object_1.setMcPartsInStock(mcPartsInStock_1);

        var operation_1 = new StandardOperation();
        operation_1.setObject(object_1);

        var materialType_2 = new MCMaterialTypeReference();
        materialType_2.setCode(deviceParams.get("code").toString());

        var service_2 = new MCServicesReference();
        service_2.setExternalRefID(request.getPayload().getExternalId());
        service_2.setServiceCode("ERROR");

        var mcCustomerAsset_2 = new MCCustomerAsset();
        mcCustomerAsset_2.setSerial(deviceParams.get("deviceId").toString());
        mcCustomerAsset_2.setExternalRefID(deviceParams.get("assetId").toString());
        mcCustomerAsset_2.setMaterialType(materialType_2);
        mcCustomerAsset_2.setService(service_2);

        var object_2 = new BaseObject();
        object_2.setMcCustomerAsset(mcCustomerAsset_2);

        var operation_2 = new StandardOperation();
        operation_2.setObject(object_2);

        var operations = new StandardOperations();
        operations.setOperation(List.of(operation_1, operation_2));

        var body = new ExecuteMultipleOperations();
        body.setOperations(operations);

        try {
            ExecuteMultipleOperationsResponse response = multipleOperationsClient.multipleOperations(body);
            log.info("ExecuteMultipleOperationsResponse: {}", response);
        } catch (Exception e) {
            log.info("request: {}, e: {}, body: {}", request, e, body);
        }

    }
}
