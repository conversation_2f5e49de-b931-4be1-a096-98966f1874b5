package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.payment_method;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.PaymentMethodTypeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@JsonTypeName(PaymentMethodTypeEnum.Constant.MANUAL)
@SuperBuilder
@Getter
@ToString
@NoArgsConstructor
public class ManualPaymentMethodResponse extends PaymentMethodResponse {

    @Override
    public PaymentMethodTypeEnum getPaymentMethodType() {
        return PaymentMethodTypeEnum.MANUAL;
    }

}
