package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.fraudmanagement.FraudManagementClient;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.FraudsRequestBody;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.response.FraudsResponseBody;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FraudManagementService {

    private final FraudManagementClient client;

    public FraudsResponseBody frauds(FraudsRequestBody request) {
        return client.frauds(request);
    }

}
