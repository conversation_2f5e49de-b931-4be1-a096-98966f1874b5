package com.tigo.galaxion.sales.facade.connector.contact.domain.response;

import com.tigo.galaxion.sales.facade.connector.contact.domain.enumeration.TypeEmailEnum;
import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Singular;
import lombok.ToString;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ContactV2Response {

    private String firstName;

    private String lastName;

    private LocalDate birthDate;

    @Singular
    private List<ContactEmailV2Response> emails = new ArrayList<>();

    @Singular
    private List<ContactPhoneNumberV2Response> phoneNumbers = new ArrayList<>();

    @Singular
    private List<ContactAddressResponse> addresses = new ArrayList<>();

    @Builder.Default
    private List<ContactIdentityDocumentV2Response> identityDocuments = new ArrayList<>();

    public Optional<ContactEmailV2Response> getFirstMainEmail() {
        return emails.stream()
                     .filter(email -> TypeEmailEnum.MAIN.equals(email.getType()))
                     .findFirst();
    }

    public Optional<ContactPhoneNumberV2Response> getFirstMobilePhoneNumber() {
        return phoneNumbers.stream()
                           .filter(phoneNumber -> "MOBILE".equals(phoneNumber.getType()))
                           .findFirst();
    }

    public Optional<ContactAddressResponse> getFirstDeliveryAddress() {
        return addresses.stream()
                        .filter(address -> AddressTypeEnum.DELIVERY.equals(address.getType()))
                        .findFirst();
    }

}
