package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.account.AccountClient;
import com.tigo.galaxion.sales.facade.connector.account.domain.request.CreateAddOnToService;
import com.tigo.galaxion.sales.facade.connector.account.domain.request.CreateInclusiveEquipmentRequest;
import com.tigo.galaxion.sales.facade.connector.account.domain.response.AccountsCategoriesResponseBody;
import com.tigo.galaxion.sales.facade.connector.account.domain.response.EquipmentWithoutAddonV3Response;

import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@RequiredArgsConstructor
public class AccountsService {
    private final AccountClient accountClient;
    

    public List<AccountsCategoriesResponseBody> getAccountsCategories(String type) {
        List<AccountsCategoriesResponseBody> accountsCategories;
        if (type == null || type.isEmpty()) {
            accountsCategories = accountClient.getAccountsCategories();
        } else {
            accountsCategories = accountClient.getAccountsCategories()
            .stream()
            .filter(item -> item.getType().equals(type))
            .collect(Collectors.toList());
        }
        return accountsCategories;
    }
}
