package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.payment_method;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.PaymentMethodTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@ApiModel(description = "The payment card information.", parent = PaymentMethodResponse.class)
@JsonTypeName(PaymentMethodTypeEnum.Constant.DIRECT_DEBIT)
@SuperBuilder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DirectDebitPaymentMethodResponse extends PaymentMethodResponse {

    @ApiModelProperty(value = "Sepa holder.", example = "M.Toto", required = true)
    private String holder;

    @ToString.Exclude
    @ApiModelProperty(value = "A IBAN.", example = "*********************", required = true)
    private String iban;

    @ApiModelProperty(value = "A bank name.", example = "John Doe", required = true)
    private String bankName;

    @ApiModelProperty(value = "A branch name.", required = true)
    private String branchName;

    @ApiModelProperty(value = "A bic.", required = true)
    private String bic;

    @Override
    public PaymentMethodTypeEnum getPaymentMethodType() {
        return PaymentMethodTypeEnum.DIRECT_DEBIT;
    }

}
