package com.tigo.galaxion.sales.facade.domain.response;

import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@ApiModel
@SuperBuilder
@Getter
@NoArgsConstructor
public class TigoInclusiveMobileBroadbandModemResponse extends TigoInclusiveEquipmentResponse {

    @Builder.Default
    @ApiModelProperty(value = "The item type", example = "MOBILE_BROADBAND_MODEM", required = true)
    private EquipmentTypeEnum type = EquipmentTypeEnum.MOBILE_BROADBAND_MODEM;
}
