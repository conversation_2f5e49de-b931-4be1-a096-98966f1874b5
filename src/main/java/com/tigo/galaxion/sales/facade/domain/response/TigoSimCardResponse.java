package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
@ToString
public class TigoSimCardResponse {

    @ApiModelProperty(value = "The msisdn to add in the cart", required = true, example = "0850000000")
    private String msisdn;

    @ApiModelProperty(value = "The simCard ICCID to add in the cart", required = true, example = "8981100022152967705")
    private String iccid;

    @ApiModelProperty(value = "The simCard IMSI to add in the cart", required = true, example = "1234567890123")
    private String imsi;
}
