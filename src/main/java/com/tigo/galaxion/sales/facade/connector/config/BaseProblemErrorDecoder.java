package com.tigo.galaxion.sales.facade.connector.config;

import com.fasterxml.jackson.databind.ObjectMapper;

import feign.Response;
import feign.RetryableException;
import feign.codec.ErrorDecoder;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Problem;
import org.zalando.problem.ProblemBuilder;
import org.zalando.problem.Status;
import org.zalando.problem.ThrowableProblem;

import java.io.IOException;
import java.io.Reader;
import java.nio.charset.StandardCharsets;

public abstract class BaseProblemErrorDecoder implements ErrorDecoder {

    private final ObjectMapper objectMapper;

    protected BaseProblemErrorDecoder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    protected abstract ThrowableProblem buildProblem(ThrowableProblem throwableProblem);

    protected abstract String getDefaultTitle();

    @Override
    public Exception decode(String methodKey, Response response) {
        AbstractThrowableProblem abstractThrowableProblem;
        try {
            Reader reader = response.body().asReader(StandardCharsets.UTF_8);
            String result = IOUtils.toString(reader);
            abstractThrowableProblem = objectMapper.readValue(result, AbstractThrowableProblem.class);
        } catch (IOException ioException) {
            return ioException;
        }
        
        if (response.status() == 401) {
    		var attributes = RequestContextHolder.getRequestAttributes();
    		var request = response.request();
    		
    		if (attributes != null) {
    			var retryCountAttr = attributes.getAttribute("retryCount", RequestAttributes.SCOPE_REQUEST);
	    		var retryCount = (retryCountAttr != null ? (Integer) retryCountAttr : 0) + 1;
	            
	            if (retryCount > 1) {
	            	throw buildProblem(getThrowableProblem(response, abstractThrowableProblem));
	            }
	            
	            attributes.setAttribute("retryCount", retryCount, RequestAttributes.SCOPE_REQUEST);
	            attributes.setAttribute("evict", true, RequestAttributes.SCOPE_REQUEST);
    		}
            return new RetryableException(response.status(), "Retrying due to outdated token", 
            		request.httpMethod(), abstractThrowableProblem, new java.util.Date(), request);
    	}

        throw buildProblem(getThrowableProblem(response, abstractThrowableProblem));
    }

    private ThrowableProblem getThrowableProblem(Response response, AbstractThrowableProblem abstractThrowableProblem) {
        String detail = getDetail(abstractThrowableProblem);
        String title = getTitle(abstractThrowableProblem);
        ProblemBuilder problemBuilder = Problem.builder()
                                               .withDetail(detail)
                                               .withInstance(abstractThrowableProblem.getInstance())
                                               .withStatus(Status.valueOf(response.status()))
                                               .withTitle(title)
                                               .withType(abstractThrowableProblem.getType());
        abstractThrowableProblem.getParameters().forEach(problemBuilder::with);
        return problemBuilder.build();
    }

    private String getTitle(AbstractThrowableProblem abstractThrowableProblem) {
        if (StringUtils.isBlank(abstractThrowableProblem.getTitle())) {
            return abstractThrowableProblem.getParameters().getOrDefault("errorCode", getDefaultTitle()).toString();
        } else {
            return abstractThrowableProblem.getTitle();
        }
    }

    private String getDetail(AbstractThrowableProblem abstractThrowableProblem) {
        if (StringUtils.isBlank(abstractThrowableProblem.getDetail())) {
            return abstractThrowableProblem.getParameters().getOrDefault("errorMessage", "No error message").toString();
        } else
            return abstractThrowableProblem.getDetail();
    }

}
