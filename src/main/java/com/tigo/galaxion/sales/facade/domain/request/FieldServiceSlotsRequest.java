package com.tigo.galaxion.sales.facade.domain.request;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Builder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FieldServiceSlotsRequest {

    private String callId;

    private Integer number;

    @NotNull
    @ApiModelProperty(value = "The desired initial date to get the available time slots", example = "2026-01-21T21:00:00Z", required = true)
    private String startDate;

    @NotNull
    @ApiModelProperty(value = "The desired end date is to get the available time slots", example = "2026-01-21T21:00:00Z", required = true)
    private String endDate;

    private String area;
    private String district;
    private Integer priority;
    private String city;
    private String countryId;

    @NotNull
    @ApiModelProperty(value = "Address description from installation address", example = "1431065734 * KR 43 A # 53 D - 46 SUR IN 1609 * Rural *  CR 43 A  # 53 D SUR - 46, INTERIOR 1609 Los Arias", required = true)
    private String address;

    private String street;

    @NotNull
    @ApiModelProperty(value = "Address latitude", example = "27.7439593445784", required = true)
    private String latitude;

    @NotNull
    @ApiModelProperty(value = "Address longitude", example = "130.935331326267", required = true)
    private String longitude;

    @NotNull
    @ApiModelProperty(value = "Category for task type", example = "Instalacion HFC", required = true)
    private String taskTypeCategory;

    @NotNull
    @ApiModelProperty(value = "Task Type", example = "Nuevo Duo HFC", required = true)
    private String taskType;

    private String openDate;
    private String region;
    private String excludeCurrentAppointment;
    private String profile;

}
