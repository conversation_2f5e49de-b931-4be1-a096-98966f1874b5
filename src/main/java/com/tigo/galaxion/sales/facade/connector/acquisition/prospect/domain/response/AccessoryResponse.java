package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AccessoryResponse {

    private Long id;

    private String description;

    private String catalogCode;

    private AmountResponse amountVatIncluded;

    private AmountResponse amountVatExcluded;

    @Builder.Default
    private List<ServiceDomainResponse> serviceDomains = new ArrayList<>();

}
