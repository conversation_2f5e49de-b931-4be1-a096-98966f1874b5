package com.tigo.galaxion.sales.facade.services;

import lombok.RequiredArgsConstructor;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tigo.galaxion.sales.facade.model.entity.CounterEntity;
import com.tigo.galaxion.sales.facade.model.repository.CounterRepository;

@Service
@RequiredArgsConstructor
public class CounterService {
    private final CounterRepository counterRepository;

    // get the value of the counter by name and then increment it by 1 and update the value. It should be a transactional operation to avoid the read and update operation to be done by different threads.
    @Transactional
    public Long incrementCounter(String name) {
        Optional<CounterEntity> counter = counterRepository.findByName(name);
        Long value = counter.get().getValue();
        counterRepository.updateValueById(counter.get().getId(), value + 1);
        return value;
    }
}
