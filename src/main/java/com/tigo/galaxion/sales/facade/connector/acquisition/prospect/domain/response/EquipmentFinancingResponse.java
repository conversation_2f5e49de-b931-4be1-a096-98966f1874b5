package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
public class EquipmentFinancingResponse {
    private Long id;
    private String code;
    private Long occurrence;
    private Long amountUpFrontChosen;
}
