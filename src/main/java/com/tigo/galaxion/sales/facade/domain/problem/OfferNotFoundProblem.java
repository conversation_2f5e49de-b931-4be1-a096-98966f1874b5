package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class OfferNotFoundProblem extends AbstractThrowableProblem {

    public OfferNotFoundProblem(Long id) {
        super(null,
              "offer-not-found-in-cart",
              Status.NOT_FOUND,
              String.format("Offer #%s not found in cart", id));
    }

}
