package com.tigo.galaxion.sales.facade.domain.problem;
 
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;
 
public class CustomerUuidNotFoundProblem extends AbstractThrowableProblem {
    public CustomerUuidNotFoundProblem(String uuid) {
        super(null,
              "customer-UUID-not-found",
              Status.NOT_FOUND,
              String.format("The customer UUID %s not found.", uuid));
    }
}
