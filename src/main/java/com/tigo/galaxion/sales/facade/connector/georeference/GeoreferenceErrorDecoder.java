package com.tigo.galaxion.sales.facade.connector.georeference;

import feign.Response;
import feign.RetryableException;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;
import org.zalando.problem.ThrowableProblem;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.problem.GeoreferenceProblem;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.response.GeoreferenceErrorResponse;

@Slf4j
@RequiredArgsConstructor
public class GeoreferenceErrorDecoder implements ErrorDecoder {

    private static final String TITLE = "georeference-service-error";

    @Override
    public Exception decode(String methodKey, Response response) {
    	if (response.status() == 401) {
    		var exception = feign.FeignException.errorStatus(methodKey, response);
    		var attributes = RequestContextHolder.getRequestAttributes();
    		var request = response.request();
    		
    		if (attributes != null) {
    			var retryCountAttr = attributes.getAttribute("retryCount", RequestAttributes.SCOPE_REQUEST);
	    		var retryCount = (retryCountAttr != null ? (Integer) retryCountAttr : 0) + 1;
	            
	            if (retryCount > 1) {
	            	throw new GeoreferenceProblem(getThrowableProblem(response));
	            }
	            
	            attributes.setAttribute("retryCount", retryCount, RequestAttributes.SCOPE_REQUEST);
	            attributes.setAttribute("evict", true, RequestAttributes.SCOPE_REQUEST);
    		}
            return new RetryableException(response.status(), "Retrying due to outdated token", 
            		request.httpMethod(), exception, new java.util.Date(), request);
    	}
    	
        throw new GeoreferenceProblem(getThrowableProblem(response));
    }

    private ThrowableProblem getThrowableProblem(Response response) {
        if (StringUtils.isNotBlank(response.body().toString())) {
            log.error(response.body().toString());
        }

        GeoreferenceErrorResponse message = null;
        try (InputStream bodyIs = response.body().asInputStream()) {
            ObjectMapper mapper = new ObjectMapper();
            message = mapper.readValue(bodyIs, GeoreferenceErrorResponse.class);
            
            var problemBuilder = Problem
                .builder()
                .withDetail("Error during Normalize and Georeference address. " + message.getErrorMessage()) 
                .withStatus(Status.valueOf(response.status()))
                .withTitle(TITLE);
            return problemBuilder.build();

        } catch (IOException e) {
            var problemBuilder = Problem
                .builder()
                .withDetail("Error during Normalize and Georeference address. " + e.getMessage()) 
                .withStatus(Status.valueOf(response.status()))
                .withTitle(TITLE);
            return problemBuilder.build();
        }

    }
}