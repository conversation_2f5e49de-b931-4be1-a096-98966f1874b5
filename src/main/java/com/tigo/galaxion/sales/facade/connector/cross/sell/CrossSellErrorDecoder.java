package com.tigo.galaxion.sales.facade.connector.cross.sell;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.config.BaseProblemErrorDecoder;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.problem.CrossSellContactNotFoundProblem;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.problem.CrossSellProblem;
import org.zalando.problem.ThrowableProblem;

public class CrossSellErrorDecoder extends BaseProblemErrorDecoder {

    public CrossSellErrorDecoder(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected String getDefaultTitle() {
        return "cross-sell-service-error";
    }

    @Override
    protected ThrowableProblem buildProblem(ThrowableProblem throwableProblem) {
        if ("contact-not-found".equals(throwableProblem.getTitle())) {
            return new CrossSellContactNotFoundProblem(throwableProblem);
        }
        return new CrossSellProblem(throwableProblem);
    }

}
