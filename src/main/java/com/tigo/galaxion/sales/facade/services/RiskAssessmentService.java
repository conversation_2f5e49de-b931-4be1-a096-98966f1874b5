package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.riskassessment.RiskAssessmentClient;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request.RiskAssessmentRequestBody;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.response.RiskAssessmentResponseBody;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RiskAssessmentService{

    private final RiskAssessmentClient client;

    public RiskAssessmentResponseBody riskValidation(RiskAssessmentRequestBody request) {
        return client.riskValidation(request);
    }

}