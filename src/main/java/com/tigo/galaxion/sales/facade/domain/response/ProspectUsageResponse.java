package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ProspectUsageResponse {

    @ApiModelProperty(value = "The description", example = "Go More 2", required = true)
    private String description;

    @ApiModelProperty(value = "Display order, lowest numbers displayed first", example = "10", required = true)
    private Long displayOrder;
}
