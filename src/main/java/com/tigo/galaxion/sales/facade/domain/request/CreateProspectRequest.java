package com.tigo.galaxion.sales.facade.domain.request;

import com.tigo.galaxion.sales.facade.domain.enumeration.OfferTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CreateProspectRequest {

    @NotBlank
    @ApiModelProperty(value = "The brand of the prospect.", example = "EPIC", required = true)
    private String brand;

    @NotNull
    @ApiModelProperty(value = "The type of offer the prospect was created from.", example = "POSTPAY", required = true)
    private OfferTypeEnum offerType;

    @NotBlank
    @ApiModelProperty(value = "The code of the channel the prospect was created from.", example = "TELESALES", required = true)
    private String channelCode;

}
