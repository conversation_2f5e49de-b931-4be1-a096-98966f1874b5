package com.tigo.galaxion.sales.facade.connector.equipments.domain;

import org.zalando.problem.StatusType;

import com.tigo.galaxion.sales.facade.connector.equipments.domain.enums.AccessType;
import com.tigo.galaxion.sales.facade.connector.equipments.domain.enums.ActivityType;
import com.tigo.galaxion.sales.facade.connector.equipments.domain.enums.CategoryType;
import com.tigo.galaxion.sales.facade.connector.equipments.domain.enums.NatureType;

import lombok.Data;

@Data
public class Equipment {
    private Integer id;
    private String serialNumber;
    private String externalNumber;
    private AccessType accessType;
    private NatureType natureType;
    private Boolean recyclable;
    private String batchNumber;
    private Integer serviceId;
    private Integer warehouseId;
    private Boolean preactivated;
    private StatusType status;
    private String orderId;
    private CategoryType category;
    private ActivityType activity;
    private String activationDate;
    private String assigmentDate;
}
