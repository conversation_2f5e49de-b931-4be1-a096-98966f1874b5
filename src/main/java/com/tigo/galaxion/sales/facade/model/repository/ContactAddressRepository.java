package com.tigo.galaxion.sales.facade.model.repository;

import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import com.tigo.galaxion.sales.facade.model.entity.ContactAddressEntity;
import com.tigo.galaxion.sales.facade.model.entity.ContactAddressId;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ContactAddressRepository extends JpaRepository<ContactAddressEntity, ContactAddressId> {

    Optional<ContactAddressEntity> findByContactAddressId_ReferenceAndContactAddressId_Type(String reference, AddressTypeEnum type);

    List<ContactAddressEntity> findAllByContactAddressId_Reference(String reference);
}
