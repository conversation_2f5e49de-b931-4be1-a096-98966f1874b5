package com.tigo.galaxion.sales.facade.connector.cbs.domain.response;

import java.util.List;

import lombok.Data;

@Data
public class PaymentInfo {
	private String acctKey;
	private String acctCode;
	private String custKey;
	private String subKey;
	private String primaryIdentity;
	private Long transId;
	private String transType;
	private String extTransId;
	private String paymentTime;
	private Long amount;
	private Integer currencyId;
	private Long oriAmount;
	private List<PaymentDetail> paymentDetail;
	private String paymentMethod;
	private String payChannelId;
	private String accessMode;
	private String status;
	private Long operId;
	private Long deptId;
	private List<AdditionalProperty> additionalProperty;
}
