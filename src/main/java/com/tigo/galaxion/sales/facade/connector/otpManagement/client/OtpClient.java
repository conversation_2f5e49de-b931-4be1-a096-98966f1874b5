package com.tigo.galaxion.sales.facade.connector.otpManagement.client;

import com.tigo.galaxion.sales.facade.connector.otpManagement.request.GetTokenRequest;
import com.tigo.galaxion.sales.facade.connector.otpManagement.request.OtpRequest;
import com.tigo.galaxion.sales.facade.connector.otpManagement.request.OtpVerificationRequest;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.GetTokenResponse;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.OtpResponse;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.OtpVerificationResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.net.URI;

@FeignClient(name = "otpClient",url = "${management-otp.service.url}")
public interface OtpClient {

    @Cacheable(value = "otp-token")
    @PostMapping("/token")
    GetTokenResponse getToken(URI baseUrl,
                              @RequestBody GetTokenRequest getTokenRequest);

    @PostMapping(path = "/create", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    OtpResponse createOtpSession(
            @RequestHeader("x-otp-length") String otpLength,
            @RequestHeader("Authorization") String authorization,
            @RequestBody OtpRequest otpRequest
    );

    @PostMapping(path = "/verify", produces = MediaType.APPLICATION_JSON_VALUE)
    OtpVerificationResponse verifyOtpSession(
            @RequestHeader("Authorization") String authorization,
            @RequestBody OtpVerificationRequest otpVerificationRequest
    );
}