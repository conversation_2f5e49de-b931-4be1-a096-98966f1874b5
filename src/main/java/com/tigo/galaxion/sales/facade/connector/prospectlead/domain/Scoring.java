package com.tigo.galaxion.sales.facade.connector.prospectlead.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Scoring {
    @ApiModelProperty(value = "Fraudt Result", required = true, example = "99")
    @JsonProperty("fraudResult")
    private Number fraudResult;

    @ApiModelProperty(value = "Risk Result", required = true, example = "99")
    @JsonProperty("riskResult")
    private Number riskResult;

    @ApiModelProperty(value = "Available Credit", required = true, example = "5000")
    @JsonProperty("availableCredit")
    private Number availableCredit;
}
