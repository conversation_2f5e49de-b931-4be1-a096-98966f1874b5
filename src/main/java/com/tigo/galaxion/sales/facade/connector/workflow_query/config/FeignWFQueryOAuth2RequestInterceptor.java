package com.tigo.galaxion.sales.facade.connector.workflow_query.config;

import feign.RequestTemplate;
import com.tigo.galaxion.sales.facade.connector.config.FeignOAuth2RequestInterceptor;       
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizeRequest;

import java.util.Objects;

import static java.lang.String.format;

public class FeignWFQueryOAuth2RequestInterceptor extends FeignOAuth2RequestInterceptor {
    private final AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager;

    public FeignWFQueryOAuth2RequestInterceptor(AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager) {
        this.authorizedClientManager = authorizedClientManager;
    }

    @Override
    public void apply(final RequestTemplate requestTemplate) {
        if (requestTemplate.path().contains(AUTH_ROUTE_PATTERN)) {
            var token = KeycloakAuthenticationTokenHelper.getTokenString();
            if (token == null) {
                token = getToken();
            }
            requestTemplate.header(DEFAULT_AUTHORIZATION_HEADER, format(DEFAULT_BEARER_TOKEN_PATTERN, token));
        }
    }

    private String getToken() {
        var authorizeRequest = OAuth2AuthorizeRequest.withClientRegistrationId("keycloak")
                                                     .principal("Galaxion Workflow Query Facade Client")
                                                     .build();
        var authorizedClient = authorizedClientManager.authorize(authorizeRequest);
        var accessToken = Objects.requireNonNull(authorizedClient).getAccessToken();

        return accessToken.getTokenValue();
    }
}
