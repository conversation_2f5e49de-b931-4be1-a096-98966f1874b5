package com.tigo.galaxion.sales.facade.connector.riskassessment.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class RiskAssessmentErrorResponseBody {
    private RiskAssessmentErrorResponse error;
}
