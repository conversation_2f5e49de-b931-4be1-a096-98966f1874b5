package com.tigo.galaxion.sales.facade.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.info.BuildProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

@Configuration
public class StartupConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(StartupConfiguration.class);

    private final BuildProperties buildProperties;

    @Value("${server.port}")
    private String serverPort;

    public StartupConfiguration(BuildProperties buildProperties) {
        this.buildProperties = buildProperties;
    }

    @EventListener({ApplicationReadyEvent.class})
    public void doSomethingAfterStartup() {
        if (LOGGER.isInfoEnabled()) {
            String baseUrl = String.format("http://localhost:%s", serverPort);
            LOGGER.info("\n================================================================================\n\n    " +
                        "Microservice: " + String.format("%s:%s", buildProperties.getName(), buildProperties.getVersion()) + "\n\n    " +
                        "Base Url: " + baseUrl + "\n\n    " +
                        "Swagger Url: " + baseUrl + "/swagger-ui/index.html\n\n    " +
                        "Health Url: " + baseUrl + "/actuator/health\n\n" +
                        "================================================================================");
        }

    }
}
