package com.tigo.galaxion.sales.facade.connector.evident.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.GenerationResult;
import com.tigo.galaxion.sales.facade.connector.evident.domain.QuestionnaireData;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class EvidentInitializeResponseBody {
   
    @JsonProperty("questionnaireData")
    private QuestionnaireData questionnaireData; 

    @JsonProperty("generationResult")
    private GenerationResult generationResult;
 
}
