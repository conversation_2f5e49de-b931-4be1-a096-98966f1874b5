//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:48:22 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.process_task_ex;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for MCMVAssets complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="MCMVAssets"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MCAsset" type="{http://crmsaleforce.resourcemanager.millicom.com/unified}MCAsset" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MCMVAssets", propOrder = {
    "mcAsset"
})
public class MCMVAssets {

    @XmlElement(name = "MCAsset", required = true)
    protected List<MCAsset> mcAsset;

    /**
     * Gets the value of the mcAsset property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the mcAsset property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMCAsset().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link MCAsset }
     * 
     * 
     */
    public List<MCAsset> getMCAsset() {
        if (mcAsset == null) {
            mcAsset = new ArrayList<MCAsset>();
        }
        return this.mcAsset;
    }

    public void setMCAsset(List<MCAsset> value) {
        this.mcAsset = value;
    }
}
