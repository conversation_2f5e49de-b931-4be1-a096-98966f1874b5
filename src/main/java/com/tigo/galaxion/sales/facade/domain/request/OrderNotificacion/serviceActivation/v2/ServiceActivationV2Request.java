package com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.v2;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.Valid;

/**
 * Represents a request for service activation v2.
 */
@Getter
@Setter
@ToString
public class ServiceActivationV2Request {

    /**
     * Provisioning request ID.
     */
    @NotNull(message = "Provisioning request ID cannot be null.")
    private String provisiongRequestID;

    /**
     * Channel workorder ID.
     */
    @NotNull(message = "Channel workorder ID cannot be null.")
    private String chanelWorkorderID;

    /**
     * Indicates if it's an asset.
     */
    private boolean isAsset;

    /**
     * Action to perform (A for Activate).
     */
    @NotNull(message = "Action cannot be null.")
    private String action;

    /**
     * Service information.
     */
    @Valid
    @NotNull(message = "Service cannot be null.")
    private Service service;

    /**
     * Equipment external reference ID.
     */
    private String equipmentExternalRefID;

    /**
     * Equipment serial number.
     */
    private String equipmentSerialNumber;

    /**
     * Equipment code.
     */
    private String equipmentCode;

    /**
     * Equipment name.
     */
    private String equipmentName;

    /**
     * Event generated by.
     */
    private String eventGeneratedBy;

    /**
     * Event generated at.
     */
    private String eventGeneratedAt;

    /**
     * Represents the service information.
     */
    @Getter
    @Setter
    @ToString
    public static class Service {

        /**
         * Service external reference ID.
         */
        @NotNull(message = "Service external reference ID cannot be null.")
        private String serviceExternalRefID;

        /**
         * Service name.
         */
        @NotNull(message = "Service name cannot be null.")
        private String serviceName;

        /**
         * Service code.
         */
        @NotNull(message = "Service code cannot be null.")
        private String serviceCode;
    }
}