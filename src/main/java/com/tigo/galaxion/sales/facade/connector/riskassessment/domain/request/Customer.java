package com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.enumeration.IdentificationTypeEnum;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Customer {
    @NotBlank
    @Size(max = 24)
    private String id;

    @NotNull
    private IdentificationTypeEnum typeIdentification;

    @Min(1)
    @Max(6)
    private Integer stratum;

    @NotNull
    private String firstLastName;

    private String firstName;
}
