package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferToCrossSellCartRequest;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.mapper.dto.TigoCartResponseMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.tigo.galaxion.sales.facade.mapper.dto.AddOfferToCartMapper.buildCrossSellAddOfferToCartRequest;

@Service
@RequiredArgsConstructor
public class CrossSellOfferService {

    private final CrossSellClient crossSellClient;
    private final TigoCartResponseMapper tigoCartResponseMapper;
    private final OfferSimDeliveryChoiceService offerSimDeliveryChoiceService;

    public TigoCartResponse addOffer(
            String prospectReference,
            AddOfferToCrossSellCartRequest addOfferToCartRequest) {

        var acquisitionProspectsAddOfferToCartRequest = buildCrossSellAddOfferToCartRequest(addOfferToCartRequest);
        var cartResponse = crossSellClient.addOfferToCart(prospectReference, acquisitionProspectsAddOfferToCartRequest);
        return tigoCartResponseMapper.buildTigoCartResponse(cartResponse);
    }

    @Transactional
    public TigoCartResponse deleteOffer(
            String prospectReference,
            Long offerId) {

        var cartResponse = crossSellClient.deleteOfferToCart(prospectReference, offerId);
        offerSimDeliveryChoiceService.delete(offerId);
        return tigoCartResponseMapper.buildTigoCartResponse(cartResponse);
    }
}
