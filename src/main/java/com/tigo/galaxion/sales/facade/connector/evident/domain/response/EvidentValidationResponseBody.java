package com.tigo.galaxion.sales.facade.connector.evident.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.ExpeditionDate;
import com.tigo.galaxion.sales.facade.connector.evident.domain.Identification;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class EvidentValidationResponseBody {
    
    @JsonProperty("valSurname")
    private String valSurname;

    @JsonProperty("valName")
    private String valName;

    @JsonProperty("valDateExp")
    private String valDateExp;

    @JsonProperty("excludeCustomer")
    private String excludeCustomer;

    @JsonProperty("alerts")
    private String alerts; 

    @JsonProperty("responseAlert")
    private String responseAlert; 

    @JsonProperty("codeAlert")
    private String codeAlert;

    @JsonProperty("result")
    private String result; 

    @JsonProperty("validationRegister")
    private String validationRegister;

    @JsonProperty("numAttemps")
    private String numAttemps;

    @JsonProperty("resultProcess")
    private String resultProcess; 

    @JsonProperty("availableQueries")
    private String availableQueries;

    @JsonProperty("name")
    private String name;

    @JsonProperty("expeditionDate")
    private ExpeditionDate expeditionDate;

    @JsonProperty("identification")
    private Identification identification;

    @JsonProperty("resultDescription")
    private String resultDescription;


}
