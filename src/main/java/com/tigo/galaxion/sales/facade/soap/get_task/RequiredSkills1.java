
package com.tigo.galaxion.sales.facade.soap.get_task;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for RequiredSkills1 complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="RequiredSkills1"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="TaskRequiredSkill1" type="{http://crmsaleforce.resourcemanager.millicom.com/gettasksoap}TaskRequiredSkill1"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RequiredSkills1", propOrder = {
    "taskRequiredSkill1"
})
public class RequiredSkills1 {

    @XmlElement(name = "TaskRequiredSkill1", required = true)
    protected TaskRequiredSkill1 taskRequiredSkill1;

    /**
     * Gets the value of the taskRequiredSkill1 property.
     * 
     * @return
     *     possible object is
     *     {@link TaskRequiredSkill1 }
     *     
     */
    public TaskRequiredSkill1 getTaskRequiredSkill1() {
        return taskRequiredSkill1;
    }

    /**
     * Sets the value of the taskRequiredSkill1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link TaskRequiredSkill1 }
     *     
     */
    public void setTaskRequiredSkill1(TaskRequiredSkill1 value) {
        this.taskRequiredSkill1 = value;
    }

}
