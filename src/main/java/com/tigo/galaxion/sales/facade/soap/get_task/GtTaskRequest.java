
package com.tigo.galaxion.sales.facade.soap.get_task;

import javax.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="GtTask" type="{http://crmsaleforce.resourcemanager.millicom.com/gettasksoap}Task"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "gtTask"
})
@XmlRootElement(name = "GtTaskRequest")
public class GtTaskRequest {

    @XmlElement(name = "GtTask", required = true)
    protected Task gtTask;

    /**
     * Gets the value of the gtTask property.
     * 
     * @return
     *     possible object is
     *     {@link Task }
     *     
     */
    public Task getGtTask() {
        return gtTask;
    }

    /**
     * Sets the value of the gtTask property.
     * 
     * @param value
     *     allowed object is
     *     {@link Task }
     *     
     */
    public void setGtTask(Task value) {
        this.gtTask = value;
    }

}
