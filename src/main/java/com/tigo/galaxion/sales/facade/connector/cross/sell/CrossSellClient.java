package com.tigo.galaxion.sales.facade.connector.cross.sell;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.config.PrivateAuthFeignClientConfiguration;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellAddOfferToCartRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellCreateCrossSellRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellEligibilityRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellOfferSearchRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.PatchCrossSellRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellEligibilityResponse;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellOfferTypeEligibilityResponse;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellResponse;
import com.tigo.galaxion.sales.facade.domain.response.OffersAndConditionalDiscountsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "cross-sell-service",
        url = "${environment.url.cross-sell-service}",
        configuration = {CrossSellErrorDecoder.class})
public interface CrossSellClient {

    @GetMapping("/api/v1/accounts/{account_id}/offer-types/eligibilities")
    List<CrossSellOfferTypeEligibilityResponse> getOfferTypesEligibility(@PathVariable("account_id") String accountId);

    @GetMapping("/api/v1/accounts/{account_id}/service-groups/eligibilities")
    List<CrossSellEligibilityResponse> getServiceGroupsEligibility(@PathVariable("account_id") String accountId,
                                                                   @SpringQueryMap CrossSellEligibilityRequest crossSellEligibilityRequest);

    @GetMapping("/api/v1/accounts/{account_id}/service-groups/eligibilities")
    List<CrossSellEligibilityResponse> getServiceGroupsEligibility(@PathVariable("account_id") String accountId);

    @PostMapping("/api/v1/cross-sells")
    String createCrossSell(@RequestBody CrossSellCreateCrossSellRequest request);

    @GetMapping("/api/v1/cross-sells/{cross_sell_reference}/carts")
    CartResponse getCrossSellCart(@PathVariable("cross_sell_reference") String crossSellReference);

    @GetMapping("/api/v1/cross-sells/{cross_sell_reference}/offers")
    OffersAndConditionalDiscountsResponse getOffers(@PathVariable("cross_sell_reference") String crossSellReference,
                                                    @SpringQueryMap CrossSellOfferSearchRequest offerSearchRequest);

    @PostMapping("/api/v1/cross-sells/{cross_sell_reference}/carts/offers")
    CartResponse addOfferToCart(@PathVariable("cross_sell_reference") String crossSellReference,
                                @RequestBody CrossSellAddOfferToCartRequest request);

    @DeleteMapping("/api/v1/cross-sells/{cross_sell_reference}/carts/offers/{cart_offer_id}")
    CartResponse deleteOfferToCart(@PathVariable("cross_sell_reference") String crossSellReference,
                                   @PathVariable("cart_offer_id") Long offerId);

    @GetMapping("/api/v1/cross-sells/{cross_sell_reference}")
    CrossSellResponse getCrossSell(@PathVariable("cross_sell_reference") String crossSellReference);

    @PatchMapping(value = "/api/v1/cross-sells/{cross_sell_reference}", consumes = "application/merge-patch+json")
    CrossSellResponse updateCrossSell(@PathVariable("cross_sell_reference") String crossSellReference,
                                      @RequestBody PatchCrossSellRequest request);
}
