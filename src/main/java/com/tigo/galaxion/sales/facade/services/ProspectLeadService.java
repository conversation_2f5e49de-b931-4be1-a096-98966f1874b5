package com.tigo.galaxion.sales.facade.services;

import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.request.ProspectLeadRequest;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectCustomersResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponseByEntity;

import com.tigo.galaxion.sales.facade.connector.prospectlead.ProspectLeadClient;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import com.tigo.galaxion.sales.facade.mapper.dto.AddressRequestMapper;

@Service
@Getter
@Setter
@RequiredArgsConstructor
public class ProspectLeadService {

    private final ProspectLeadClient client;
    private final AddressService addressService;

    public ProspectLeadResponse getAllProspectInfo(String prospectId) {
        return client.getAllProspectEntities(prospectId);
    }

    public ProspectLeadResponseByEntity getProspectInfoByEntityType(String prospectId, String entity,
            Integer entityType) {
        return client.getProspectEntityByEntityType(prospectId, entity, entityType);
    }

    public ProspectCustomersResponse getProspectCustomersInfo(String documentType, String documentId) {
        return client.getProspectLeadCustomer( documentType,  documentId);
    }

    public void updateAllProspectInfo(String prospectId, ProspectLeadRequest prospectLeadRequest) {
        client.updateAllProspectEntities(prospectId, prospectLeadRequest);
    }

    public void updateProspectInfoByEntity(String prospectId, String entity,
            JsonNode request) {

        if (entity.equals("address")) {
            JsonNode addressType = request.get("addressType");
            int addressTypeString = addressType.asInt();
            if (addressTypeString == 0) {
                var res = addressService.createAddress(AddressRequestMapper.builAddressMSResponseForJsonNode(request));
                ((ObjectNode) request).put("coreInstallationAddressId", res);
            }
        }
        client.updateProspectEntyByEntityType(prospectId, entity, request);
    }
}