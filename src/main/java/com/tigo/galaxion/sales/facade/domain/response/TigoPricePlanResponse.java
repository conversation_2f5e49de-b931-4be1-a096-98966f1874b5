package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
public class TigoPricePlanResponse {

    @ApiModelProperty(value = "price plan catalog id", example = "ASDFGH", required = true)
    private String catalogCode;

    @ApiModelProperty(value = "The amount to be paid vat included", required = true)
    private Long amountVatIncluded;

    @ApiModelProperty(value = "The amount to be paid vat excluded", required = true)
    private Long amountVatExcluded;
}
