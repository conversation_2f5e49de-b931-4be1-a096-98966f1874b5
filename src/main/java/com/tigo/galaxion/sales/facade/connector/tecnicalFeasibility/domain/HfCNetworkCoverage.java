package com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HfCNetworkCoverage {
    private String nodeCode;
    private String nodeType;
    private String digitalTV;
    private String toipCapacity;
    private String priority;
    private String coverage;
}
