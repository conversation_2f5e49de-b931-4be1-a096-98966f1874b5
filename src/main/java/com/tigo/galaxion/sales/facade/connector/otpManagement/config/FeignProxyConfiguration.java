package com.tigo.galaxion.sales.facade.connector.otpManagement.config;

import org.apache.http.HttpHost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import feign.Client;
import feign.httpclient.ApacheHttpClient;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FeignProxyConfiguration {

    @Value("${management-otp.proxy.use-proxy}")
    private Boolean useProxy;

    @Value("${management-otp.proxy.host}")
    private String proxyHost;

    @Value("${management-otp.proxy.port}")
    private Integer proxyPort;

    @Bean
    public Client OTPfeignClient() {
        log.info("OTP-useProxy: {}", useProxy);
        HttpClientBuilder builder = HttpClientBuilder.create();
        if (useProxy) {
            HttpHost proxy = new HttpHost(proxyHost, proxyPort);
            builder.setProxy(proxy);
        }
        CloseableHttpClient httpClient = builder.build();

        return new ApacheHttpClient(httpClient);
    }
}
