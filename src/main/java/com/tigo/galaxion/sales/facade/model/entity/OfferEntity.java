package com.tigo.galaxion.sales.facade.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "offer")
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OfferEntity {

    @Id
    @Column(name = "offer_id", nullable = false)
    private Long offerId;

    @Column(name = "sim_delivery_type")
    private String simDeliveryType;

    @Column(name = "msisdn_type")
    private String msisdnType;
}
