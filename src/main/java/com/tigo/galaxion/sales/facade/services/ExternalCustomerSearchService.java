package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.customer.search.ExternalCustomerSearchClient;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomer;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomerFullDetail;
import com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum;
import com.tigo.galaxion.sales.facade.domain.problem.ExternalCustomerExpeditionDateProblem;
import com.tigo.galaxion.sales.facade.domain.problem.ExternalCustomerSearchNotFoundProblem;
import com.tigo.galaxion.sales.facade.mapper.dto.ExternalCustomerSearchResponseMapper;

import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ExternalCustomerSearchService {

    private final ExternalCustomerSearchClient externalCustomerSearchClient;

    public ExternalCustomer searchCustomerByExternalId(String idType, String idNumber, String expeditionDate)
            throws Exception {
        ExternalCustomerFullDetail customerComplete;
        try {
            customerComplete = externalCustomerSearchClient
                    .searchCustomerByExternalId(idNumber, idType);
            if (customerComplete == null || customerComplete.basicInformation() == null) {
                throw new ExternalCustomerSearchNotFoundProblem();
            }

        } catch (Exception e) {
            throw new ExternalCustomerSearchNotFoundProblem();
        }

        ExternalCustomer customer = ExternalCustomerSearchResponseMapper.buildExternalCustomer(customerComplete);

        if (!idType.equals(IdDocumentTypeEnum.NIT.name())) {
            LocalDate customerExpDate = OffsetDateTime.parse(customer.expeditionDate()).toLocalDate();
            LocalDate sendExpDate = LocalDate.parse(expeditionDate);

            if (customerExpDate.equals(sendExpDate)) {
                return customer;
            }
            throw new ExternalCustomerExpeditionDateProblem();
        }

        return customer;
    }
}
