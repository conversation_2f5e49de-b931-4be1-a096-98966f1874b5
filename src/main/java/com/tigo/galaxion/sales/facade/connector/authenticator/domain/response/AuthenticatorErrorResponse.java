package com.tigo.galaxion.sales.facade.connector.authenticator.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class AuthenticatorErrorResponse {
	
	@Valid
    @JsonProperty("backendErrorCode")
    private AuthenticatorBackendError backendErrorCode;

}
