package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class FailedToRenewAccessTokenProblem extends AbstractThrowableProblem {

    public FailedToRenewAccessTokenProblem() {
        super(null, "failed-to-renew-access-token", Status.INTERNAL_SERVER_ERROR, "Internal error renewing access token");
    }

    public FailedToRenewAccessTokenProblem(String title, String detail, Status status) {
        super(null, title, status, detail);
    }

}
