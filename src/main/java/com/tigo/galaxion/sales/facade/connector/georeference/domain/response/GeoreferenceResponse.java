package com.tigo.galaxion.sales.facade.connector.georeference.domain.response;

import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
public class GeoreferenceResponse {
    @Size(max=320)
    @JsonProperty("naturalAddress")
    private String naturalAddress;
    @Valid
    @JsonProperty("gisCommonInfoDir")
    GisCommonInfoDir gisCommonInfoDir;
}


