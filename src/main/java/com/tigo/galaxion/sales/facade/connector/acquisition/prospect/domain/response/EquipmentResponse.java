package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@NoArgsConstructor
@SuperBuilder
@AllArgsConstructor
@Getter
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        property = "type", visible = true, defaultImpl = EquipmentResponse.class)
@JsonSubTypes({
        @JsonSubTypes.Type(value = HandsetResponse.class, name = EquipmentTypeEnum.Constant.HANDSET),
        @JsonSubTypes.Type(value = MobileBroadbandModemResponse.class, name = EquipmentTypeEnum.Constant.MOBILE_BROADBAND_MODEM),
        @JsonSubTypes.Type(value = AccessoryEquipmentResponse.class, name = EquipmentTypeEnum.Constant.ACCESSORY),
})
public class EquipmentResponse {

    private Long id;

    private String catalogCode;

    private String imei;

    private AmountResponse amountVatIncluded;

    private AmountResponse amountVatExcluded;

    private String color;

    private String colorCode;

    private String description;

    private String manufacturer;

    private String model;

    private String inventoryCode;

    private ChargeResponse charge;

    private AddOnResponse addon;

    private EquipmentTypeEnum type;

    private SubsidyResponse subsidy;

    private boolean subsidyApplied;

    private EquipmentFinancingResponse equipmentFinancing;

}
