package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.DirectoryPreferenceEnum;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.equipments.InclusiveEquipmentResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
public class OfferResponse {

    private Long id;

    private BaseOfferResponse baseOffer;

    private AmountResponse amountVatIncluded;

    private AmountResponse amountVatExcluded;

    @Builder.Default
    private List<AddOnResponse> addOns = new ArrayList<>();

    @Builder.Default
    private List<DiscountResponse> discounts = new ArrayList<>();

    private SimCardResponse simCard;

    private Long topUpAmount;

    private String number;

    private DirectoryPreferenceEnum directoryPreference;

    private Boolean applyDeposit;

    private Long depositAmountVatIncluded;

    private Long depositAmountVatExcluded;

    private Long depositTerm;

    @Builder.Default
    private List<EquipmentResponse> equipments = new ArrayList<>();

    @Builder.Default
    private List<InclusiveEquipmentResponse> inclusiveEquipments = new ArrayList<>();

    private AmountResponse activationFeeVatIncluded;

    private AmountResponse activationFeeVatExcluded;

    private Long parentOfferId;

    private Long parentSubscriptionId;

    private PortInResponse portInInfo;

    private Long installationAddressId;

    private LocalDate activationAt;

    private LocalDate subscriberBirthDate;

    @Builder.Default
    private List<RecurringAmountByOccurrenceResponse> recurringAmountsByOccurrence = new ArrayList<>();
}
