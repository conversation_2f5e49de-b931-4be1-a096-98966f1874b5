package com.tigo.galaxion.sales.facade.connector.notification;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tigo.galaxion.sales.facade.connector.notification.domain.request.CifratorDataRequest;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.CifratorDataResponse;


@FeignClient(value = "tigo-notifications-cifrator-service", url = "${environment.url.tigo-notifications-cifrator-service}")
public interface NotificationCifratorClient {
    @PostMapping(value = "/fetapi/cifrado", consumes = MediaType.APPLICATION_JSON_VALUE)
    CifratorDataResponse getUrl(@RequestBody CifratorDataRequest cifratorDataRequest);
}
