package com.tigo.galaxion.sales.facade.connector.workflow_engine;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.tigo.galaxion.sales.facade.connector.config.PrivateAuthFeignClientConfiguration;

import mc.monacotelecom.workflow.app.dto.VariableUpdateDTO;
import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;
import mc.monacotelecom.workflow.base.enumeration.ProcessType;
import mc.monacotelecom.workflow.order.dto.CancelOrderRequestDTO;

@FeignClient(value = "WorkflowEngineClient", url = "${environment.url.workflow-engine}", path = "/private/auth", configuration = PrivateAuthFeignClientConfiguration.class)
public interface WorkflowEngineClient {

    @PostMapping("/process/signal")
    void processSignal(@RequestBody WorkflowSignalRequestDTO request);

    @PostMapping("/process/signal/order")
    void sendSignal(@RequestBody WorkflowSignalRequestDTO request);

    @PutMapping("/orders/{orderId}")
    void updateConnectorData(@PathVariable("orderId") String orderId, @RequestBody VariableUpdateDTO request);

    @GetMapping("/orders/{orderId}")
    Map<String, Object> getConnectorData(@PathVariable("orderId") String orderId,
            @RequestParam("type") ProcessType typeDataOrder,
            @RequestParam(value = "signalName", required = false) String signalName,
            @RequestParam(value = "names", required = false) List<String> varNames);

    @PostMapping("/orders/cancel")
    void cancelOrder(@RequestBody CancelOrderRequestDTO request);


   
    
}
