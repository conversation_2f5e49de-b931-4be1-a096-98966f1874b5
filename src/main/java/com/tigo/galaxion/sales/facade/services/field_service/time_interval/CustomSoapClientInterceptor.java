package com.tigo.galaxion.sales.facade.services.field_service.time_interval;

import org.springframework.ws.client.WebServiceClientException;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.context.MessageContext;

public class CustomSoapClientInterceptor implements ClientInterceptor {

    @Override
    public boolean handleResponse(MessageContext messageContext) {
        // Aquí puedes validar y manejar la respuesta
        return true;
    }

    @Override
    public boolean handleRequest(MessageContext messageContext) {
        // Procesar la solicitud si es necesario
        return true;
    }

    @Override
    public boolean handleFault(MessageContext messageContext) {
        // Manejar los errores SOAP
        System.err.println("SOAP Fault occurred!");
        return true;
    }

    @Override
    public void afterCompletion(MessageContext messageContext, Exception ex) throws WebServiceClientException {
        // Manejar la excepción después de la llamada
    }
}
