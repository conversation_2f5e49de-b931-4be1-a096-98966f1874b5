package com.tigo.galaxion.sales.facade.model.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.tigo.galaxion.sales.facade.model.entity.CounterEntity;

import java.util.Optional;


public interface CounterRepository extends JpaRepository<CounterEntity, Integer> {

    Optional<CounterEntity> findByName(String name);;

    @Modifying
    @Transactional
    @Query("update CounterEntity c set c.value = :value where c.id = :id")
    void updateValueById(Integer id, Long value);
    
}