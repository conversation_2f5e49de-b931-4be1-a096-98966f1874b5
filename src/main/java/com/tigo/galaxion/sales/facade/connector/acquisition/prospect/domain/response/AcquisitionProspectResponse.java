package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import com.tigo.galaxion.sales.facade.domain.enumeration.OfferTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AcquisitionProspectResponse {

    private String reference;

    private String status;

    private String channelCode;

    private String channelGroup;

    private String brand;

    private OfferTypeEnum offerType;

    private String customerType;

    private String cartUuid;

    private String creditScore;

    private Long contractFileId;

    private String contractFileName;

    private String csrAgentEmail;

    private PaymentSettingsResponse paymentSettings;

    private Boolean isBlacklisted;

    private String contractSignatureOption;

}
