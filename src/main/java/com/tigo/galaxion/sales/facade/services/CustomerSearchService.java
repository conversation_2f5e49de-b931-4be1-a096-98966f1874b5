package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.domain.problem.CustomerIdentityDocumentNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.request.CustomerSearchRequest;
import com.tigo.galaxion.sales.facade.domain.response.CustomerSearchResponse;
import com.tigo.galaxion.sales.facade.model.repository.AccountContactViewRepository;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CustomerSearchService {
    private final AccountContactViewRepository customerRepo;

    public CustomerSearchResponse getCustomerSearch(CustomerSearchRequest customerSearchRequest) {

        var identityDocument = customerRepo.findByIdentityDocumentIdentifierAndIdentityDocumentType(
                customerSearchRequest.getIdentifier(),
                customerSearchRequest.getType());
        if (identityDocument.size() > 0) {
            var customer = identityDocument.get(0);

            return CustomerSearchResponse.builder()
                    .type(customer.getIdentityDocumentType())
                    .identifier(customer.getIdentityDocumentIdentifier())
                    .expiration_date(customer.getIdentityDocumentExpirationDate())
                    .first_name(customer.getContactFirstName())
                    .last_name(customer.getContactLastName())
                    .email(customer.getContactMainEmail())
                    .phone_number(customer.getContactMobilePhoneNumber())
                    .account_id(customer.getAccountId())
                    .build();
        } else {
            throw new CustomerIdentityDocumentNotFoundProblem(customerSearchRequest.getIdentifier(),
                    customerSearchRequest.getType());
        }
    }

    public List<CustomerSearchResponse> getCustomerSearchList(CustomerSearchRequest customerSearchRequest) {
        var identityDocuments = customerRepo.findByIdentityDocumentIdentifierAndIdentityDocumentType(
                customerSearchRequest.getIdentifier(),
                customerSearchRequest.getType());
        if (identityDocuments.size() > 0) {
            return identityDocuments.stream().map(customer -> CustomerSearchResponse.builder()
                    .type(customer.getIdentityDocumentType())
                    .identifier(customer.getIdentityDocumentIdentifier())
                    .expiration_date(customer.getIdentityDocumentExpirationDate())
                    .first_name(customer.getContactFirstName())
                    .last_name(customer.getContactLastName())
                    .email(customer.getContactMainEmail())
                    .phone_number(customer.getContactMobilePhoneNumber())
                    .account_id(customer.getAccountId())
                    .build()).collect(Collectors.toList());
        } else {
            throw new CustomerIdentityDocumentNotFoundProblem(customerSearchRequest.getIdentifier(),
                    customerSearchRequest.getType());
        }
    }

}