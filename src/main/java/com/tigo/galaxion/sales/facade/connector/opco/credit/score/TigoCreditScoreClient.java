package com.tigo.galaxion.sales.facade.connector.opco.credit.score;

import com.tigo.galaxion.sales.facade.connector.config.PrivateAuthFeignClientConfiguration;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.request.TigoCreditScoreRequest;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.response.TigoCreditScoreResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "tigo-credit-scores-service",
             url = "${environment.url.tigo-credit-scores-service}",
             configuration = {TigoCreditScoreErrorDecoder.class})
public interface TigoCreditScoreClient {

    @PostMapping("/score")
    TigoCreditScoreResponse score(@RequestBody TigoCreditScoreRequest request);
}
