package com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request;

import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.enumeration.TransactionTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class RiskAssessmentRequestBody {
  @ApiModelProperty(value = "a trace ID")
  private String traceID;

  @NotNull
  @ApiModelProperty(value = "Transaction Type", required = true)
  private TransactionTypeEnum transactionType;

  @ApiModelProperty(value = "Municipality Code")
  private String municipalityCode;

  @ApiModelProperty(value = "Department Code")
  private String departmentCode;

  @NotNull
  @Valid
  @ApiModelProperty(value = "Customer", required = true)
  private Customer customer;

  @ApiModelProperty(value = "Offers")
  @Valid
  private Offer[] offers;

  @Valid
  @ApiModelProperty(value = "Some properties")
  private PropertyObject[] properties;
}
