package com.tigo.galaxion.sales.facade.domain.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Builder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PaymentInformationRequest {
    @NotBlank
    @ApiModelProperty(value = "Order Identification.", required = false, example = "ORDER123")
    @Size(max = 10)
    @JsonProperty("orderId")
    private String orderId;
    
    @NotNull
    @ApiModelProperty(value = "Client Identification.", required = true, example = "123456")
    @JsonProperty("clientId")
    private Long clientId;
    
    @NotNull
    @ApiModelProperty(value = "Account Identification.", required = true, example = "654321")
    @JsonProperty("accountId")
    private Long accountId;
    
    @NotNull
    @ApiModelProperty(value = "Amount paid.", required = true, example = "100")
    @JsonProperty("payedAmount")
    private Long payedAmount;
    
    @NotNull
    @ApiModelProperty(value = "Balance after payment.", required = true, example = "400")
    @JsonProperty("balance")
    private Long balance;
    
    @NotBlank
    @ApiModelProperty(value = "Payment Date.", required = true, example = "2024-08-27T19:05:12")
    @Size(max = 60)
    @JsonProperty("paymentDate")
    private String paymentDate;

    @NotBlank
    @ApiModelProperty(value = "External ID used by Panamá (event_ref_id in the ar.balance table).", required = false)
    @Size(max = 60)
    @JsonProperty("externalId")
    private String externalId;
}
