package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.connector.otpManagement.request.OtpRequest;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.OtpResponse;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.OtpVerificationResponse;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.SendCodeResponse;
import com.tigo.galaxion.sales.facade.services.OtpManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Api
@RestController
@RequiredArgsConstructor
public class OtpManagementController {

    private final OtpManagementService otpManagementService;

    @PostMapping(path = "/api/v1/otp-management/send", produces = MediaType.APPLICATION_JSON_VALUE + "; charset=utf-8")
    @ApiOperation("Send code OTP")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Code send"),
    })
    public OtpResponse sendCode(@RequestBody OtpRequest otpRequest) throws Exception {
        return otpManagementService.sendCodeOtp(otpRequest);
    }

    @PostMapping(path = "/api/v1/otp-management/validate/otp/{otp}/code/{uuid}", produces = MediaType.APPLICATION_JSON_VALUE + "; charset=utf-8")
    @ApiOperation("Validate code OTP")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Code Valid"),
            @ApiResponse(code = 400, message = "Invalid Code"),
    })
    public OtpVerificationResponse validateCode(@PathVariable("otp") String otp, @PathVariable("uuid") String uuid) throws Exception {
        return otpManagementService.validateCode(otp, uuid);
    }
}