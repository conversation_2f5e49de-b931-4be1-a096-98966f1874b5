package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class OwnerContactIdentityDocumentNotFoundProblem extends AbstractThrowableProblem {

    public OwnerContactIdentityDocumentNotFoundProblem(String uuid) {
        super(null,
              "owner-contact-identity-document-not-found",
              Status.NOT_FOUND,
              String.format("The owner contact identity document with contact uuid %s not found.", uuid));
    }

}
