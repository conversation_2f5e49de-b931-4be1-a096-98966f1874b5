package com.tigo.galaxion.sales.facade.services.field_service.process_task_ex;

import java.text.ParseException;
import javax.xml.datatype.DatatypeConfigurationException;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointment;
import com.tigo.galaxion.sales.facade.mapper.dto.FieldServiceConfirmAppointmentMapper;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.ProcessTaskExRequest;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.ProcessTaskExResponse;

@Service 
public class ProcessTaskExClient extends WebServiceGatewaySupport {

  public ProcessTaskExResponse processTaskEx(FieldServiceConfirmAppointment data)
    throws DatatypeConfigurationException, ParseException {
    ProcessTaskExRequest request = FieldServiceConfirmAppointmentMapper.buildProcessTaskExRequest(data);

    return (ProcessTaskExResponse) getWebServiceTemplate().marshalSendAndReceive(request);
  }
}
