package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.connector.coverage.domain.request.CoverageRequest;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.response.CoverageResponse;
import com.tigo.galaxion.sales.facade.services.CoverageService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/coverage")
@RequiredArgsConstructor
public class CoverageController {

    private final CoverageService coverageService;

    @PostMapping
    public ResponseEntity<CoverageResponse> getCoverage(@RequestBody CoverageRequest request) {
        CoverageResponse response = coverageService.getCoverage(
                request.getIdBarrio(), request.getIdCalle(), request.getIdCasa()
        );
        return ResponseEntity.ok(response);
    }
}