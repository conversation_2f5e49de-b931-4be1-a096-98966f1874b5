package com.tigo.galaxion.sales.facade.connector.alfresco;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.NoSuchElementException;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Address;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetadataFormato9 {

    private static final String ID_FORMATO = "9";
    private static final String ACEPTA_CONSULTA = "SI";

    private String idFormato;
    private String mail;
    private String fecha;
    private String departamento1;
    private String mes;
    private String dia;
    private String ano;
    private String aceptaConsulta;
    private String ciudad;
    private String nombres;
    private String tipoDocumento;
    private String numeroDocumento;
    private String direccion;
    private String telefono;
    private String firma;
    private String noAceptaConsulta;
    private String ciudad1;

    public MetadataFormato9(ProspectLeadResponse p) {

        LocalDate fechaActual = LocalDate.now();

        List<Address> filteredAddresses = new ArrayList<>();

        for (Address address : p.getAddress()) {
            if (address.getAddressType() == 1) {
                filteredAddresses.add(address);
            }
        }

        if (filteredAddresses.size() > 1) {
            throw new IllegalArgumentException("Filtered addresses contain more than one addressType=1");
        }

        if (filteredAddresses.size() == 0) {
            throw new NoSuchElementException("Filtered addresses doesn't contain any addressType=1");
        }

        Address direccionInstalacion = filteredAddresses.get(0);

        setIdFormato(ID_FORMATO);
        setMail(p.getCustomer().getContactEmail());
        setFecha(fechaActual.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        setDepartamento1(direccionInstalacion.getDepartment());
        setMes(fechaActual.format(DateTimeFormatter.ofPattern("MMMM",new Locale("es", "CO"))));
        setDia(fechaActual.format(DateTimeFormatter.ofPattern("dd")));
        setAno(fechaActual.format(DateTimeFormatter.ofPattern("yyyy")));
        setCiudad(direccionInstalacion.getMunicipality());
        setNombres(p.getCustomer().getNames().concat(" ").concat(p.getCustomer().getLastName()));

        setAceptaConsulta(ACEPTA_CONSULTA);
        setTipoDocumento(this.getTypeDocument(p.getCustomer().getDocumentType()));
        setNumeroDocumento(p.getCustomer().getDocumentId());
        setDireccion(direccionInstalacion.getAddress());
        setTelefono(p.getCustomer().getContactPhone());
        setFirma(p.getCustomer().getCallId());
        setNoAceptaConsulta("N/A");        
        setCiudad1(direccionInstalacion.getMunicipality());
    }

    public String toMetadataString() {
        //String idFormato9 = "{\"idFormato\":\"9\",\"mail\":\"<EMAIL>\",\"fecha\":\"14/03/2023\",\"departamento1\":\"Distrito Capital\",\"mes\":\"marzo\",\"dia\":\"14\",\"ano\":\"2023\",\"aceptaConsulta\":\"SI\",\"ciudad\":\"Bogotá D.C\",\"nombres\":\"Nicolas Wagner\",\"tipoDocumento\":\"Cédula de Ciudadanía\",\"numeroDocumento\":\"45000014\",\"direccion\":\"Calle 123 # 4 56\",\"telefono\":\"300120000\",\"firma\":\"https://upload.wikimedia.org/wikipedia/commons/8/8b/Firma_de_Harold.jpg\",\"noAceptaConsulta\":\"N/A\",\"ciudad1\":\"Bogotá D.C\"}";

        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.valueToTree(this);
        
        return jsonNode.toString();

    }

    private String getTypeDocument(String documentType){
        String typeDocument="";
        switch (documentType){
            case "CC":
            case "Cédula de Ciudadanía":
                typeDocument = "Cédula de Ciudadanía";
                break;
            case "CE":
            case "Cédula de Extranjería":
                typeDocument = "Cédula de Extranjería";
                break;
            case "Numero de Identificacion Tributaria":
            case "NIT":
                typeDocument = "NIT";
                break;
            case "Pasaporte":
                typeDocument = documentType;
                break;
            default:
                typeDocument = "Do not exists this Document Type";
        }
        return typeDocument;
    }

}
