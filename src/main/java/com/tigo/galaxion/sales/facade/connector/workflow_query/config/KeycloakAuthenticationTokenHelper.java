package com.tigo.galaxion.sales.facade.connector.workflow_query.config;

import org.keycloak.adapters.RefreshableKeycloakSecurityContext;
import org.keycloak.adapters.springsecurity.token.KeycloakAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

public final class KeycloakAuthenticationTokenHelper {

    private KeycloakAuthenticationTokenHelper() {
        // Prevent instantiation.
    }

    /**
     * Get the full JSON Web Token of the current authenticated user.
     *
     * @return JWT as a String.
     */
    public static String getTokenString() {
        var keycloakAuthenticationToken = getKeycloakAuthenticationToken();
        if (keycloakAuthenticationToken == null) return null;
        var refreshableKeycloakSecurityContext = (RefreshableKeycloakSecurityContext) keycloakAuthenticationToken.getCredentials();
        return refreshableKeycloakSecurityContext.getTokenString();
    }

    private static KeycloakAuthenticationToken getKeycloakAuthenticationToken() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth instanceof KeycloakAuthenticationToken) {
            return (KeycloakAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        }
        return null;
    }
}

