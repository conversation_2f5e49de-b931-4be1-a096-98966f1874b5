package com.tigo.galaxion.sales.facade.domain.response;

import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@Getter
@ToString
@Builder
@NoArgsConstructor
public class AddressNormalizeGisCommonInfoDir {
  @ApiModelProperty(value = "Address Code", required = true, example = "1431065734")
  @JsonProperty("addressCode")
  private String addressCode;
  @ApiModelProperty(value = "Normalized Address", required = true, example = "KR 43 A # 53 D - 46 SUR IN 1609")
  @Size(max = 320)
  @JsonProperty("normalizedAddress")
  private String normalizedAddress;
  @ApiModelProperty(value = "Latitude", required = true, example = "6.157284259796143")
  @JsonProperty("latitude")
  private String latitude;
  @ApiModelProperty(value = "Longitude", required = true, example = "-75.60497283935547")
  @JsonProperty("longitude")
  private String longitude;
  @ApiModelProperty(value = "Stratum", required = true, example = "2")
  @Size(min = 1, max = 2)
  @JsonProperty("stratum")
  private String stratum;
  @ApiModelProperty(value = "Rural", required = true, example = "NO")
  @JsonProperty("rural")
  private String rural;
  @ApiModelProperty(value = "Georeferencing Status", required = true, example = "M")
  @JsonProperty("georeferencingStatus")
  private String georeferencingStatus;
  @ApiModelProperty(value = "Provider Address Code", required = true, example = "670000000SKR043053000000AD000460000000000000000IN  1609000000000000000000000000")
  @JsonProperty("providerAddressCode")
  private String providerAddressCode;
  @ApiModelProperty(value = "Country Code", required = true, example = "57")
  @JsonProperty("countryCode")
  private Number countryCode;
  @ApiModelProperty(value = "Department Code", required = true, example = "5")
  @Size(max = 6)
  @JsonProperty("departmentCode")
  private String departmentCode;
  @ApiModelProperty(value = "Municipality Code", required = true, example = "5631000")
  @Size(max = 6)
  @JsonProperty("municipalityCode")
  private String municipalityCode;
  @ApiModelProperty(value = "Neighborhood Code", required = true, example = "6")
  @Size(max = 6)
  @JsonProperty("neighborhoodCode")
  private String neighborhoodCode;
  @ApiModelProperty(value = "Dane Block Code", required = true, example = "5631100000601")
  @Size(max = 320)
  @JsonProperty("daneBlockCode")
  private Long daneBlockCode;
  @ApiModelProperty(value = "predial Code", required = true, example = "5631100000601089")
  @JsonProperty("predialCode")
  private Long predialCode;
  @ApiModelProperty(value = "Neighborhood Name", required = true, example = "Los Arias")
  @JsonProperty("neighborhoodName")
  private String neighborhoodName;
  @ApiModelProperty(value = "Micro Zone", required = false, example = "CALDAS")
  @JsonProperty("microzone")
  private String microzone;
  @ApiModelProperty(value = "Gis code * + * normalized address * +  * Urbana|Rural * Natural address * + neighborhoodName", required = false, example = "1431065734 * KR 43 A # 53 D - 46 SUR IN 1609 * Rural *  CR 43 A  # 53 D SUR - 46, INTERIOR 1609 Los Arias")
  @JsonProperty("descriptionAddress")
  private String descriptionAddress;
}
