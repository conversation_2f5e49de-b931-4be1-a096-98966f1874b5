package com.tigo.galaxion.sales.facade.connector.riskassessment.domain.enumeration;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum TransactionTypeEnum{

    ACTIVATION("Activation"), 
    CHANGE_PLAN("ChangePlan"),
    CHANGE_SUB_OWNERSHIP("ChangeSubOwnerShip");

    private final String name;

    TransactionTypeEnum(String name) {
        this.name = name;
    }

    @JsonValue
    public String getName() {
        return name;
    }

    @JsonCreator
    public static TransactionTypeEnum fromValue(String value) {
        for ( TransactionTypeEnum t : values()) {
            String c = t.getName();
            if (c.equals(value)) {
                return t;
            }
        }
        // Return a response entity with a 400 Bad Request status
       throw new IllegalArgumentException("Invalid value for Contact type Enum: " + value);
    }
}
