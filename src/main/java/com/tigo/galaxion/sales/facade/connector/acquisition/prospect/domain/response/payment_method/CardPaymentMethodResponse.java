package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.payment_method;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.PaymentMethodTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

@ApiModel(description = "The payment card information.", parent = PaymentMethodResponse.class)
@JsonTypeName(PaymentMethodTypeEnum.Constant.CREDIT_CARD)
@SuperBuilder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CardPaymentMethodResponse extends PaymentMethodResponse {

    private String creditCardType;

    private String cardholderName;

    private LocalDate expirationDate;

    private String partialDigits;

    private String providerReference;

    @Override
    public PaymentMethodTypeEnum getPaymentMethodType() {
        return PaymentMethodTypeEnum.CREDIT_CARD;
    }

}
