package com.tigo.galaxion.sales.facade.connector.contact.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ContactPermissionGroupListV2Response {

    private Boolean allowThirdParty;

    private List<ContactPermissionGroupV2Response> permissionGroupResponse;
}
