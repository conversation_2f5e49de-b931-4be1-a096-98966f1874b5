package com.tigo.galaxion.sales.facade.domain.response;

import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TigoCrossSellResponse extends CrossSellResponse {

    @Setter
    private String contractSignatureOption;

}
