package com.tigo.galaxion.sales.facade.connector.contact;

import com.tigo.galaxion.sales.facade.connector.contact.domain.request.ContactV2Request;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PatchAddressRequest;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PatchEmailRequest;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PatchPhoneNumberRequest;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactIdentityDocumentV2Response;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactPermissionListV2Response;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactV2Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "contacts-service", url = "${environment.url.contacts-service}", configuration = ContactErrorDecoder.class)
public interface ContactClient {

    @GetMapping("/api/v2/permissions")
    ContactPermissionListV2Response getPermissions();

    @GetMapping("/api/v2/contacts")
    List<ContactV2Response> searchContact(@RequestParam("identityDocumentNumber") String identifier);

    @GetMapping("/api/v2/contacts/{contact_uuid}/identity-documents")
    List<ContactIdentityDocumentV2Response> getIdentityDocuments(@PathVariable("contact_uuid") String uuid);

    @PostMapping("/api/v2/contacts")
    String createContact(@RequestBody ContactV2Request contactV2Request);

    @GetMapping("/api/v2/contacts/{contact_uuid}")
    ContactV2Response getContact(@PathVariable(name = "contact_uuid") String contactUuid);

    @PatchMapping(value = "/api/v2/emails/{email_id}", consumes = "application/merge-patch+json")
    void updateEmail(@PathVariable(name = "email_id") Long emailId,
                     @RequestBody PatchEmailRequest request);

    @PatchMapping(value = "/api/v2/phone-numbers/{phone_number_id}", consumes = "application/merge-patch+json")
    void updatePhoneNumber(@PathVariable(name = "phone_number_id") Long phoneNumberId,
                           @RequestBody PatchPhoneNumberRequest request);

    @PatchMapping(value = "/api/v2/contacts/{contact_uuid}/addresses", consumes = "application/merge-patch+json")
    void updateOrCreateAddress(@PathVariable(name = "contact_uuid") String contactUuid,
                               @RequestBody PatchAddressRequest request);
}
