package com.tigo.galaxion.sales.facade.services.order_notification;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.ServiceActivationRequest;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.ServiceActivationRequest.Equipment.Identifier;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.ServiceActivationRequest.Equipment.Identifier.IdentifierType;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.app.dto.VariableUpdateDTO;
import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;
import mc.monacotelecom.workflow.base.enumeration.ProcessType;

@Service
@RequiredArgsConstructor
@Slf4j
public class ServiceActivationService {
    @Value("${wfe.signal.SP_WAIT_BB_PROV_REQUEST}")
    private String signalName;

    private final WorkflowEngineClient workflowEngineClient;

    public String sendNotification(String orderId, String serviceId, ServiceActivationRequest request)
            throws Exception {
        log.info("Create tecrep equipment: {}", orderId);
        workflowEngineClient.updateConnectorData(serviceId,
                VariableUpdateDTO.builder()
                        .type(ProcessType.SOM)
                        .data(request)
                        .variable("fieldservice")
                        .build());

        log.info("send signal from: {} to serviceId: {}; signalName: {}", orderId, serviceId, signalName);

        var trackingNumber = UUID.randomUUID().toString();

        var deviceParams = generateDeviceParamsMap(request);
        deviceParams.put("trackingNumber", trackingNumber);

        log.info("deviceParams: {}", deviceParams);

        workflowEngineClient.updateConnectorData(
                serviceId,
                VariableUpdateDTO.builder()
                        .type(ProcessType.SOM)
                        .data(deviceParams)
                        .variable("deviceParams")
                        .build());

        workflowEngineClient.processSignal(WorkflowSignalRequestDTO.builder()
                .name(signalName)
                .orderId(serviceId)
                .context(Map.of("fieldServiceResult", request.getTransactionType(),
                        "transactionId", request.getTransactionId()))
                .build());

        return trackingNumber;
    }

    private Map<String, Object> generateDeviceParamsMap(ServiceActivationRequest request) throws Exception {
        String brand = request.getEquipment().getBrand();

        Map<String, Object> deviceParams = new HashMap<>();

        // Get the equipment identifiers
        List<Identifier> identifiers = request.getEquipment().getIdentifiers();

        String deviceId = null;
        String smartcardId = null;
        if (request.getEquipment().getType().equalsIgnoreCase("DECO")) {
            Optional<Identifier> smartcard = identifiers.stream()
                    .filter(item -> IdentifierType.SMART_CARD_ID == item.getType())
                    .findFirst();
            if (smartcard.isPresent()) {
                smartcardId = smartcard.get().getId();
                deviceId = smartcard.get().getId();
                brand = "VERIMATRIX";
            } else {
                Identifier serial = identifiers.stream()
                        .filter(item -> IdentifierType.SERIAL == item.getType())
                        .findFirst().get();

                deviceId = serial.getId();
            }
        } else {
            Identifier mac1 = identifiers.stream()
                    .filter(p -> p.getType() == IdentifierType.MAC1)
                    .findFirst().get();

            deviceId = mac1.getId();
        }

        // Set the deviceParams properties
        deviceParams.put("type", request.getEquipment().getType());
        deviceParams.put("deviceId", deviceId);
        deviceParams.put("smartcardId", smartcardId);
        deviceParams.put("vendorCas", brand);
        deviceParams.put("provisioningId", request.getProvisioningId());
        deviceParams.put("assetId", request.getAssetId());
        deviceParams.put("code", request.getEquipment().getCode());
        // TODO: cambiar valores con nueva estructura de PA
        deviceParams.put("inventorySerialNumber", request.getEquipment().getInventorySerialNumber());
        deviceParams.put("phoneNumber", request.getEquipment().getInventorySerialNumber());
        deviceParams.put("port", request.getEquipment().getInventorySerialNumber());
        deviceParams.put("ssid", request.getEquipment().getInventorySerialNumber());
        deviceParams.put("apName", request.getEquipment().getInventorySerialNumber());

        return deviceParams;
    }

}
