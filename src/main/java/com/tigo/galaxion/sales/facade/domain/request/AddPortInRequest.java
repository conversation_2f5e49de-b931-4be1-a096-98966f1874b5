package com.tigo.galaxion.sales.facade.domain.request;

import com.tigo.galaxion.sales.facade.domain.enumeration.PortTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AddPortInRequest {

    @ApiModelProperty(value = "The account number. Only required for a POSTPAY type", example = "98")
    private String accountNumber;

    @NotNull
    @ApiModelProperty(value = "The port-in type", required = true, example = "PAYG_REGISTERED_MULTI_LINE")
    private PortTypeEnum type;

    @NotNull
    @ApiModelProperty(value = "Port start date.", example = "2026-01-21T21:00:00Z", required = true)
    private ZonedDateTime portStartDateTime;

    @NotBlank
    @ApiModelProperty(value = "Phone number to port-in.", example = "**********", required = true)
    private String msisdn;

}
