package com.tigo.galaxion.sales.facade.controller;

import java.text.ParseException;

import javax.xml.datatype.DatatypeConfigurationException;

import com.tigo.galaxion.sales.facade.domain.request.GetTaskRequest;
import com.tigo.galaxion.sales.facade.services.field_service.get_task.GetTaskClient;
import com.tigo.galaxion.sales.facade.soap.get_task.GtTaskRequest;
import com.tigo.galaxion.sales.facade.soap.get_task.GtTaskResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.domain.request.FieldServiceSlotsRequest;
import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointment;
import com.tigo.galaxion.sales.facade.domain.response.order_notification.ErrorResponse;
import com.tigo.galaxion.sales.facade.services.field_service.multiple_operations.MultipleOperationsClient;
import com.tigo.galaxion.sales.facade.services.field_service.process_task_ex.ProcessTaskExClient;
import com.tigo.galaxion.sales.facade.services.field_service.time_interval.TimeIntervalClient;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.ExecuteMultipleOperations;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.ExecuteMultipleOperationsResponse;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.ProcessTaskExResponse;
import com.tigo.galaxion.sales.facade.soap.time_interval.TimeIntervalResponse;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;

@Api
@RestController
@AllArgsConstructor
@RequestMapping("/api/v1/field-service")
public class FieldServiceController {

    private final TimeIntervalClient timeIntervalClient;
    private final ProcessTaskExClient processTaskExClient;
    private final MultipleOperationsClient multipleOperationsClient;
    private final GetTaskClient getTaskClient;

    @PostMapping("/time-interval")
    public TimeIntervalResponse getTimeInterval(@RequestBody FieldServiceSlotsRequest request)
            throws DatatypeConfigurationException, ParseException {
        return timeIntervalClient.getTimeIntervals(request);
    }

    @PostMapping("/process-task-ex")
    public ProcessTaskExResponse processTaskEx(@RequestBody FieldServiceConfirmAppointment request)
            throws DatatypeConfigurationException, ParseException {
        return processTaskExClient.processTaskEx(request);
    }

    @PostMapping("/multiple-operations")
    public ResponseEntity<?> multipleOperations(@RequestBody ExecuteMultipleOperations request) {

        try {
            ExecuteMultipleOperationsResponse response = multipleOperationsClient.multipleOperations(request);
            var action = response.getOperations().getOperation().get(0).getAction();
            if (action.equalsIgnoreCase("error")) {
                ErrorResponse errorResponse = new ErrorResponse(
                        400,
                        response.getOperations().getOperation().get(0).getObject().getObjectReference());
                return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
            } else {
                return new ResponseEntity<ExecuteMultipleOperationsResponse>(response, HttpStatus.OK);
            }
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    e.getMessage());
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/get-task")
    public ResponseEntity<?> getTask(@RequestBody GetTaskRequest request) {

        try {
            GtTaskResponse response = getTaskClient.getTask(request);
            var action = response.getMessageError();
            if (action.equalsIgnoreCase("error")) {
                ErrorResponse errorResponse = new ErrorResponse(
                        400,
                        action);
                return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
            } else {
                return new ResponseEntity<GtTaskResponse>(response, HttpStatus.OK);
            }
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    e.getMessage());
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
