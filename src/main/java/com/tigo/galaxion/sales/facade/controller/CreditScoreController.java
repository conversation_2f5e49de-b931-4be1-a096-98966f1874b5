package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.domain.request.CreditScoreRequest;
import com.tigo.galaxion.sales.facade.services.CreditScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static org.springframework.http.HttpStatus.CREATED;

@Api
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1")
public class CreditScoreController {

    private final CreditScoreService creditScoreService;

    @PostMapping("/prospects/{prospect_reference}/credit-scores")
    @ApiOperation("Execute credit score. Retrieve the contact information.")
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Document id already exist in galaxion (Problem type = 'identity-document-already-exists')"),
    })
    @ResponseStatus(CREATED)
    public void executeCreditScore(@ApiParam(value = "The prospect reference", required = true, example = "53ODP1O7") @PathVariable("prospect_reference") String prospectReference,
                                   @Valid @RequestBody CreditScoreRequest creditScoreRequest) {
        creditScoreService.executeCreditScore(prospectReference, creditScoreRequest);
    }

}
