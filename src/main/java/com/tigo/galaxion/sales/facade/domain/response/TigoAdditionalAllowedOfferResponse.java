package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoAdditionalAllowedOfferResponse {

    @ApiModelProperty(value = "The code of the service group", example = "MOBILE", required = true)
    private String serviceGroup;

    @ApiModelProperty(value = "The number of allowed offers", example = "5", required = true)
    private Long number;
}
