//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:48:22 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.process_task_ex;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.tigo.galaxion.sales.facade.soap.process_task_ex package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.tigo.galaxion.sales.facade.soap.process_task_ex
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ProcessTaskExRequest }
     * 
     */
    public ProcessTaskExRequest createProcessTaskExRequest() {
        return new ProcessTaskExRequest();
    }

    /**
     * Create an instance of {@link Task }
     * 
     */
    public Task createTask() {
        return new Task();
    }

    /**
     * Create an instance of {@link ProcessTaskExResponse }
     * 
     */
    public ProcessTaskExResponse createProcessTaskExResponse() {
        return new ProcessTaskExResponse();
    }

    /**
     * Create an instance of {@link TaskResponse }
     * 
     */
    public TaskResponse createTaskResponse() {
        return new TaskResponse();
    }

    /**
     * Create an instance of {@link TaskRequest }
     * 
     */
    public TaskRequest createTaskRequest() {
        return new TaskRequest();
    }

    /**
     * Create an instance of {@link Region }
     * 
     */
    public Region createRegion() {
        return new Region();
    }

    /**
     * Create an instance of {@link MCServices }
     * 
     */
    public MCServices createMCServices() {
        return new MCServices();
    }

    /**
     * Create an instance of {@link MCService }
     * 
     */
    public MCService createMCService() {
        return new MCService();
    }

    /**
     * Create an instance of {@link MCMVAssets }
     * 
     */
    public MCMVAssets createMCMVAssets() {
        return new MCMVAssets();
    }

    /**
     * Create an instance of {@link MCAsset }
     * 
     */
    public MCAsset createMCAsset() {
        return new MCAsset();
    }

    /**
     * Create an instance of {@link MaterialType }
     * 
     */
    public MaterialType createMaterialType() {
        return new MaterialType();
    }

    /**
     * Create an instance of {@link Service }
     * 
     */
    public Service createService() {
        return new Service();
    }

    /**
     * Create an instance of {@link Stamp }
     * 
     */
    public Stamp createStamp() {
        return new Stamp();
    }

    /**
     * Create an instance of {@link RequiredSkills1 }
     * 
     */
    public RequiredSkills1 createRequiredSkills1() {
        return new RequiredSkills1();
    }

    /**
     * Create an instance of {@link TaskRequiredSkill1 }
     * 
     */
    public TaskRequiredSkill1 createTaskRequiredSkill1() {
        return new TaskRequiredSkill1();
    }

}
