package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.equipments;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.HashSet;
import java.util.Set;

@ApiModel
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "type",
        visible = true,
        defaultImpl = InclusiveEquipmentResponse.class)
@JsonSubTypes({
        @JsonSubTypes.Type(value = InclusiveHandsetResponse.class, name = EquipmentTypeEnum.Constant.HANDSET),
        @JsonSubTypes.Type(value = InclusiveHandsetResponse.class, name = EquipmentTypeEnum.Constant.SIM),
})
@SuperBuilder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class InclusiveEquipmentResponse {

    private Boolean main;

    private String catalogCode;

    private String inclusiveEquipmentCatalogCode;

    private String color;

    private String colorCode;

    private String description;

    private String manufacturer;

    private String model;

    private String inventoryCode;

    private EquipmentTypeEnum type;

    private String inclusiveEquipmentGroupCatalogCode;

    private Set<String> serviceDomains = new HashSet<>();

    private String name;

    private String serialNumber;

    private String partNumber;

}
