package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.connector.order.domain.response.GetOrdersResponse;
import com.tigo.galaxion.sales.facade.connector.order.service.response.OrderServiceResponse;
import com.tigo.galaxion.sales.facade.services.OrderService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;


@Api
@RestController
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;

    @GetMapping("/api/v1/packages/orders/search/{msisdn}")
    @ApiOperation("Get orders by client")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Getting orders by client."),
    })
    public Page<GetOrdersResponse> getOrders(@PathVariable(value = "msisdn") Long msisdn,
                                             @RequestParam(value = "page", defaultValue = "0") Integer page,
                                             @RequestParam(value = "size", defaultValue = "10") Integer size,
                                             @RequestParam String start, @RequestParam String end) {
        return orderService.getOrdersByClient(page, size, msisdn, start, end);
    }

    @GetMapping("/api/v1/services/orders/search/{msisdn}")
    @ApiOperation("Get services by client")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Getting orders by client."),
    })
    public OrderServiceResponse getServices(@PathVariable(value = "msisdn") Long msisdn,
                                            @RequestParam(value = "page", defaultValue = "0") Integer page,
                                            @RequestParam(value = "size", defaultValue = "10") Integer size,
                                            @RequestParam String start, @RequestParam String end) {
        return orderService.getServicesByClient(page, size, msisdn, start, end);
    }

}
