package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.services.AddressService;

import com.tigo.galaxion.sales.facade.domain.request.AddressNormalizeRequestBody;
import com.tigo.galaxion.sales.facade.domain.response.AddressNormalizeResponseBody;
import com.tigo.galaxion.sales.facade.domain.response.AddressMSResponse;

import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;


@Api
@RestController
@RequiredArgsConstructor
public class AddressController {

    private final AddressService addressService;

    @PostMapping("/api/v1/address/normalize")
    @ApiOperation("Normalize an address.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Address Normalized."),
    })
    public AddressNormalizeResponseBody normalizeAddress(@Valid @RequestBody AddressNormalizeRequestBody request) {
        return addressService.normalizeAddress(request);
    }

    @GetMapping("api/v1/adresses/{id}")
    @ApiOperation("Get an address.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Address found."),
            @ApiResponse(code = 404, message = "Address not found.")
    })
    public AddressMSResponse getAdress(@PathVariable("id") Long id) {
        return addressService.getAddress(id);
    }

    @PostMapping("api/v1/adresses")
    @ApiOperation("Create an address.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Address created."),
            @ApiResponse(code = 400, message = "Invalid request.")
    })
    public long createAddress(@Valid @RequestBody AddressMSResponse address) {
        return addressService.createAddress(address);
    }

    @PatchMapping("api/v1/adresses/{id}")
    @ApiOperation("Update an address.")
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Invalid request.")
    })
    public void updateAddress(@PathVariable("id") Long id, @Valid @RequestBody AddressMSResponse address) {
        addressService.updateAddress(id, address);
    }
    

}
