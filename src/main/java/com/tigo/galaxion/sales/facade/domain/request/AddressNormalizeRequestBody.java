package com.tigo.galaxion.sales.facade.domain.request;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AddressNormalizeRequestBody {
    @JsonProperty("georreferenceRequest")
    @NotNull(message = "georreferenceRequest cannot be null")
    @Valid
    private AddressNormalizeRequest addressNormalizeRequest;
}
