package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.model.entity.ProspectEntity;
import com.tigo.galaxion.sales.facade.model.repository.ProspectRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ContractSignatureOptionService {

    private final ProspectRepository prospectRepository;

    public void updateContractSignatureOption(String prospectReference, String contractSignatureOption) {
        var prospect = prospectRepository.findByReference(prospectReference).orElse(ProspectEntity.builder().reference(prospectReference).build());
        prospect.setContractSignatureOption(contractSignatureOption);
        prospectRepository.save(prospect);
    }

}
