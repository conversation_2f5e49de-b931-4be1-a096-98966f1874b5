package com.tigo.galaxion.sales.facade.domain.request;

import com.tigo.galaxion.sales.facade.domain.enumeration.OfferTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@SuperBuilder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CreateCrossSellRequest {

    @NotBlank
    @ApiModelProperty(value = "The code of the channel the cross sell was created from.", example = "TELESALES", required = true)
    private String channelCode;

    @NotBlank
    @ApiModelProperty(value = "The brand of the cross sell.", example = "EIR", required = true)
    private String brand;

    @NotNull
    @ApiModelProperty(value = "The type of offer the cross sell was created from.", example = "POSTPAY", required = true)
    private OfferTypeEnum offerType;

    @ApiModelProperty(value = "Define if the cross sell will be to buy fixed accessories.", example = "true")
    private boolean accessory;
}
