
package com.tigo.galaxion.sales.facade.soap.get_task;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.tigo.galaxion.sales.facade.soap.get_task package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _TaskResponseKey_QNAME = new QName("", "Key");
    private final static QName _TaskResponseRevision_QNAME = new QName("", "Revision");
    private final static QName _TaskResponseStamp_QNAME = new QName("", "Stamp");
    private final static QName _TaskResponseCallID_QNAME = new QName("", "CallID");
    private final static QName _TaskResponseNumber_QNAME = new QName("", "Number");
    private final static QName _TaskResponseEarlyStart_QNAME = new QName("", "EarlyStart");
    private final static QName _TaskResponseDueDate_QNAME = new QName("", "DueDate");
    private final static QName _TaskResponseLateStart_QNAME = new QName("", "LateStart");
    private final static QName _TaskResponsePriority_QNAME = new QName("", "Priority");
    private final static QName _TaskResponseStatus_QNAME = new QName("", "Status");
    private final static QName _TaskResponseCustomer_QNAME = new QName("", "Customer");
    private final static QName _TaskResponseCalendar_QNAME = new QName("", "Calendar");
    private final static QName _TaskResponseRegion_QNAME = new QName("", "Region");
    private final static QName _TaskResponseDistrict_QNAME = new QName("", "District");
    private final static QName _TaskResponsePostcode_QNAME = new QName("", "Postcode");
    private final static QName _TaskResponsePreferredEngineers_QNAME = new QName("", "PreferredEngineers");
    private final static QName _TaskResponseContractType_QNAME = new QName("", "ContractType");
    private final static QName _TaskResponseOpenDate_QNAME = new QName("", "OpenDate");
    private final static QName _TaskResponseContactDate_QNAME = new QName("", "ContactDate");
    private final static QName _TaskResponseConfirmationDate_QNAME = new QName("", "ConfirmationDate");
    private final static QName _TaskResponseTaskType_QNAME = new QName("", "TaskType");
    private final static QName _TaskResponseDuration_QNAME = new QName("", "Duration");
    private final static QName _TaskResponseRequiredEngineers_QNAME = new QName("", "RequiredEngineers");
    private final static QName _TaskResponseNumberOfRequiredEngineers_QNAME = new QName("", "NumberOfRequiredEngineers");
    private final static QName _TaskResponseRequiredSkills1_QNAME = new QName("", "RequiredSkills1");
    private final static QName _TaskResponseRequiredSkills2_QNAME = new QName("", "RequiredSkills2");
    private final static QName _TaskResponseEngineerType_QNAME = new QName("", "EngineerType");
    private final static QName _TaskResponseRequiredEngineerTools_QNAME = new QName("", "RequiredEngineerTools");
    private final static QName _TaskResponseCritical_QNAME = new QName("", "Critical");
    private final static QName _TaskResponseTimeDependencies_QNAME = new QName("", "TimeDependencies");
    private final static QName _TaskResponseEngineerDependencies_QNAME = new QName("", "EngineerDependencies");
    private final static QName _TaskResponseAppointmentStart_QNAME = new QName("", "AppointmentStart");
    private final static QName _TaskResponseAppointmentFinish_QNAME = new QName("", "AppointmentFinish");
    private final static QName _TaskResponseContactName_QNAME = new QName("", "ContactName");
    private final static QName _TaskResponseContactPhoneNumber_QNAME = new QName("", "ContactPhoneNumber");
    private final static QName _TaskResponseBinaryData_QNAME = new QName("", "BinaryData");
    private final static QName _TaskResponseLatitude_QNAME = new QName("", "Latitude");
    private final static QName _TaskResponseLongitude_QNAME = new QName("", "Longitude");
    private final static QName _TaskResponseGISDataSource_QNAME = new QName("", "GISDataSource");
    private final static QName _TaskResponseStreet_QNAME = new QName("", "Street");
    private final static QName _TaskResponseCity_QNAME = new QName("", "City");
    private final static QName _TaskResponseMCState_QNAME = new QName("", "MCState");
    private final static QName _TaskResponseState_QNAME = new QName("", "State");
    private final static QName _TaskResponseTaskStatusContext_QNAME = new QName("", "TaskStatusContext");
    private final static QName _TaskResponseIsCrewTask_QNAME = new QName("", "IsCrewTask");
    private final static QName _TaskResponseCountryID_QNAME = new QName("", "CountryID");
    private final static QName _TaskResponseEngineerRequirements_QNAME = new QName("", "EngineerRequirements");
    private final static QName _TaskResponseIsScheduled_QNAME = new QName("", "IsScheduled");
    private final static QName _TaskResponseCustomerEmail_QNAME = new QName("", "CustomerEmail");
    private final static QName _TaskResponseExcludedEngineers_QNAME = new QName("", "ExcludedEngineers");
    private final static QName _TaskResponseRequiredCrewSize_QNAME = new QName("", "RequiredCrewSize");
    private final static QName _TaskResponseInJeopardy_QNAME = new QName("", "InJeopardy");
    private final static QName _TaskResponsePinned_QNAME = new QName("", "Pinned");
    private final static QName _TaskResponseJeopardyState_QNAME = new QName("", "JeopardyState");
    private final static QName _TaskResponseDisplayStatus_QNAME = new QName("", "DisplayStatus");
    private final static QName _TaskResponseDispatchDate_QNAME = new QName("", "DispatchDate");
    private final static QName _TaskResponseScheduleDate_QNAME = new QName("", "ScheduleDate");
    private final static QName _TaskResponseDisplayDate_QNAME = new QName("", "DisplayDate");
    private final static QName _TaskResponseOnSiteDate_QNAME = new QName("", "OnSiteDate");
    private final static QName _TaskResponseComment_QNAME = new QName("", "Comment");
    private final static QName _TaskResponseCustomerReference_QNAME = new QName("", "CustomerReference");
    private final static QName _TaskResponseStateSubdivision_QNAME = new QName("", "StateSubdivision");
    private final static QName _TaskResponseCitySubdivision_QNAME = new QName("", "CitySubdivision");
    private final static QName _TaskResponseTeam_QNAME = new QName("", "Team");
    private final static QName _TaskResponseSignature_QNAME = new QName("", "Signature");
    private final static QName _TaskResponseExternalRefID_QNAME = new QName("", "ExternalRefID");
    private final static QName _TaskResponsePartsUsed_QNAME = new QName("", "PartsUsed");
    private final static QName _TaskResponseAssets_QNAME = new QName("", "Assets");
    private final static QName _TaskResponseBackReportings_QNAME = new QName("", "BackReportings");
    private final static QName _TaskResponseTaskTypeCategory_QNAME = new QName("", "TaskTypeCategory");
    private final static QName _TaskResponseWorkOrderItem_QNAME = new QName("", "WorkOrderItem");
    private final static QName _TaskResponseUserCustomerAccount_QNAME = new QName("", "User_CustomerAccount");
    private final static QName _TaskResponseIsAppointment_QNAME = new QName("", "IsAppointment");
    private final static QName _TaskResponseTravelDate_QNAME = new QName("", "TravelDate");
    private final static QName _TaskResponseCompletionDate_QNAME = new QName("", "CompletionDate");
    private final static QName _TaskResponseAttachments_QNAME = new QName("", "Attachments");
    private final static QName _TaskResponseCancellationDate_QNAME = new QName("", "CancellationDate");
    private final static QName _TaskResponseIsMegatask_QNAME = new QName("", "IsMegatask");
    private final static QName _TaskResponseIsBundled_QNAME = new QName("", "IsBundled");
    private final static QName _TaskResponseIsManuallyBundled_QNAME = new QName("", "IsManuallyBundled");
    private final static QName _TaskResponseMegatask_QNAME = new QName("", "Megatask");
    private final static QName _TaskResponseMegataskPureDuration_QNAME = new QName("", "MegataskPureDuration");
    private final static QName _TaskResponseBundlerConfiguration_QNAME = new QName("", "BundlerConfiguration");
    private final static QName _TaskResponseSubtasks_QNAME = new QName("", "Subtasks");
    private final static QName _TaskResponseSkillsDuration_QNAME = new QName("", "SkillsDuration");
    private final static QName _TaskResponseCustomerAccount_QNAME = new QName("", "CustomerAccount");
    private final static QName _TaskResponseArea_QNAME = new QName("", "Area");
    private final static QName _TaskResponseIncompleteReason_QNAME = new QName("", "IncompleteReason");
    private final static QName _TaskResponseMCWorkPackageDescription_QNAME = new QName("", "MCWorkPackageDescription");
    private final static QName _TaskResponseMCComment_QNAME = new QName("", "MCComment");
    private final static QName _TaskResponseMCCRMComment_QNAME = new QName("", "MCCRMComment");
    private final static QName _TaskResponseMCContactEmail_QNAME = new QName("", "MCContactEmail");
    private final static QName _TaskResponseMCCustomerCode_QNAME = new QName("", "MCCustomerCode");
    private final static QName _TaskResponseMCCustomerPhoneNumber_QNAME = new QName("", "MCCustomerPhoneNumber");
    private final static QName _TaskResponseMCSaldoPending_QNAME = new QName("", "MCSaldoPending");
    private final static QName _TaskResponseMCBillingAccountInfo_QNAME = new QName("", "MCBillingAccountInfo");
    private final static QName _TaskResponseMCConnectionData_QNAME = new QName("", "MCConnectionData");
    private final static QName _TaskResponseLastRejectedEngineer_QNAME = new QName("", "LastRejectedEngineer");
    private final static QName _TaskResponseRejectedDate_QNAME = new QName("", "RejectedDate");
    private final static QName _TaskResponseRejectionReason_QNAME = new QName("", "RejectionReason");
    private final static QName _TaskResponseCancellationReason_QNAME = new QName("", "CancellationReason");
    private final static QName _TaskResponseMCConfirmationStatus_QNAME = new QName("", "MCConfirmationStatus");
    private final static QName _TaskResponseMCMVPartsRequired_QNAME = new QName("", "MCMVPartsRequired");
    private final static QName _TaskResponseMCMVMaterialUsed_QNAME = new QName("", "MCMVMaterialUsed");
    private final static QName _TaskResponseMCMVEquipmentUsed_QNAME = new QName("", "MCMVEquipmentUsed");
    private final static QName _TaskResponseMCMVEquipmentCollected_QNAME = new QName("", "MCMVEquipmentCollected");
    private final static QName _TaskResponseMCNoMaterialUsed_QNAME = new QName("", "MCNoMaterialUsed");
    private final static QName _TaskResponseMCNoEquipmentUsed_QNAME = new QName("", "MCNoEquipmentUsed");
    private final static QName _TaskResponseMCCauseReason_QNAME = new QName("", "MCCauseReason");
    private final static QName _TaskResponseMCLaboresUsed_QNAME = new QName("", "MCLaboresUsed");
    private final static QName _TaskResponseMCCustomsSeal_QNAME = new QName("", "MCCustomsSeal");
    private final static QName _TaskResponseMCCodeBobina1_QNAME = new QName("", "MCCodeBobina1");
    private final static QName _TaskResponseMCCodeBobina2_QNAME = new QName("", "MCCodeBobina2");
    private final static QName _TaskResponseMCMVServices_QNAME = new QName("", "MCMVServices");
    private final static QName _TaskResponseDynamicPriority_QNAME = new QName("", "DynamicPriority");
    private final static QName _TaskResponseExternalRefIDExtension_QNAME = new QName("", "ExternalRefIDExtension");
    private final static QName _TaskResponseServiceSummary_QNAME = new QName("", "ServiceSummary");
    private final static QName _TaskResponseStreetSmartJob_QNAME = new QName("", "StreetSmartJob");
    private final static QName _TaskResponseRecurrenceTask_QNAME = new QName("", "RecurrenceTask");
    private final static QName _TaskResponseRTIsRecurringTask_QNAME = new QName("", "RTIsRecurringTask");
    private final static QName _TaskResponseRTUpdateAllRecurrences_QNAME = new QName("", "RTUpdateAllRecurrences");
    private final static QName _TaskResponseRTIsPrime_QNAME = new QName("", "RTIsPrime");
    private final static QName _TaskResponseArchiveStatus_QNAME = new QName("", "ArchiveStatus");
    private final static QName _TaskResponseAssignedEngineerName_QNAME = new QName("", "AssignedEngineerName");
    private final static QName _TaskResponseMobileKey_QNAME = new QName("", "MobileKey");
    private final static QName _TaskResponseUnit_QNAME = new QName("", "Unit");
    private final static QName _TaskResponseSupervisorStatusGroup_QNAME = new QName("", "SupervisorStatusGroup");
    private final static QName _TaskResponseIsSingleTask_QNAME = new QName("", "IsSingleTask");
    private final static QName _TaskResponseFieldCommentEng_QNAME = new QName("", "FieldCommentEng");
    private final static QName _TaskResponseMCCustomerClass_QNAME = new QName("", "MCCustomerClass");
    private final static QName _TaskResponseMCOpeningReason_QNAME = new QName("", "MCOpeningReason");
    private final static QName _TaskResponseMCZonaRamal_QNAME = new QName("", "MCZonaRamal");
    private final static QName _TaskResponseMCTap_QNAME = new QName("", "MCTap");
    private final static QName _TaskResponseMCBoca_QNAME = new QName("", "MCBoca");
    private final static QName _TaskResponseMCInfoCustomerSite_QNAME = new QName("", "MCInfoCustomerSite");
    private final static QName _TaskResponseMCTaskClosureLongitude_QNAME = new QName("", "MCTaskClosureLongitude");
    private final static QName _TaskResponseMCTaskClosureLatitude_QNAME = new QName("", "MCTaskClosureLatitude");
    private final static QName _TaskResponseMCLastLocationInfo_QNAME = new QName("", "MCLastLocationInfo");
    private final static QName _TaskResponseMCTaskClosureGeoToken_QNAME = new QName("", "MCTaskClosureGeoToken");
    private final static QName _TaskResponseMCTaskClosureGeoTokenEntered_QNAME = new QName("", "MCTaskClosureGeoTokenEntered");
    private final static QName _TaskResponseURLAuditInfo_QNAME = new QName("", "URLAuditInfo");
    private final static QName _TaskResponseURLSurvey_QNAME = new QName("", "URLSurvey");
    private final static QName _TaskResponseMCBuildingType_QNAME = new QName("", "MCBuildingType");
    private final static QName _TaskResponseMCServicesSignature_QNAME = new QName("", "MCServicesSignature");
    private final static QName _TaskResponseMCPlaca_QNAME = new QName("", "MCPlaca");
    private final static QName _TaskResponseMCCustomerIdentityNumber_QNAME = new QName("", "MCCustomerIdentityNumber");
    private final static QName _TaskResponseIsDESent_QNAME = new QName("", "IsDESent");
    private final static QName _TaskResponseReturnVerification_QNAME = new QName("", "ReturnVerification");
    private final static QName _TaskResponseIsETASent_QNAME = new QName("", "IsETASent");
    private final static QName _TaskResponseIsReminder24HourSent_QNAME = new QName("", "IsReminder24HourSent");
    private final static QName _TaskResponseIsSurveySent_QNAME = new QName("", "IsSurveySent");
    private final static QName _TaskResponseScheduleLowerBound_QNAME = new QName("", "ScheduleLowerBound");
    private final static QName _TaskResponseScheduleUpperBound_QNAME = new QName("", "ScheduleUpperBound");
    private final static QName _TaskResponseSurveyAnswer_QNAME = new QName("", "SurveyAnswer");
    private final static QName _TaskResponseSurveyComment_QNAME = new QName("", "SurveyComment");
    private final static QName _TaskResponseServiceAccepted_QNAME = new QName("", "ServiceAccepted");
    private final static QName _TaskResponseMCGoogleLatitude_QNAME = new QName("", "MCGoogleLatitude");
    private final static QName _TaskResponseMCGoogleLongitude_QNAME = new QName("", "MCGoogleLongitude");
    private final static QName _TaskResponseMCCustomerAssets_QNAME = new QName("", "MCCustomerAssets");
    private final static QName _TaskResponseMCPTWorkingArea_QNAME = new QName("", "MCPTWorkingArea");
    private final static QName _TaskResponseMCPTFilter_QNAME = new QName("", "MCPTFilter");
    private final static QName _TaskResponseMCTPPolygonFilter_QNAME = new QName("", "MCTPPolygonFilter");
    private final static QName _TaskResponseWorkingArea_QNAME = new QName("", "WorkingArea");
    private final static QName _TaskResponseMCDispatchOMSent_QNAME = new QName("", "MCDispatchOMSent");
    private final static QName _TaskResponseMCDEComment_QNAME = new QName("", "MCDEComment");
    private final static QName _TaskResponseMCCEMAttachments_QNAME = new QName("", "MCCEMAttachments");
    private final static QName _TaskResponseMCContactPhoneNumbers_QNAME = new QName("", "MCContactPhoneNumbers");
    private final static QName _TaskResponseTaskAppointmentTime_QNAME = new QName("", "TaskAppointmentTime");
    private final static QName _TaskResponseTaskAppointmentUpdated_QNAME = new QName("", "TaskAppointmentUpdated");
    private final static QName _TaskResponseMCServicePaid_QNAME = new QName("", "MCServicePaid");
    private final static QName _TaskResponseMCUnscheduledForNotPaid_QNAME = new QName("", "MCUnscheduledForNotPaid");
    private final static QName _TaskResponseMCCEMABCancellationNotification_QNAME = new QName("", "MCCEMABCancellationNotification");
    private final static QName _TaskResponseMCUnscheduledForNotPaidCounter_QNAME = new QName("", "MCUnscheduledForNotPaidCounter");
    private final static QName _TaskResponseMCTaskUnpaidReminderSent_QNAME = new QName("", "MCTaskUnpaidReminderSent");
    private final static QName _TaskResponseMCCEMNoMsgOnDispatcherReschedule_QNAME = new QName("", "MCCEMNoMsgOnDispatcherReschedule");
    private final static QName _TaskResponseMCIsNonABTaskScheduledNotificationSent_QNAME = new QName("", "MCIsNonABTaskScheduledNotificationSent");
    private final static QName _TaskResponseMCNewTaskAssigned_QNAME = new QName("", "MCNewTaskAssigned");
    private final static QName _TaskResponseMCWifiCertService_QNAME = new QName("", "MCWifiCertService");
    private final static QName _TaskResponseMCShowCertificateWifiInitiate_QNAME = new QName("", "MCShowCertificateWifiInitiate");
    private final static QName _TaskResponseMCCertificateResultWifi_QNAME = new QName("", "MCCertificateResultWifi");
    private final static QName _TaskResponseMCCertificationActionType_QNAME = new QName("", "MCCertificationActionType");
    private final static QName _TaskResponseMCCertWifiGenerateTriggerC_QNAME = new QName("", "MCCertWifiGenerateTrigger__c");
    private final static QName _TaskResponseMCCertificateWifiRequired_QNAME = new QName("", "MCCertificateWifiRequired");
    private final static QName _TaskResponseMCTAPHighFrecuency_QNAME = new QName("", "MCTAPHighFrecuency");
    private final static QName _TaskResponseMCTAPLowFrecuency_QNAME = new QName("", "MCTAPLowFrecuency");
    private final static QName _TaskResponseMCSplitterHighFrecuency_QNAME = new QName("", "MCSplitterHighFrecuency");
    private final static QName _TaskResponseMCSplitterLowFrecuency_QNAME = new QName("", "MCSplitterLowFrecuency");
    private final static QName _TaskResponseMCCMHighFrecuency_QNAME = new QName("", "MCCMHighFrecuency");
    private final static QName _TaskResponseMCCMLowFrecuency_QNAME = new QName("", "MCCMLowFrecuency");
    private final static QName _TaskResponseMCResendClosureOM_QNAME = new QName("", "MCResendClosureOM");
    private final static QName _TaskResponseCodigoTecnico_QNAME = new QName("", "CodigoTecnico");
    private final static QName _TaskResponseServiceAppointmentID_QNAME = new QName("", "ServiceAppointmentID");
    private final static QName _TaskResponseAppointmentStatus_QNAME = new QName("", "AppointmentStatus");
    private final static QName _TaskResponseServiceTerritory_QNAME = new QName("", "ServiceTerritory");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.tigo.galaxion.sales.facade.soap.get_task
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link GtTaskRequest }
     * 
     */
    public GtTaskRequest createGtTaskRequest() {
        return new GtTaskRequest();
    }

    /**
     * Create an instance of {@link Task }
     * 
     */
    public Task createTask() {
        return new Task();
    }

    /**
     * Create an instance of {@link GtTaskResponse }
     * 
     */
    public GtTaskResponse createGtTaskResponse() {
        return new GtTaskResponse();
    }

    /**
     * Create an instance of {@link TaskResponse }
     * 
     */
    public TaskResponse createTaskResponse() {
        return new TaskResponse();
    }

    /**
     * Create an instance of {@link TaskRequest }
     * 
     */
    public TaskRequest createTaskRequest() {
        return new TaskRequest();
    }

    /**
     * Create an instance of {@link Stamp }
     * 
     */
    public Stamp createStamp() {
        return new Stamp();
    }

    /**
     * Create an instance of {@link MCMVMaterialUsedKey }
     * 
     */
    public MCMVMaterialUsedKey createMCMVMaterialUsedKey() {
        return new MCMVMaterialUsedKey();
    }

    /**
     * Create an instance of {@link Attributes }
     * 
     */
    public Attributes createAttributes() {
        return new Attributes();
    }

    /**
     * Create an instance of {@link CustomerAccount }
     * 
     */
    public CustomerAccount createCustomerAccount() {
        return new CustomerAccount();
    }

    /**
     * Create an instance of {@link BillingAdress }
     * 
     */
    public BillingAdress createBillingAdress() {
        return new BillingAdress();
    }

    /**
     * Create an instance of {@link MCMVEquipmentUsedKey }
     * 
     */
    public MCMVEquipmentUsedKey createMCMVEquipmentUsedKey() {
        return new MCMVEquipmentUsedKey();
    }

    /**
     * Create an instance of {@link MCMVEquipmentCollectedKey }
     * 
     */
    public MCMVEquipmentCollectedKey createMCMVEquipmentCollectedKey() {
        return new MCMVEquipmentCollectedKey();
    }

    /**
     * Create an instance of {@link MCLaboresUsedKey }
     * 
     */
    public MCLaboresUsedKey createMCLaboresUsedKey() {
        return new MCLaboresUsedKey();
    }

    /**
     * Create an instance of {@link MCServices }
     * 
     */
    public MCServices createMCServices() {
        return new MCServices();
    }

    /**
     * Create an instance of {@link MCService }
     * 
     */
    public MCService createMCService() {
        return new MCService();
    }

    /**
     * Create an instance of {@link RequiredSkills1 }
     * 
     */
    public RequiredSkills1 createRequiredSkills1() {
        return new RequiredSkills1();
    }

    /**
     * Create an instance of {@link TaskRequiredSkill1 }
     * 
     */
    public TaskRequiredSkill1 createTaskRequiredSkill1() {
        return new TaskRequiredSkill1();
    }

    /**
     * Create an instance of {@link Assignment }
     * 
     */
    public Assignment createAssignment() {
        return new Assignment();
    }

    /**
     * Create an instance of {@link TaskAssigment }
     * 
     */
    public TaskAssigment createTaskAssigment() {
        return new TaskAssigment();
    }

    /**
     * Create an instance of {@link Engineer }
     * 
     */
    public Engineer createEngineer() {
        return new Engineer();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Key", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseKey(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseKey_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Revision", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseRevision(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseRevision_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Stamp }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Stamp }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Stamp", scope = TaskResponse.class)
    public JAXBElement<Stamp> createTaskResponseStamp(Stamp value) {
        return new JAXBElement<Stamp>(_TaskResponseStamp_QNAME, Stamp.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CallID", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCallID(String value) {
        return new JAXBElement<String>(_TaskResponseCallID_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Number", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseNumber(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseNumber_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "EarlyStart", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseEarlyStart(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseEarlyStart_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "DueDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseDueDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseDueDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "LateStart", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseLateStart(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseLateStart_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Priority", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponsePriority(String value) {
        return new JAXBElement<String>(_TaskResponsePriority_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Status", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseStatus(String value) {
        return new JAXBElement<String>(_TaskResponseStatus_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Customer", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCustomer(String value) {
        return new JAXBElement<String>(_TaskResponseCustomer_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Calendar", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCalendar(String value) {
        return new JAXBElement<String>(_TaskResponseCalendar_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Region", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseRegion(String value) {
        return new JAXBElement<String>(_TaskResponseRegion_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "District", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseDistrict(String value) {
        return new JAXBElement<String>(_TaskResponseDistrict_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Postcode", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponsePostcode(String value) {
        return new JAXBElement<String>(_TaskResponsePostcode_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "PreferredEngineers", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponsePreferredEngineers(String value) {
        return new JAXBElement<String>(_TaskResponsePreferredEngineers_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ContractType", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseContractType(String value) {
        return new JAXBElement<String>(_TaskResponseContractType_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "OpenDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseOpenDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseOpenDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ContactDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseContactDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseContactDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ConfirmationDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseConfirmationDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseConfirmationDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "TaskType", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseTaskType(String value) {
        return new JAXBElement<String>(_TaskResponseTaskType_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Double }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Double }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Duration", scope = TaskResponse.class)
    public JAXBElement<Double> createTaskResponseDuration(Double value) {
        return new JAXBElement<Double>(_TaskResponseDuration_QNAME, Double.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RequiredEngineers", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseRequiredEngineers(String value) {
        return new JAXBElement<String>(_TaskResponseRequiredEngineers_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "NumberOfRequiredEngineers", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseNumberOfRequiredEngineers(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseNumberOfRequiredEngineers_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TaskRequiredSkill1 }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link TaskRequiredSkill1 }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RequiredSkills1", scope = TaskResponse.class)
    public JAXBElement<TaskRequiredSkill1> createTaskResponseRequiredSkills1(TaskRequiredSkill1 value) {
        return new JAXBElement<TaskRequiredSkill1>(_TaskResponseRequiredSkills1_QNAME, TaskRequiredSkill1 .class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RequiredSkills2", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseRequiredSkills2(String value) {
        return new JAXBElement<String>(_TaskResponseRequiredSkills2_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "EngineerType", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseEngineerType(String value) {
        return new JAXBElement<String>(_TaskResponseEngineerType_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RequiredEngineerTools", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseRequiredEngineerTools(String value) {
        return new JAXBElement<String>(_TaskResponseRequiredEngineerTools_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Critical", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseCritical(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseCritical_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "TimeDependencies", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseTimeDependencies(String value) {
        return new JAXBElement<String>(_TaskResponseTimeDependencies_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "EngineerDependencies", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseEngineerDependencies(String value) {
        return new JAXBElement<String>(_TaskResponseEngineerDependencies_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "AppointmentStart", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseAppointmentStart(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseAppointmentStart_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "AppointmentFinish", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseAppointmentFinish(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseAppointmentFinish_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ContactName", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseContactName(String value) {
        return new JAXBElement<String>(_TaskResponseContactName_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ContactPhoneNumber", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseContactPhoneNumber(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseContactPhoneNumber_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "BinaryData", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseBinaryData(String value) {
        return new JAXBElement<String>(_TaskResponseBinaryData_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Double }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Double }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Latitude", scope = TaskResponse.class)
    public JAXBElement<Double> createTaskResponseLatitude(Double value) {
        return new JAXBElement<Double>(_TaskResponseLatitude_QNAME, Double.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Double }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Double }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Longitude", scope = TaskResponse.class)
    public JAXBElement<Double> createTaskResponseLongitude(Double value) {
        return new JAXBElement<Double>(_TaskResponseLongitude_QNAME, Double.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "GISDataSource", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseGISDataSource(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseGISDataSource_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Street", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseStreet(String value) {
        return new JAXBElement<String>(_TaskResponseStreet_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "City", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCity(String value) {
        return new JAXBElement<String>(_TaskResponseCity_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCState", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCState(String value) {
        return new JAXBElement<String>(_TaskResponseMCState_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "State", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseState(String value) {
        return new JAXBElement<String>(_TaskResponseState_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "TaskStatusContext", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseTaskStatusContext(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseTaskStatusContext_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsCrewTask", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsCrewTask(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsCrewTask_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CountryID", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCountryID(String value) {
        return new JAXBElement<String>(_TaskResponseCountryID_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "EngineerRequirements", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseEngineerRequirements(String value) {
        return new JAXBElement<String>(_TaskResponseEngineerRequirements_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsScheduled", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsScheduled(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsScheduled_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CustomerEmail", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseCustomerEmail(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseCustomerEmail_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ExcludedEngineers", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseExcludedEngineers(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseExcludedEngineers_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RequiredCrewSize", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseRequiredCrewSize(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseRequiredCrewSize_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "InJeopardy", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseInJeopardy(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseInJeopardy_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Pinned", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponsePinned(Integer value) {
        return new JAXBElement<Integer>(_TaskResponsePinned_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "JeopardyState", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseJeopardyState(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseJeopardyState_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "DisplayStatus", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseDisplayStatus(String value) {
        return new JAXBElement<String>(_TaskResponseDisplayStatus_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "DispatchDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseDispatchDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseDispatchDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ScheduleDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseScheduleDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseScheduleDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "DisplayDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseDisplayDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseDisplayDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "OnSiteDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseOnSiteDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseOnSiteDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Comment", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseComment(String value) {
        return new JAXBElement<String>(_TaskResponseComment_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CustomerReference", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCustomerReference(String value) {
        return new JAXBElement<String>(_TaskResponseCustomerReference_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "StateSubdivision", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseStateSubdivision(String value) {
        return new JAXBElement<String>(_TaskResponseStateSubdivision_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CitySubdivision", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCitySubdivision(String value) {
        return new JAXBElement<String>(_TaskResponseCitySubdivision_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Team", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseTeam(String value) {
        return new JAXBElement<String>(_TaskResponseTeam_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Signature", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseSignature(String value) {
        return new JAXBElement<String>(_TaskResponseSignature_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ExternalRefID", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseExternalRefID(String value) {
        return new JAXBElement<String>(_TaskResponseExternalRefID_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "PartsUsed", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponsePartsUsed(String value) {
        return new JAXBElement<String>(_TaskResponsePartsUsed_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Assets", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseAssets(String value) {
        return new JAXBElement<String>(_TaskResponseAssets_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "BackReportings", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseBackReportings(String value) {
        return new JAXBElement<String>(_TaskResponseBackReportings_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "TaskTypeCategory", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseTaskTypeCategory(String value) {
        return new JAXBElement<String>(_TaskResponseTaskTypeCategory_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "WorkOrderItem", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseWorkOrderItem(String value) {
        return new JAXBElement<String>(_TaskResponseWorkOrderItem_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CustomerAccount }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link CustomerAccount }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "User_CustomerAccount", scope = TaskResponse.class)
    public JAXBElement<CustomerAccount> createTaskResponseUserCustomerAccount(CustomerAccount value) {
        return new JAXBElement<CustomerAccount>(_TaskResponseUserCustomerAccount_QNAME, CustomerAccount.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsAppointment", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsAppointment(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsAppointment_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "TravelDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseTravelDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseTravelDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CompletionDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseCompletionDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseCompletionDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Attachments", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseAttachments(String value) {
        return new JAXBElement<String>(_TaskResponseAttachments_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CancellationDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseCancellationDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseCancellationDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsMegatask", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsMegatask(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsMegatask_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsBundled", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsBundled(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsBundled_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsManuallyBundled", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsManuallyBundled(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsManuallyBundled_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Megatask", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMegatask(String value) {
        return new JAXBElement<String>(_TaskResponseMegatask_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MegataskPureDuration", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMegataskPureDuration(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMegataskPureDuration_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "BundlerConfiguration", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseBundlerConfiguration(String value) {
        return new JAXBElement<String>(_TaskResponseBundlerConfiguration_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Subtasks", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseSubtasks(String value) {
        return new JAXBElement<String>(_TaskResponseSubtasks_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "SkillsDuration", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseSkillsDuration(String value) {
        return new JAXBElement<String>(_TaskResponseSkillsDuration_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CustomerAccount", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCustomerAccount(String value) {
        return new JAXBElement<String>(_TaskResponseCustomerAccount_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Area", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseArea(String value) {
        return new JAXBElement<String>(_TaskResponseArea_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IncompleteReason", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseIncompleteReason(String value) {
        return new JAXBElement<String>(_TaskResponseIncompleteReason_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCWorkPackageDescription", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCWorkPackageDescription(String value) {
        return new JAXBElement<String>(_TaskResponseMCWorkPackageDescription_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCComment", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCComment(String value) {
        return new JAXBElement<String>(_TaskResponseMCComment_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCRMComment", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCRMComment(String value) {
        return new JAXBElement<String>(_TaskResponseMCCRMComment_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCContactEmail", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCContactEmail(String value) {
        return new JAXBElement<String>(_TaskResponseMCContactEmail_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCustomerCode", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCCustomerCode(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCCustomerCode_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCustomerPhoneNumber", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCCustomerPhoneNumber(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCCustomerPhoneNumber_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Double }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Double }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCSaldoPending", scope = TaskResponse.class)
    public JAXBElement<Double> createTaskResponseMCSaldoPending(Double value) {
        return new JAXBElement<Double>(_TaskResponseMCSaldoPending_QNAME, Double.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCBillingAccountInfo", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCBillingAccountInfo(String value) {
        return new JAXBElement<String>(_TaskResponseMCBillingAccountInfo_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCConnectionData", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCConnectionData(String value) {
        return new JAXBElement<String>(_TaskResponseMCConnectionData_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "LastRejectedEngineer", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseLastRejectedEngineer(String value) {
        return new JAXBElement<String>(_TaskResponseLastRejectedEngineer_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RejectedDate", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseRejectedDate(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseRejectedDate_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RejectionReason", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseRejectionReason(String value) {
        return new JAXBElement<String>(_TaskResponseRejectionReason_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CancellationReason", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCancellationReason(String value) {
        return new JAXBElement<String>(_TaskResponseCancellationReason_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCConfirmationStatus", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCConfirmationStatus(String value) {
        return new JAXBElement<String>(_TaskResponseMCConfirmationStatus_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCMVPartsRequired", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCMVPartsRequired(String value) {
        return new JAXBElement<String>(_TaskResponseMCMVPartsRequired_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MCMVMaterialUsedKey }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link MCMVMaterialUsedKey }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCMVMaterialUsed", scope = TaskResponse.class)
    public JAXBElement<MCMVMaterialUsedKey> createTaskResponseMCMVMaterialUsed(MCMVMaterialUsedKey value) {
        return new JAXBElement<MCMVMaterialUsedKey>(_TaskResponseMCMVMaterialUsed_QNAME, MCMVMaterialUsedKey.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MCMVEquipmentUsedKey }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link MCMVEquipmentUsedKey }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCMVEquipmentUsed", scope = TaskResponse.class)
    public JAXBElement<MCMVEquipmentUsedKey> createTaskResponseMCMVEquipmentUsed(MCMVEquipmentUsedKey value) {
        return new JAXBElement<MCMVEquipmentUsedKey>(_TaskResponseMCMVEquipmentUsed_QNAME, MCMVEquipmentUsedKey.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MCMVEquipmentCollectedKey }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link MCMVEquipmentCollectedKey }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCMVEquipmentCollected", scope = TaskResponse.class)
    public JAXBElement<MCMVEquipmentCollectedKey> createTaskResponseMCMVEquipmentCollected(MCMVEquipmentCollectedKey value) {
        return new JAXBElement<MCMVEquipmentCollectedKey>(_TaskResponseMCMVEquipmentCollected_QNAME, MCMVEquipmentCollectedKey.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCNoMaterialUsed", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCNoMaterialUsed(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCNoMaterialUsed_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCNoEquipmentUsed", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCNoEquipmentUsed(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCNoEquipmentUsed_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCauseReason", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCauseReason(String value) {
        return new JAXBElement<String>(_TaskResponseMCCauseReason_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MCLaboresUsedKey }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link MCLaboresUsedKey }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCLaboresUsed", scope = TaskResponse.class)
    public JAXBElement<MCLaboresUsedKey> createTaskResponseMCLaboresUsed(MCLaboresUsedKey value) {
        return new JAXBElement<MCLaboresUsedKey>(_TaskResponseMCLaboresUsed_QNAME, MCLaboresUsedKey.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCustomsSeal", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCustomsSeal(String value) {
        return new JAXBElement<String>(_TaskResponseMCCustomsSeal_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCodeBobina1", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCodeBobina1(String value) {
        return new JAXBElement<String>(_TaskResponseMCCodeBobina1_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCodeBobina2", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCodeBobina2(String value) {
        return new JAXBElement<String>(_TaskResponseMCCodeBobina2_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MCServices }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link MCServices }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCMVServices", scope = TaskResponse.class)
    public JAXBElement<MCServices> createTaskResponseMCMVServices(MCServices value) {
        return new JAXBElement<MCServices>(_TaskResponseMCMVServices_QNAME, MCServices.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "DynamicPriority", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseDynamicPriority(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseDynamicPriority_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ExternalRefIDExtension", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseExternalRefIDExtension(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseExternalRefIDExtension_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ServiceSummary", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseServiceSummary(String value) {
        return new JAXBElement<String>(_TaskResponseServiceSummary_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "StreetSmartJob", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseStreetSmartJob(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseStreetSmartJob_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RecurrenceTask", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseRecurrenceTask(String value) {
        return new JAXBElement<String>(_TaskResponseRecurrenceTask_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RTIsRecurringTask", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseRTIsRecurringTask(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseRTIsRecurringTask_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RTUpdateAllRecurrences", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseRTUpdateAllRecurrences(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseRTUpdateAllRecurrences_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "RTIsPrime", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseRTIsPrime(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseRTIsPrime_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ArchiveStatus", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseArchiveStatus(String value) {
        return new JAXBElement<String>(_TaskResponseArchiveStatus_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "AssignedEngineerName", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseAssignedEngineerName(String value) {
        return new JAXBElement<String>(_TaskResponseAssignedEngineerName_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MobileKey", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMobileKey(String value) {
        return new JAXBElement<String>(_TaskResponseMobileKey_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "Unit", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseUnit(String value) {
        return new JAXBElement<String>(_TaskResponseUnit_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "SupervisorStatusGroup", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseSupervisorStatusGroup(String value) {
        return new JAXBElement<String>(_TaskResponseSupervisorStatusGroup_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsSingleTask", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsSingleTask(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsSingleTask_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "FieldCommentEng", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseFieldCommentEng(String value) {
        return new JAXBElement<String>(_TaskResponseFieldCommentEng_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCustomerClass", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCustomerClass(String value) {
        return new JAXBElement<String>(_TaskResponseMCCustomerClass_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCOpeningReason", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCOpeningReason(String value) {
        return new JAXBElement<String>(_TaskResponseMCOpeningReason_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCZonaRamal", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCZonaRamal(String value) {
        return new JAXBElement<String>(_TaskResponseMCZonaRamal_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCTap", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCTap(String value) {
        return new JAXBElement<String>(_TaskResponseMCTap_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCBoca", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCBoca(String value) {
        return new JAXBElement<String>(_TaskResponseMCBoca_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCInfoCustomerSite", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCInfoCustomerSite(String value) {
        return new JAXBElement<String>(_TaskResponseMCInfoCustomerSite_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCTaskClosureLongitude", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCTaskClosureLongitude(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCTaskClosureLongitude_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCTaskClosureLatitude", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCTaskClosureLatitude(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCTaskClosureLatitude_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCLastLocationInfo", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCLastLocationInfo(String value) {
        return new JAXBElement<String>(_TaskResponseMCLastLocationInfo_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCTaskClosureGeoToken", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCTaskClosureGeoToken(String value) {
        return new JAXBElement<String>(_TaskResponseMCTaskClosureGeoToken_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCTaskClosureGeoTokenEntered", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCTaskClosureGeoTokenEntered(String value) {
        return new JAXBElement<String>(_TaskResponseMCTaskClosureGeoTokenEntered_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "URLAuditInfo", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseURLAuditInfo(String value) {
        return new JAXBElement<String>(_TaskResponseURLAuditInfo_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "URLSurvey", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseURLSurvey(String value) {
        return new JAXBElement<String>(_TaskResponseURLSurvey_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCBuildingType", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCBuildingType(String value) {
        return new JAXBElement<String>(_TaskResponseMCBuildingType_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCServicesSignature", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCServicesSignature(String value) {
        return new JAXBElement<String>(_TaskResponseMCServicesSignature_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCPlaca", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCPlaca(String value) {
        return new JAXBElement<String>(_TaskResponseMCPlaca_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCustomerIdentityNumber", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCustomerIdentityNumber(String value) {
        return new JAXBElement<String>(_TaskResponseMCCustomerIdentityNumber_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsDESent", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsDESent(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsDESent_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ReturnVerification", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseReturnVerification(String value) {
        return new JAXBElement<String>(_TaskResponseReturnVerification_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsETASent", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsETASent(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsETASent_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsReminder24HourSent", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsReminder24HourSent(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsReminder24HourSent_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "IsSurveySent", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseIsSurveySent(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseIsSurveySent_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ScheduleLowerBound", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseScheduleLowerBound(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseScheduleLowerBound_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ScheduleUpperBound", scope = TaskResponse.class)
    public JAXBElement<XMLGregorianCalendar> createTaskResponseScheduleUpperBound(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_TaskResponseScheduleUpperBound_QNAME, XMLGregorianCalendar.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "SurveyAnswer", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseSurveyAnswer(String value) {
        return new JAXBElement<String>(_TaskResponseSurveyAnswer_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "SurveyComment", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseSurveyComment(String value) {
        return new JAXBElement<String>(_TaskResponseSurveyComment_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ServiceAccepted", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseServiceAccepted(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseServiceAccepted_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Float }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Float }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCGoogleLatitude", scope = TaskResponse.class)
    public JAXBElement<Float> createTaskResponseMCGoogleLatitude(Float value) {
        return new JAXBElement<Float>(_TaskResponseMCGoogleLatitude_QNAME, Float.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Float }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Float }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCGoogleLongitude", scope = TaskResponse.class)
    public JAXBElement<Float> createTaskResponseMCGoogleLongitude(Float value) {
        return new JAXBElement<Float>(_TaskResponseMCGoogleLongitude_QNAME, Float.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCustomerAssets", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCustomerAssets(String value) {
        return new JAXBElement<String>(_TaskResponseMCCustomerAssets_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCPTWorkingArea", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCPTWorkingArea(String value) {
        return new JAXBElement<String>(_TaskResponseMCPTWorkingArea_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCPTFilter", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCPTFilter(String value) {
        return new JAXBElement<String>(_TaskResponseMCPTFilter_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCTPPolygonFilter", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCTPPolygonFilter(String value) {
        return new JAXBElement<String>(_TaskResponseMCTPPolygonFilter_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "WorkingArea", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseWorkingArea(String value) {
        return new JAXBElement<String>(_TaskResponseWorkingArea_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCDispatchOMSent", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCDispatchOMSent(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCDispatchOMSent_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCDEComment", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCDEComment(String value) {
        return new JAXBElement<String>(_TaskResponseMCDEComment_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCEMAttachments", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCEMAttachments(String value) {
        return new JAXBElement<String>(_TaskResponseMCCEMAttachments_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCContactPhoneNumbers", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCContactPhoneNumbers(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCContactPhoneNumbers_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "TaskAppointmentTime", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseTaskAppointmentTime(String value) {
        return new JAXBElement<String>(_TaskResponseTaskAppointmentTime_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "TaskAppointmentUpdated", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseTaskAppointmentUpdated(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseTaskAppointmentUpdated_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Boolean }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Boolean }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCServicePaid", scope = TaskResponse.class)
    public JAXBElement<Boolean> createTaskResponseMCServicePaid(Boolean value) {
        return new JAXBElement<Boolean>(_TaskResponseMCServicePaid_QNAME, Boolean.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCUnscheduledForNotPaid", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCUnscheduledForNotPaid(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCUnscheduledForNotPaid_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCEMABCancellationNotification", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCCEMABCancellationNotification(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCCEMABCancellationNotification_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCUnscheduledForNotPaidCounter", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCUnscheduledForNotPaidCounter(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCUnscheduledForNotPaidCounter_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCTaskUnpaidReminderSent", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCTaskUnpaidReminderSent(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCTaskUnpaidReminderSent_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCEMNoMsgOnDispatcherReschedule", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCCEMNoMsgOnDispatcherReschedule(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCCEMNoMsgOnDispatcherReschedule_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCIsNonABTaskScheduledNotificationSent", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCIsNonABTaskScheduledNotificationSent(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCIsNonABTaskScheduledNotificationSent_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCNewTaskAssigned", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCNewTaskAssigned(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCNewTaskAssigned_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCWifiCertService", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCWifiCertService(String value) {
        return new JAXBElement<String>(_TaskResponseMCWifiCertService_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCShowCertificateWifiInitiate", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCShowCertificateWifiInitiate(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCShowCertificateWifiInitiate_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCertificateResultWifi", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCertificateResultWifi(String value) {
        return new JAXBElement<String>(_TaskResponseMCCertificateResultWifi_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCertificationActionType", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCertificationActionType(String value) {
        return new JAXBElement<String>(_TaskResponseMCCertificationActionType_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Boolean }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Boolean }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCertWifiGenerateTrigger__c", scope = TaskResponse.class)
    public JAXBElement<Boolean> createTaskResponseMCCertWifiGenerateTriggerC(Boolean value) {
        return new JAXBElement<Boolean>(_TaskResponseMCCertWifiGenerateTriggerC_QNAME, Boolean.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Boolean }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Boolean }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCertificateWifiRequired", scope = TaskResponse.class)
    public JAXBElement<Boolean> createTaskResponseMCCertificateWifiRequired(Boolean value) {
        return new JAXBElement<Boolean>(_TaskResponseMCCertificateWifiRequired_QNAME, Boolean.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCTAPHighFrecuency", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCTAPHighFrecuency(String value) {
        return new JAXBElement<String>(_TaskResponseMCTAPHighFrecuency_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCTAPLowFrecuency", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCTAPLowFrecuency(String value) {
        return new JAXBElement<String>(_TaskResponseMCTAPLowFrecuency_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCSplitterHighFrecuency", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCSplitterHighFrecuency(String value) {
        return new JAXBElement<String>(_TaskResponseMCSplitterHighFrecuency_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCSplitterLowFrecuency", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCSplitterLowFrecuency(String value) {
        return new JAXBElement<String>(_TaskResponseMCSplitterLowFrecuency_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCMHighFrecuency", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCMHighFrecuency(String value) {
        return new JAXBElement<String>(_TaskResponseMCCMHighFrecuency_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCCMLowFrecuency", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseMCCMLowFrecuency(String value) {
        return new JAXBElement<String>(_TaskResponseMCCMLowFrecuency_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "MCResendClosureOM", scope = TaskResponse.class)
    public JAXBElement<Integer> createTaskResponseMCResendClosureOM(Integer value) {
        return new JAXBElement<Integer>(_TaskResponseMCResendClosureOM_QNAME, Integer.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "CodigoTecnico", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseCodigoTecnico(String value) {
        return new JAXBElement<String>(_TaskResponseCodigoTecnico_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ServiceAppointmentID", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseServiceAppointmentID(String value) {
        return new JAXBElement<String>(_TaskResponseServiceAppointmentID_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "AppointmentStatus", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseAppointmentStatus(String value) {
        return new JAXBElement<String>(_TaskResponseAppointmentStatus_QNAME, String.class, TaskResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "", name = "ServiceTerritory", scope = TaskResponse.class)
    public JAXBElement<String> createTaskResponseServiceTerritory(String value) {
        return new JAXBElement<String>(_TaskResponseServiceTerritory_QNAME, String.class, TaskResponse.class, value);
    }

}
