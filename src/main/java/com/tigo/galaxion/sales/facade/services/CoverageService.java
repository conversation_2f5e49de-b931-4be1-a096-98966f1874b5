package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.coverage.CoverageClient;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.response.CoverageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CoverageService {

    private final CoverageClient coverageClient;

    public CoverageResponse getCoverage(String idBarrio, String idCalle, String idCasa) {
        try {
            CoverageResponse response = coverageClient.getCoverage(idBarrio, idCalle, idCasa);

            if (!response.isSuccessful()) {
                log.warn("API externa devolvió un estado no exitoso: {}", response.getMessage());
                return CoverageResponse.builder()
                        .response("Sin cobertura")
                        .message(response.getMessage())
                        .successful(false)
                        .tecnologia("Desconocida")
                        .coordenadaX("0.0")
                        .coordenadaY("0.0")
                        .segmento("No definido")
                        .zona("No definida")
                        .build();
            }

            return response;
        } catch (Exception e) {
            log.error("Error al consultar cobertura", e);
            throw new RuntimeException("Error al consultar cobertura, intenta más tarde.");
        }
    }
}