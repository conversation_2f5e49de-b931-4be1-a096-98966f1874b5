package com.tigo.galaxion.sales.facade.domain.request.multiple_operations;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.tigo.galaxion.sales.facade.soap.multiple_operations.StandardOperation;
import com.tigo.galaxion.sales.facade.soap.multiple_operations.StandardOperations;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor

public class ExecuteMultipleOperationsRequest {

    protected StandardOperations operations;
    protected boolean oneTransaction;
    protected boolean continueOnError;

    public class StandardOperations {

        @XmlElement(name = "Operation")
        protected List<StandardOperation> operation;

        /**
         * Gets the value of the operation property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the operation
         * property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * 
         * <pre>
         * getOperation().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link StandardOperation }
         * 
         * 
         */
        public List<StandardOperation> getOperation() {
            if (operation == null) {
                operation = new ArrayList<StandardOperation>();
            }
            return this.operation;
        }

    }

}
