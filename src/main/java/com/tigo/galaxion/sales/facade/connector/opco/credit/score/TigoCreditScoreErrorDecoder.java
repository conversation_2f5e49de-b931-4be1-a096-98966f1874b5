package com.tigo.galaxion.sales.facade.connector.opco.credit.score;

import com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.problem.TigoCreditScoreProblem;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;
import org.zalando.problem.ThrowableProblem;

@Slf4j
@RequiredArgsConstructor
public class TigoCreditScoreErrorDecoder implements ErrorDecoder {

    @Override
    public Exception decode(String methodKey, Response response) {
        throw new TigoCreditScoreProblem(getThrowableProblem(response));
    }

    private ThrowableProblem getThrowableProblem(Response response) {
        if (StringUtils.isNotBlank(response.body().toString())) {
            log.error(response.body().toString());
        }
        var problemBuilder = Problem
                .builder()
                .withDetail("Error during credit vetting.")
                .withStatus(Status.valueOf(response.status()))
                .withTitle("tigo-credit-scores-service-error");
        return problemBuilder.build();
    }
}
