//
// This file was generated by the Eclipse Implementation of JAXB, v2.3.7 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2024.09.02 at 03:48:22 PM CST 
//


package com.tigo.galaxion.sales.facade.soap.process_task_ex;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for Task complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Task"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Nodo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="Task" type="{http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap}TaskRequest"/&gt;
 *         &lt;element name="ReturnAssignment" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="Status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MCCRMCancellationReason__c" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Task", propOrder = {
    "nodo",
    "task",
    "returnAssignment",
    "status",
    "mccrmCancellationReasonC"
})
public class Task {

    @XmlElement(name = "Nodo")
    protected String nodo;
    @XmlElement(name = "Task", required = true)
    protected TaskRequest task;
    @XmlElement(name = "ReturnAssignment")
    protected boolean returnAssignment;
    @XmlElement(name = "Status", required = true)
    protected String status;
    @XmlElement(name = "MCCRMCancellationReason__c")
    protected String mccrmCancellationReasonC;

    /**
     * Gets the value of the nodo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNodo() {
        return nodo;
    }

    /**
     * Sets the value of the nodo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNodo(String value) {
        this.nodo = value;
    }

    /**
     * Gets the value of the task property.
     * 
     * @return
     *     possible object is
     *     {@link TaskRequest }
     *     
     */
    public TaskRequest getTask() {
        return task;
    }

    /**
     * Sets the value of the task property.
     * 
     * @param value
     *     allowed object is
     *     {@link TaskRequest }
     *     
     */
    public void setTask(TaskRequest value) {
        this.task = value;
    }

    /**
     * Gets the value of the returnAssignment property.
     * 
     */
    public boolean isReturnAssignment() {
        return returnAssignment;
    }

    /**
     * Sets the value of the returnAssignment property.
     * 
     */
    public void setReturnAssignment(boolean value) {
        this.returnAssignment = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the mccrmCancellationReasonC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMCCRMCancellationReasonC() {
        return mccrmCancellationReasonC;
    }

    /**
     * Sets the value of the mccrmCancellationReasonC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMCCRMCancellationReasonC(String value) {
        this.mccrmCancellationReasonC = value;
    }

}
