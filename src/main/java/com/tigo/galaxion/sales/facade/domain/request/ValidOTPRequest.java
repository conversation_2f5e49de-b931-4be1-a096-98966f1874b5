package com.tigo.galaxion.sales.facade.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ValidOTPRequest {

    @NotBlank
    @ApiModelProperty(value = "Phone number to validate.", example = "0800000000", required = true)
    private String msisdn;

    @NotBlank
    @ApiModelProperty(value = "The OTP to validate the phone number.", example = "4YVH67", required = true)
    private String otp;
}
