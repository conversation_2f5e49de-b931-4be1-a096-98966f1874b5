package com.tigo.galaxion.sales.facade.connector.address.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AddressRequest {
    
    private String area;
    private String town;
    private String street;
    private String streetNumber;
    private String code;
    private String county;
    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String poBox;
    private String country;
    private String streetQualifier;
}
