package com.tigo.galaxion.sales.facade.connector.crm_api.workflow_engine_facade;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(value = "workflow-query-service", url = "${environment.url.crm-api-wef-service}", configuration = {WEFErrorDecoder.class})
public interface WEFClient {
    
    @DeleteMapping("/workflow-engine-facade/orders/{orderId}")
    void deleteOrder(@PathVariable("orderId") String orderId);
}
