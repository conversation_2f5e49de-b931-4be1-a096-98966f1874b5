package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Builder
@AllArgsConstructor
@Getter
public class TigoUsageResponse {

    @ApiModelProperty(value = "The code of the usage in the Catalog", example = "ALL_CALLS_ALL_TEXTS", required = true)
    private String catalogCode;

    @ApiModelProperty(value = "The usage description", example = "All national calls included", required = true)
    private String description;

    @ApiModelProperty(value = "The usage's type", example = "SMS", required = true)
    private String type;

    @ApiModelProperty(value = "Display order of the usage. Lowest numbers displayed first", example = "10", required = true)
    private Long displayOrder;

}
