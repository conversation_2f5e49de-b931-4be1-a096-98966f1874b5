package com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TariffPlanDiscountResponse {

    private String description;
    private Long amountVatIncluded;
    private Long amountVatExcluded;
    private String catalogCode;
    private String billingType;
    private Integer occurrence;
    private String discountItemType;
}
