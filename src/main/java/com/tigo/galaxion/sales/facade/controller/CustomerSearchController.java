package com.tigo.galaxion.sales.facade.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.tigo.galaxion.sales.facade.domain.request.CustomerSearchRequest;
import com.tigo.galaxion.sales.facade.domain.response.CustomerSearchResponse;
import com.tigo.galaxion.sales.facade.services.CustomerSearchService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;

import java.util.List;

@Api
@RestController
@RequiredArgsConstructor
public class CustomerSearchController {

    private final CustomerSearchService customerSearchService;

    @GetMapping("/api/v1/contacts/search/{id_type}/{identifier}")
    @ApiOperation("Search an customer.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Customer search."),
    })
    public CustomerSearchResponse getCustomerSearch(
            @ApiParam(value = "Credencial identification type.", required = true, example = "Pasaporte") @PathVariable("id_type") String idtype,
            @ApiParam(value = "Credencial identification number.", required = true, example = "*************") @PathVariable("identifier") String idnumber) {

        return customerSearchService.getCustomerSearch(CustomerSearchRequest
                .builder()
                .type(idtype)
                .identifier(idnumber)
                .build());
    }

    @GetMapping("/api/v1/accounts/search/{id_type}/{identifier}")
    @ApiOperation("Search an customer.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Customer search."),
    })
    public List<CustomerSearchResponse> getCustomerSearchList(
            @ApiParam(value = "Credential identification type.", required = true, example = "Pasaporte") @PathVariable("id_type") String idtype,
            @ApiParam(value = "Credential identification number.", required = true, example = "*************") @PathVariable("identifier") String idnumber) {

        return customerSearchService.getCustomerSearchList(CustomerSearchRequest
                .builder()
                .type(idtype)
                .identifier(idnumber)
                .build());
    }
}
