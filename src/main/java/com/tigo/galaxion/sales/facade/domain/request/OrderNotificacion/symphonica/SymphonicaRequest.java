package com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.symphonica;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SymphonicaRequest {
    private Payload payload;
    private String eventTime;
    private String eventType;
    private String serviceName;

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Payload {
        private String id;
        private String externalId;
        private String priority;
        private String category;
        private String state;
        private String orderDate;
        private String startDate;
        private String completionDate;
        private String source;
        private String executionMode;
        private String traceId;
        private String orderType;
        private List<String> notes;
        private List<String> orderRelationships;
        private List<Error> errors;
        private List<RelatedParty> relatedParty;
        private List<ExtraValue> extraValues;
        private List<OrderItem> orderItems;
        private Integer executionTimeInMillis;
        private String cancellationReason;
        private String cancellationDate;
        private String cancelServiceOrderId;

        @Builder
        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class RelatedParty {
            private String id;
            private String source;
            private String name;
            private String role;
        }

        @Builder
        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class ExtraValue {
            private String name;
            private String value;
        }

        @Builder
        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class OrderItem {
            private String id;
            private String action;
            private String state;
            private List<String> appointments;
            private String processId;
            private List<Error> errors;
            private Service service;

            @Builder
            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            public static class Service {
                private String description;
                private ServiceSpecification serviceSpecification;
                private String category;
                private List<Characteristic> characteristics;
                private String publicIdentifier;

                @Builder
                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                public static class ServiceSpecification {
                    private String id;
                    private String source;
                    private String name;
                    private String code;
                    private String version;
                }

                @Builder
                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                public static class Characteristic {
                    private String name;
                    private String value;
                }
            }
        }

        @Builder
        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Error {
            private String code;
            private String message;
        }
    }
}
