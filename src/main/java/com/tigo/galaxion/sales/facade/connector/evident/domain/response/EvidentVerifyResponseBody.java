package com.tigo.galaxion.sales.facade.connector.evident.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.GenerationResult;
import com.tigo.galaxion.sales.facade.connector.evident.domain.QuestionnaireData;
import com.tigo.galaxion.sales.facade.connector.evident.domain.ValidationResult;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentVerifyResponseBody {

    @JsonProperty("validationResult")
    private String validationResult;

    @JsonProperty("validationMessage")
    private String validationMessage;

    @JsonProperty("validCode")
    private String validCode;

    @JsonProperty("transactionIDOTP")
    private String transactionIDOTP;

}