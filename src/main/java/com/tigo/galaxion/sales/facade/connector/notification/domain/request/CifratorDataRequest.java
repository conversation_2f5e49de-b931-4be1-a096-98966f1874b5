package com.tigo.galaxion.sales.facade.connector.notification.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CifratorDataRequest {
        
    @JsonProperty("orderId")
    private String orderId;

    @JsonProperty("ExpirationDate")
    private String expirationDate;

    @JsonProperty("clientName")
    private String clientName;

    @JsonProperty("clientNumber")
    private String clientNumber;

    @JsonProperty("sellerName")
    private String sellerName;

    @JsonProperty("carpeta")
    private String carpeta;

    @JsonProperty("tipocliente")
    private String tipocliente; 

    @JsonProperty("tipo")
    private String tipo;

    @JsonProperty("portabilidad")
    private String portabilidad; 
}
