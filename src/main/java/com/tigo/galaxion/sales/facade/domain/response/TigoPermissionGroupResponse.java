package com.tigo.galaxion.sales.facade.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoPermissionGroupResponse {

    @ApiModelProperty(value = "The human readable name for this group.", example = "An active customer", required = true)
    private String name;

    @ApiModelProperty(value = "The code of the group.", example = "ACTIVE_CUSTOMER", required = true)
    private String permissionGroup;

    @Builder.Default
    private List<TigoPermissionResponse> permissions = new ArrayList<>();
}
