package com.tigo.galaxion.sales.facade.domain.response;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.RecurringAmountResponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TigoAmountResponse {

    @ApiModelProperty(value = "The one off charge to be paid on the first invoice", example = "9999", required = true)
    private Long oneOffAmount;

    @ApiModelProperty(value = "The discounted one off charge to be paid", example = "3999", required = true)
    private Long discountedOneOffAmount;

    @ApiModelProperty(value = "The up-front charge", example = "4999", required = true)
    private Long upFrontAmount;

    @ApiModelProperty(value = "The discounted up-front charge to be paid", example = "3999", required = true)
    private Long discountedUpFrontAmount;

    private RecurringAmountResponse recurringAmount;
}
