package com.tigo.galaxion.sales.facade.services.retrieval;

import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.domain.response.TigoCrossSellResponse;
import com.tigo.galaxion.sales.facade.mapper.dto.TigoCrossSellResponseMapper;
import com.tigo.galaxion.sales.facade.model.repository.ProspectRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CrossSellRetrievalService {

    private final CrossSellClient crossSellClient;
    private final ProspectRepository prospectRepository;

    @Transactional(readOnly = true)
    public TigoCrossSellResponse getCrossSell(String reference) {
        var crossSellResponse = crossSellClient.getCrossSell(reference);
        var tigoCrossSellResponse = TigoCrossSellResponseMapper.buildTigoCrossSellResponse(crossSellResponse);
        retrieveAndSetContractSignatureOption(tigoCrossSellResponse, reference);
        return tigoCrossSellResponse;
    }

    private void retrieveAndSetContractSignatureOption(TigoCrossSellResponse prospectResponse, String prospectReference) {
        var prospectEntity = prospectRepository.findByReference(prospectReference);
        if (prospectEntity.isPresent()) {
            prospectResponse.setContractSignatureOption(prospectEntity.get().getContractSignatureOption());
        }
    }

}
