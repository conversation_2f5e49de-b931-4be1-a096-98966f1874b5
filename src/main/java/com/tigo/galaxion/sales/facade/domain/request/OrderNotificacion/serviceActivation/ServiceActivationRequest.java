package com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.Valid;
import java.util.List;

/**
 * Represents a request for service activation or deactivation.
 */
@Getter
@Setter
@ToString
public class ServiceActivationRequest {

    /**
     * Activate or Deactivate.
     */
    @NotNull(message = "Transaction type cannot be null.")
    private String transactionType;

    /**
     * Transaction identifier for tracking purposes.
     */
    @NotNull(message = "Transaction ID cannot be null.")
    private String transactionId;


    private String provisioningId;
    
    private String assetId;

    /**
     * Information about the equipment.
     */
    @Valid
    @NotNull(message = "Equipment cannot be null.")
    private Equipment equipment;

    /**
     * Represents the equipment associated with the request.
     */
    @Getter
    @Setter
    @ToString
    public static class Equipment {

        /**
         * Serial number of the equipment in the inventory.
         */
        @NotNull(message = "Inventory serial number cannot be null.")
        private String inventorySerialNumber;

        /**
         * Code of the equipment associated with the asset.
         */
        @NotNull(message = "Code cannot be null.")
        private String code;

        /**
         * Brand of the equipment associated with the asset.
         */
        @NotNull(message = "Brand cannot be null.")
        private String brand;

        /**
         * Reference of the equipment associated with the asset.
         */
        @NotNull(message = "Reference cannot be null.")
        private String reference;

        /**
         * Type of the equipment associated with the asset.
         */
        @NotNull(message = "Type cannot be null.")
        private String type;

        /**
         * Category of the equipment associated with the asset.
         */
        private String category;

        /**
         * List of identifiers associated with the equipment.
         */
        @NotEmpty(message = "Identifiers cannot be empty.")
        @Valid
        private List<Identifier> identifiers;

        /**
         * Represents an identifier associated with the equipment.
         */
        @Getter
        @Setter
        @ToString
        public static class Identifier {

            /**
             * Identifier associated with the equipment.
             */
            @NotNull(message = "Identifier ID cannot be null.")
            private String id;

            /**
             * Type of Identifier associated with the equipment. For example, MAC, serial,
             * smart card ID, etc.
             */
            @NotNull(message = "Identifier type cannot be null.")
            private IdentifierType type;

            /**
             * Enumeration for the type of identifier.
             */
            public enum IdentifierType {
                MAC1, MAC2, SERIAL, SMART_CARD_ID
            }
        }
    }
}
