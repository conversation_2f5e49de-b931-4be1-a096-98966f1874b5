package com.tigo.galaxion.sales.facade.domain.response;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TigoProspectResponse extends AcquisitionProspectResponse {

    @Setter
    private String contractSignatureOption;

}
