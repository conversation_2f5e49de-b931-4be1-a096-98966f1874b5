package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.domain.request.AddOfferToCrossSellCartRequest;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.services.CrossSellOfferService;
import com.tigo.galaxion.sales.facade.services.OfferMsisdnTypeChoiceService;
import com.tigo.galaxion.sales.facade.services.OfferSimDeliveryChoiceService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("/cross-sells/{reference}")
public class CrossSellOfferController {

    private final CrossSellOfferService crossSellOfferService;
    private final OfferSimDeliveryChoiceService offerSimDeliveryChoiceService;
    private final OfferMsisdnTypeChoiceService offerMsisdnTypeChoiceService;

    @PostMapping("/carts/offers")
    public TigoCartResponse addOfferToCart(
            @PathVariable("reference") String reference,
            @Valid @RequestBody AddOfferToCrossSellCartRequest request) {
        return crossSellOfferService.addOffer(reference, request);
    }

    @DeleteMapping("/carts/offers/{offer_id}")
    public TigoCartResponse deleteOffer(
            @PathVariable("reference") String reference,
            @PathVariable("offer_id") long offerId) {
        return crossSellOfferService.deleteOffer(reference, offerId);
    }

    @PutMapping("/carts/offers/{offer_id}/sims/delivery-type")
    public TigoCartResponse updateDeliverySimType(
            @PathVariable("reference") String reference,
            @PathVariable("offer_id") Long offerId,
            @RequestParam String type) {
        return offerSimDeliveryChoiceService.updateForCrossSell(reference, offerId, type);
    }

    @PutMapping("/carts/offers/{offer_id}/msisdn/number-type")
    public TigoCartResponse updateNumberType(
            @PathVariable("reference") String reference,
            @PathVariable("offer_id") Long offerId,
            @RequestParam String type) {
        return offerMsisdnTypeChoiceService.updateForCrossSell(reference, offerId, type);
    }
}
