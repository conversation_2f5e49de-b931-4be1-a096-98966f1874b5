package com.tigo.galaxion.sales.facade.domain.problem;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class InvalidHandsetNumberProblem extends AbstractThrowableProblem {

    public InvalidHandsetNumberProblem(int number) {
        super(null,
              "invalid-handset-number",
              Status.BAD_REQUEST,
              String.format("Offer should have only one handset, but found %d", number)
        );
    }

}
