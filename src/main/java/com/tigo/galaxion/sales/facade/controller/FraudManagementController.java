package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.services.FraudManagementService;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.FraudsRequestBody;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.response.FraudsResponseBody;

import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/fraud-management")
public class FraudManagementController {

    private final FraudManagementService fraudService;

    @PostMapping("/frauds")
    @ApiOperation("This interface will be consulted if existing frauds.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Address Normalized."),
    })
    public FraudsResponseBody frauds(@Valid @RequestBody FraudsRequestBody request) {
        return fraudService.frauds(request);
    }

}
