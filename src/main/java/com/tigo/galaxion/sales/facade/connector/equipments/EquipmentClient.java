package com.tigo.galaxion.sales.facade.connector.equipments;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.tigo.galaxion.sales.facade.connector.equipments.domain.Equipment;
import com.tigo.galaxion.sales.facade.connector.equipments.domain.EquipmentCpe;

@FeignClient(value = "equipment-service", url = "${environment.url.equipment-service}", configuration = EquipmentErrorDecoder.class)
public interface EquipmentClient {

	/**
	 * Equipment
	 */
	@GetMapping("/api/v1/equipments/equipment/orders/{orderId}/services/{serviceId}")
	Equipment getEquipmentByServiceIdAndOrderId(@PathVariable("orderId") String orderId, @PathVariable("serviceId") Integer serviceId);

	@PostMapping("/api/v1/equipments/equipment")
	Equipment addEquipment(@RequestBody Equipment equipment);

	/**
	 * Equipment CPE
	 */
	@PostMapping("/api/v1/equipments/equipment/{equipmentId}/cpe")
	EquipmentCpe addEquipmentCPE(@PathVariable("equipmentId") String equipmentId, @RequestBody EquipmentCpe equipment);

	@GetMapping("/api/v1/equipments/cpe/{equipmentId}")
	EquipmentCpe getEquipmentByServiceIdAndOrderId(@PathVariable("equipmentId") String equipmentId);

}
