package com.tigo.galaxion.sales.facade.connector.evident.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tigo.galaxion.sales.facade.connector.evident.domain.ExpeditionDate;
import com.tigo.galaxion.sales.facade.connector.evident.domain.Identification;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EvidentValidationRequestBody {
       
    @JsonProperty("identification")
    private Identification identification;

    @JsonProperty("surname")
    private String surname;

    @JsonProperty("names")
    private String names;

    @JsonProperty("expeditionDate")
    private ExpeditionDate expeditionDate;

    @JsonProperty("onlyQuestionnaire")
    private String onlyQuestionnaire;

}
