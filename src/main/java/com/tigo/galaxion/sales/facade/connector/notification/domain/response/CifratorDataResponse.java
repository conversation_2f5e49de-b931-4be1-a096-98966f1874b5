package com.tigo.galaxion.sales.facade.connector.notification.domain.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CifratorDataResponse {
    @JsonProperty("response")
    private String response;

    @JsonProperty("message")
    private CifratorDataMessageResponse message;

    @JsonProperty("successful")
    private boolean successful;

    @JsonProperty("link")
    private String link;
    
}
