<wsdl:definitions targetNamespace="http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:sch="http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap">
  <wsdl:types>
    <xs:schema elementFormDefault="unqualified" targetNamespace="http://crmsaleforce.resourcemanager.millicom.com/processtaskexsoap" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="ProcessTaskExRequest">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="ProcessTaskEx" type="tns:Task"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:complexType name="Task">
        <xs:sequence>
          <xs:element minOccurs="0" name="Nodo" type="xs:string"/>
          <xs:element name="Task" type="tns:TaskRequest"/>
          <xs:element name="ReturnAssignment" type="xs:boolean"/>
          <xs:element name="Status" type="xs:string"/>
          <xs:element minOccurs="0" name="MCCRMCancellationReason__c" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TaskRequest">
        <xs:sequence>
          <xs:element name="CallID" type="xs:string"/>
          <xs:element name="Number" type="xs:int"/>
          <xs:element name="Priority" type="xs:int"/>
          <xs:element name="Duration" type="xs:int"/>
          <xs:element name="EarlyStart" type="xs:date"/>
          <xs:element name="LateStart" type="xs:date"/>
          <xs:element name="DueDate" type="xs:date"/>
          <xs:element name="OpenDate" type="xs:date"/>
          <xs:element name="Area" type="xs:string"/>
          <xs:element name="Region" type="tns:Region"/>
          <xs:element name="District" type="xs:string"/>
          <xs:element name="Street" type="xs:string"/>
          <xs:element name="City" type="xs:string"/>
          <xs:element name="MCState" type="xs:string"/>
          <xs:element name="CountryID" type="xs:string"/>
          <xs:element name="TDRequired" type="xs:boolean"/>
          <xs:element minOccurs="0" name="RequiredCrewSize" type="xs:int"/>
          <xs:element minOccurs="0" name="NumberOfRequiredEngineers" type="xs:int"/>
          <xs:element name="TaskTypeCategory" type="xs:string"/>
          <xs:element name="TaskType" type="xs:string"/>
          <xs:element minOccurs="0" name="MCComment" type="xs:string"/>
          <xs:element minOccurs="0" name="MCCRMComment" type="xs:string"/>
          <xs:element minOccurs="0" name="MCContactEmail" type="xs:string"/>
          <xs:element name="MCCustomerCode" type="xs:string"/>
          <xs:element minOccurs="0" name="MCCustomerPhoneNumber" type="xs:int"/>
          <xs:element minOccurs="0" name="MCStatusFCVToken" type="xs:string"/>
          <xs:element name="Customer" type="xs:string"/>
          <xs:element minOccurs="0" name="ContactPhoneNumber" type="xs:int"/>
          <xs:element name="MCWorkPackageDescription" type="xs:string"/>
          <xs:element name="MCConnectionData" type="xs:string"/>
          <xs:element name="MCBillingAccountInfo" type="xs:string"/>
          <xs:element name="AppointmentStart" type="xs:date"/>
          <xs:element name="AppointmentFinish" type="xs:date"/>
          <xs:element name="Latitude" type="xs:string"/>
          <xs:element name="Longitude" type="xs:string"/>
          <xs:element minOccurs="0" name="MCMVServices" type="tns:MCServices"/>
          <xs:element minOccurs="0" name="MCMVAssets" type="tns:MCMVAssets"/>
          <xs:element minOccurs="0" name="ContactName" type="xs:string"/>
          <xs:element minOccurs="0" name="MCInfoCustomerSite" type="xs:string"/>
          <xs:element minOccurs="0" name="MCOpeningReason" type="xs:string"/>
          <xs:element minOccurs="0" name="ContractType" type="xs:string"/>
          <xs:element minOccurs="0" name="MCCustomerClass" type="xs:string"/>
          <xs:element minOccurs="0" name="MCServicePaid" type="xs:int"/>
          <xs:element minOccurs="0" name="MCCustomerIdentityNumber" type="xs:string"/>
          <xs:element minOccurs="0" name="MCConfirmationStatus" type="xs:string"/>
          <xs:element minOccurs="0" name="MCCustomsSeal" type="xs:string"/>
          <xs:element minOccurs="0" name="MCRecurrentClient" type="xs:string"/>
          <xs:element name="MCTap" type="xs:string"/>
          <xs:element name="MCBoca" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Region">
        <xs:sequence>
          <xs:element name="Name" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCServices">
        <xs:sequence>
          <xs:element maxOccurs="unbounded" name="MCService" type="tns:MCService"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCService">
        <xs:sequence>
          <xs:element name="ExternalRefID" type="xs:string"/>
          <xs:element name="ServiceName" type="xs:string"/>
          <xs:element name="MCAdditionalData" type="xs:string"/>
          <xs:element name="Code" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCMVAssets">
        <xs:sequence>
          <xs:element maxOccurs="unbounded" name="MCAsset" type="tns:MCAsset"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCAsset">
        <xs:sequence>
          <xs:element name="ExternalRefID" type="xs:string"/>
          <xs:element name="AssetName" type="xs:string"/>
          <xs:element name="Description" type="xs:string"/>
          <xs:element name="SerialNumber" type="xs:string"/>
          <xs:element name="MaterialType" type="tns:MaterialType"/>
          <xs:element name="Service" type="tns:Service"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MaterialType">
        <xs:sequence>
          <xs:element name="MaterialName" type="xs:string"/>
          <xs:element name="MaterialCode" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Service">
        <xs:sequence>
          <xs:element name="ExternalRefID" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ProcessTaskExResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Task" type="tns:TaskResponse"/>
            <xs:element name="MessageError" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:complexType name="TaskResponse">
        <xs:sequence>
          <xs:element name="Key" type="xs:int"/>
          <xs:element name="Revision" type="xs:int"/>
          <xs:element name="Stamp" type="tns:Stamp"/>
          <xs:element name="CallID" type="xs:string"/>
          <xs:element name="Number" type="xs:int"/>
          <xs:element name="EarlyStart" type="xs:date"/>
          <xs:element name="DueDate" type="xs:date"/>
          <xs:element name="LateStart" type="xs:date"/>
          <xs:element name="Priority" type="xs:int"/>
          <xs:element name="Status" type="xs:string"/>
          <xs:element name="Customer" type="xs:string"/>
          <xs:element name="Calendar" type="xs:string"/>
          <xs:element name="Region" type="xs:string"/>
          <xs:element name="District" type="xs:string"/>
          <xs:element name="Postcode" type="xs:string"/>
          <xs:element name="PreferredEngineers" type="xs:string"/>
          <xs:element name="ContractType" type="xs:string"/>
          <xs:element name="OpenDate" type="xs:date"/>
          <xs:element name="ContactDate" type="xs:date"/>
          <xs:element name="ConfirmationDate" type="xs:date"/>
          <xs:element name="TaskType" type="xs:string"/>
          <xs:element name="Duration" type="xs:int"/>
          <xs:element name="RequiredEngineers" type="xs:string"/>
          <xs:element name="NumberOfRequiredEngineers" type="xs:int"/>
          <xs:element name="RequiredSkills1" type="tns:TaskRequiredSkill1"/>
          <xs:element name="RequiredSkills2" type="xs:string"/>
          <xs:element name="EngineerType" type="xs:string"/>
          <xs:element name="RequiredEngineerTools" type="xs:string"/>
          <xs:element name="Critical" type="xs:int"/>
          <xs:element name="TimeDependencies" type="xs:string"/>
          <xs:element name="EngineerDependencies" type="xs:string"/>
          <xs:element name="AppointmentStart" type="xs:date"/>
          <xs:element name="AppointmentFinish" type="xs:date"/>
          <xs:element name="ContactName" type="xs:string"/>
          <xs:element name="ContactPhoneNumber" type="xs:int"/>
          <xs:element name="BinaryData" type="xs:string"/>
          <xs:element name="Latitude" type="xs:int"/>
          <xs:element name="Longitude" type="xs:int"/>
          <xs:element name="GISDataSource" type="xs:int"/>
          <xs:element name="Street" type="xs:string"/>
          <xs:element name="City" type="xs:string"/>
          <xs:element name="MCState" type="xs:string"/>
          <xs:element name="State" type="xs:string"/>
          <xs:element name="TaskStatusContext" type="xs:int"/>
          <xs:element name="IsCrewTask" type="xs:int"/>
          <xs:element name="CountryID" type="xs:string"/>
          <xs:element name="EngineerRequirements" type="xs:string"/>
          <xs:element name="IsScheduled" type="xs:int"/>
          <xs:element name="CustomerEmail" type="xs:int"/>
          <xs:element name="ExcludedEngineers" type="xs:int"/>
          <xs:element name="RequiredCrewSize" type="xs:int"/>
          <xs:element name="InJeopardy" type="xs:int"/>
          <xs:element name="Pinned" type="xs:int"/>
          <xs:element name="JeopardyState" type="xs:int"/>
          <xs:element name="DisplayStatus" type="xs:int"/>
          <xs:element name="DispatchDate" type="xs:date"/>
          <xs:element name="ScheduleDate" type="xs:date"/>
          <xs:element name="DisplayDate" type="xs:date"/>
          <xs:element name="OnSiteDate" type="xs:date"/>
          <xs:element name="Comment" type="xs:string"/>
          <xs:element name="CustomerReference" type="xs:string"/>
          <xs:element name="StateSubdivision" type="xs:string"/>
          <xs:element name="CitySubdivision" type="xs:string"/>
          <xs:element name="Team" type="xs:string"/>
          <xs:element name="Signature" type="xs:string"/>
          <xs:element name="ExternalRefID" type="xs:string"/>
          <xs:element name="PartsUsed" type="xs:string"/>
          <xs:element name="Assets" type="xs:string"/>
          <xs:element name="BackReportings" type="xs:string"/>
          <xs:element name="TaskTypeCategory" type="xs:string"/>
          <xs:element name="WorkOrderItem" type="xs:string"/>
          <xs:element name="User_CustomerAccount" type="xs:string"/>
          <xs:element name="IsAppointment" type="xs:int"/>
          <xs:element name="TravelDate" type="xs:date"/>
          <xs:element name="CompletionDate" type="xs:date"/>
          <xs:element name="Attachments" type="xs:string"/>
          <xs:element name="CancellationDate" type="xs:date"/>
          <xs:element name="IsMegatask" type="xs:int"/>
          <xs:element name="IsBundled" type="xs:int"/>
          <xs:element name="IsManuallyBundled" type="xs:int"/>
          <xs:element name="Megatask" type="xs:string"/>
          <xs:element name="MegataskPureDuration" type="xs:int"/>
          <xs:element name="BundlerConfiguration" type="xs:string"/>
          <xs:element name="Subtasks" type="xs:string"/>
          <xs:element name="SkillsDuration" type="xs:string"/>
          <xs:element name="CustomerAccount" type="xs:string"/>
          <xs:element name="Area" type="xs:string"/>
          <xs:element name="IncompleteReason" type="xs:string"/>
          <xs:element name="MCWorkPackageDescription" type="xs:string"/>
          <xs:element name="MCComment" type="xs:string"/>
          <xs:element minOccurs="0" name="MCCRMComment" type="xs:string"/>
          <xs:element name="MCContactEmail" type="xs:string"/>
          <xs:element name="MCCustomerCode" type="xs:int"/>
          <xs:element name="MCCustomerPhoneNumber" type="xs:int"/>
          <xs:element name="MCSaldoPending" type="xs:string"/>
          <xs:element name="MCBillingAccountInfo" type="xs:string"/>
          <xs:element name="MCConnectionData" type="xs:string"/>
          <xs:element name="LastRejectedEngineer" type="xs:string"/>
          <xs:element name="RejectedDate" type="xs:date"/>
          <xs:element name="RejectionReason" type="xs:string"/>
          <xs:element name="CancellationReason" type="xs:string"/>
          <xs:element name="MCConfirmationStatus" type="xs:string"/>
          <xs:element name="MCMVPartsRequired" type="xs:string"/>
          <xs:element name="MCMVMaterialUsed" type="xs:string"/>
          <xs:element name="MCMVEquipmentUsed" type="xs:string"/>
          <xs:element name="MCMVEquipmentCollected" type="xs:int"/>
          <xs:element name="MCNoMaterialUsed" type="xs:int"/>
          <xs:element name="MCNoEquipmentUsed" type="xs:int"/>
          <xs:element name="MCCauseReason" type="xs:string"/>
          <xs:element name="MCLaboresUsed" type="xs:string"/>
          <xs:element name="MCCustomsSeal" type="xs:string"/>
          <xs:element name="MCCodeBobina1" type="xs:string"/>
          <xs:element name="MCCodeBobina2" type="xs:string"/>
          <xs:element name="MCMVServices" type="xs:string"/>
          <xs:element name="DynamicPriority" type="xs:int"/>
          <xs:element name="ExternalRefIDExtension" type="xs:int"/>
          <xs:element name="ServiceSummary" type="xs:string"/>
          <xs:element name="StreetSmartJob" type="xs:int"/>
          <xs:element name="RecurrenceTask" type="xs:string"/>
          <xs:element name="RTIsRecurringTask" type="xs:int"/>
          <xs:element name="RTUpdateAllRecurrences" type="xs:int"/>
          <xs:element name="RTIsPrime" type="xs:int"/>
          <xs:element name="ArchiveStatus" type="xs:string"/>
          <xs:element name="AssignedEngineerName" type="xs:string"/>
          <xs:element name="MobileKey" type="xs:string"/>
          <xs:element name="Unit" type="xs:string"/>
          <xs:element name="SupervisorStatusGroup" type="xs:string"/>
          <xs:element name="IsSingleTask" type="xs:int"/>
          <xs:element name="FieldCommentEng" type="xs:string"/>
          <xs:element name="MCCustomerClass" type="xs:string"/>
          <xs:element name="MCOpeningReason" type="xs:string"/>
          <xs:element name="MCZonaRamal" type="xs:string"/>
          <xs:element name="MCTap" type="xs:string"/>
          <xs:element name="MCBoca" type="xs:string"/>
          <xs:element name="MCInfoCustomerSite" type="xs:string"/>
          <xs:element name="MCTaskClosureLongitude" type="xs:int"/>
          <xs:element name="MCTaskClosureLatitude" type="xs:int"/>
          <xs:element name="MCTaskClosureGeoToken" type="xs:string"/>
          <xs:element name="MCTaskClosureGeoTokenEntered" type="xs:string"/>
          <xs:element name="URLAuditInfo" type="xs:string"/>
          <xs:element name="URLSurvey" type="xs:string"/>
          <xs:element name="MCBuildingType" type="xs:string"/>
          <xs:element name="MCServicesSignature" type="xs:string"/>
          <xs:element name="MCPlaca" type="xs:string"/>
          <xs:element name="MCCustomerIdentityNumber" type="xs:string"/>
          <xs:element name="IsDESent" type="xs:int"/>
          <xs:element name="ReturnVerification" type="xs:string"/>
          <xs:element name="IsETASent" type="xs:int"/>
          <xs:element name="IsReminder24HourSent" type="xs:int"/>
          <xs:element name="IsSurveySent" type="xs:int"/>
          <xs:element name="ScheduleLowerBound" type="xs:date"/>
          <xs:element name="ScheduleUpperBound" type="xs:date"/>
          <xs:element name="SurveyAnswer" type="xs:string"/>
          <xs:element name="SurveyComment" type="xs:string"/>
          <xs:element name="ServiceAccepted" type="xs:int"/>
          <xs:element name="MCGoogleLatitude" type="xs:float"/>
          <xs:element name="MCGoogleLongitude" type="xs:float"/>
          <xs:element name="MCCustomerAssets" type="xs:string"/>
          <xs:element name="MCPTWorkingArea" type="xs:string"/>
          <xs:element name="MCPTFilter" type="xs:string"/>
          <xs:element name="MCTPPolygonFilter" type="xs:string"/>
          <xs:element name="WorkingArea" type="xs:string"/>
          <xs:element name="MCDispatchOMSent" type="xs:int"/>
          <xs:element name="MCDEComment" type="xs:string"/>
          <xs:element name="MCCEMAttachments" type="xs:string"/>
          <xs:element name="MCContactPhoneNumbers" type="xs:int"/>
          <xs:element name="TaskAppointmentTime" type="xs:string"/>
          <xs:element name="TaskAppointmentUpdated" type="xs:int"/>
          <xs:element name="MCServicePaid" type="xs:int"/>
          <xs:element name="MCUnscheduledForNotPaid" type="xs:int"/>
          <xs:element name="MCCEMABCancellationNotification" type="xs:int"/>
          <xs:element name="MCUnscheduledForNotPaidCounter" type="xs:int"/>
          <xs:element name="MCTaskUnpaidReminderSent" type="xs:int"/>
          <xs:element name="MCCEMNoMsgOnDispatcherReschedule" type="xs:int"/>
          <xs:element name="MCIsNonABTaskScheduledNotificationSent" type="xs:int"/>
          <xs:element name="MCNewTaskAssigned" type="xs:int"/>
          <xs:element name="MCWifiCertService" type="xs:string"/>
          <xs:element name="MCShowCertificateWifiInitiate" type="xs:int"/>
          <xs:element name="MCCertificateResultWifi" type="xs:string"/>
          <xs:element name="MCCertificationActionType" type="xs:string"/>
          <xs:element name="MCGenerateCertificateWifi" type="xs:int"/>
          <xs:element name="MCCertificateWifiRequired" type="xs:int"/>
          <xs:element name="MCTAPHighFrecuency" type="xs:string"/>
          <xs:element name="MCTAPLowFrecuency" type="xs:string"/>
          <xs:element name="MCSplitterHighFrecuency" type="xs:string"/>
          <xs:element name="MCSplitterLowFrecuency" type="xs:string"/>
          <xs:element name="MCCMHighFrecuency" type="xs:string"/>
          <xs:element name="MCCMLowFrecuency" type="xs:string"/>
          <xs:element name="MCResendClosureOM" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Stamp">
        <xs:sequence>
          <xs:element name="CreatedBy" type="xs:string"/>
          <xs:element name="TimeCreated" type="xs:date"/>
          <xs:element name="CreatingProcess" type="xs:int"/>
          <xs:element name="ModifiedBy" type="xs:string"/>
          <xs:element name="TimeModified" type="xs:date"/>
          <xs:element name="ModifyingProcess" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RequiredSkills1">
        <xs:sequence>
          <xs:element name="TaskRequiredSkill1" type="tns:TaskRequiredSkill1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TaskRequiredSkill1">
        <xs:sequence>
          <xs:element name="Key" type="xs:string"/>
          <xs:element name="SkillLevel" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="ProcessTaskExResponse">
    <wsdl:part element="tns:ProcessTaskExResponse" name="ProcessTaskExResponse"/>
  </wsdl:message>
  <wsdl:message name="ProcessTaskExRequest">
    <wsdl:part element="tns:ProcessTaskExRequest" name="ProcessTaskExRequest"/>
  </wsdl:message>
  <wsdl:portType name="processTaskExPort">
    <wsdl:operation name="ProcessTaskEx">
      <wsdl:input message="tns:ProcessTaskExRequest" name="ProcessTaskExRequest"/>
      <wsdl:output message="tns:ProcessTaskExResponse" name="ProcessTaskExResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="processTaskExPortSoap11" type="tns:processTaskExPort">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="ProcessTaskEx">
      <soap:operation soapAction=""/>
      <wsdl:input name="ProcessTaskExRequest">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="ProcessTaskExResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="processTaskExPortService">
    <wsdl:port binding="tns:processTaskExPortSoap11" name="processTaskExPortSoap11">
      <soap:address location="http://fs-ar2-test.fsmillicom.com:8080/wrapper/soapWS/processTaskEx"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>