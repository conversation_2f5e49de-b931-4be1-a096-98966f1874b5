<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://crmsaleforce.resourcemanager.millicom.com/gettasksoap" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:sch="http://crmsaleforce.resourcemanager.millicom.com/gettasksoap" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://crmsaleforce.resourcemanager.millicom.com/gettasksoap">
  <wsdl:types>
    <xs:schema elementFormDefault="unqualified" targetNamespace="http://crmsaleforce.resourcemanager.millicom.com/gettasksoap" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="GtTaskRequest">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="GtTask" type="tns:Task"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:complexType name="Task">
        <xs:sequence>
          <xs:element name="Task" type="tns:TaskRequest"/>
          <xs:element name="GetAssignment" type="xs:boolean" nillable="true"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TaskRequest">
        <xs:sequence>
          <xs:element name="CallID" type="xs:string"/>
          <xs:element name="Number" type="xs:int" nillable="true"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="GtTaskResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Task" type="tns:TaskResponse"/>
            <xs:element name="MessageError" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:complexType name="TaskResponse">
        <xs:sequence>
          <xs:element name="Key" type="xs:int"/>
          <xs:element name="Revision" type="xs:int"/>
          <xs:element name="Stamp" type="tns:Stamp"/>
          <xs:element name="CallID" type="xs:string"/>
          <xs:element name="Number" type="xs:int"/>
          <xs:element name="EarlyStart" type="xs:date"/>
          <xs:element name="DueDate" type="xs:date"/>
          <xs:element name="LateStart" type="xs:date"/>
          <xs:element name="Priority" type="xs:string"/>
          <xs:element name="Status" type="xs:string"/>
          <xs:element name="Customer" type="xs:string"/>
          <xs:element name="Calendar" type="xs:string"/>
          <xs:element name="Region" type="xs:string"/>
          <xs:element name="District" type="xs:string"/>
          <xs:element name="Postcode" type="xs:string"/>
          <xs:element name="PreferredEngineers" type="xs:string"/>
          <xs:element name="ContractType" type="xs:string"/>
          <xs:element name="OpenDate" type="xs:date"/>
          <xs:element name="ContactDate" type="xs:date"/>
          <xs:element name="ConfirmationDate" type="xs:date"/>
          <xs:element name="TaskType" type="xs:string"/>
          <xs:element name="Duration" type="xs:double"/>
          <xs:element name="RequiredEngineers" type="xs:string"/>
          <xs:element name="NumberOfRequiredEngineers" type="xs:int"/>
          <xs:element name="RequiredSkills1" type="tns:TaskRequiredSkill1"/>
          <xs:element name="RequiredSkills2" type="xs:string"/>
          <xs:element name="EngineerType" type="xs:string"/>
          <xs:element name="RequiredEngineerTools" type="xs:string"/>
          <xs:element name="Critical" type="xs:int"/>
          <xs:element name="TimeDependencies" type="xs:string"/>
          <xs:element name="EngineerDependencies" type="xs:string"/>
          <xs:element name="AppointmentStart" type="xs:date"/>
          <xs:element name="AppointmentFinish" type="xs:date"/>
          <xs:element name="ContactName" type="xs:string"/>
          <xs:element name="ContactPhoneNumber" type="xs:int"/>
          <xs:element name="BinaryData" type="xs:string"/>
          <xs:element name="Latitude" type="xs:double"/>
          <xs:element name="Longitude" type="xs:double"/>
          <xs:element name="GISDataSource" type="xs:int"/>
          <xs:element name="Street" type="xs:string"/>
          <xs:element name="City" type="xs:string"/>
          <xs:element name="MCState" type="xs:string"/>
          <xs:element name="State" type="xs:string"/>
          <xs:element name="TaskStatusContext" type="xs:int"/>
          <xs:element name="IsCrewTask" type="xs:int"/>
          <xs:element name="CountryID" type="xs:string"/>
          <xs:element name="EngineerRequirements" type="xs:string"/>
          <xs:element name="IsScheduled" type="xs:int"/>
          <xs:element name="CustomerEmail" type="xs:int"/>
          <xs:element name="ExcludedEngineers" type="xs:int"/>
          <xs:element name="RequiredCrewSize" type="xs:int"/>
          <xs:element name="InJeopardy" type="xs:int"/>
          <xs:element name="Pinned" type="xs:int"/>
          <xs:element name="JeopardyState" type="xs:int"/>
          <xs:element name="DisplayStatus" type="xs:string"/>
          <xs:element name="DispatchDate" type="xs:date"/>
          <xs:element name="ScheduleDate" type="xs:date"/>
          <xs:element name="DisplayDate" type="xs:date"/>
          <xs:element name="OnSiteDate" type="xs:date"/>
          <xs:element name="Comment" type="xs:string"/>
          <xs:element name="CustomerReference" type="xs:string"/>
          <xs:element name="StateSubdivision" type="xs:string"/>
          <xs:element name="CitySubdivision" type="xs:string"/>
          <xs:element name="Team" type="xs:string"/>
          <xs:element name="Signature" type="xs:string"/>
          <xs:element name="ExternalRefID" type="xs:string"/>
          <xs:element name="PartsUsed" type="xs:string"/>
          <xs:element name="Assets" type="xs:string"/>
          <xs:element name="BackReportings" type="xs:string"/>
          <xs:element name="TaskTypeCategory" type="xs:string"/>
          <xs:element name="WorkOrderItem" type="xs:string"/>
          <xs:element name="User_CustomerAccount" type="tns:CustomerAccount"/>
          <xs:element name="IsAppointment" type="xs:int"/>
          <xs:element name="TravelDate" type="xs:date"/>
          <xs:element name="CompletionDate" type="xs:date"/>
          <xs:element name="Attachments" type="xs:string"/>
          <xs:element name="CancellationDate" type="xs:date"/>
          <xs:element name="IsMegatask" type="xs:int"/>
          <xs:element name="IsBundled" type="xs:int"/>
          <xs:element name="IsManuallyBundled" type="xs:int"/>
          <xs:element name="Megatask" type="xs:string"/>
          <xs:element name="MegataskPureDuration" type="xs:int"/>
          <xs:element name="BundlerConfiguration" type="xs:string"/>
          <xs:element name="Subtasks" type="xs:string"/>
          <xs:element name="SkillsDuration" type="xs:string"/>
          <xs:element name="CustomerAccount" type="xs:string"/>
          <xs:element name="Area" type="xs:string"/>
          <xs:element name="IncompleteReason" type="xs:string"/>
          <xs:element name="MCWorkPackageDescription" type="xs:string"/>
          <xs:element name="MCComment" type="xs:string"/>
          <xs:element name="MCCRMComment" type="xs:string"/>
          <xs:element name="MCContactEmail" type="xs:string"/>
          <xs:element name="MCCustomerCode" type="xs:int"/>
          <xs:element name="MCCustomerPhoneNumber" type="xs:int"/>
          <xs:element name="MCSaldoPending" type="xs:double"/>
          <xs:element name="MCBillingAccountInfo" type="xs:string"/>
          <xs:element name="MCConnectionData" type="xs:string"/>
          <xs:element name="LastRejectedEngineer" type="xs:string"/>
          <xs:element name="RejectedDate" type="xs:date"/>
          <xs:element name="RejectionReason" type="xs:string"/>
          <xs:element name="CancellationReason" type="xs:string"/>
          <xs:element name="MCConfirmationStatus" type="xs:string"/>
          <xs:element name="MCMVPartsRequired" type="xs:string"/>
          <xs:element name="MCMVMaterialUsed" type="tns:MCMVMaterialUsedKey"/>
          <xs:element name="MCMVEquipmentUsed" type="tns:MCMVEquipmentUsedKey"/>
          <xs:element name="MCMVEquipmentCollected" type="tns:MCMVEquipmentCollectedKey"/>
          <xs:element name="MCNoMaterialUsed" type="xs:int"/>
          <xs:element name="MCNoEquipmentUsed" type="xs:int"/>
          <xs:element name="MCCauseReason" type="xs:string"/>
          <xs:element name="MCLaboresUsed" type="tns:MCLaboresUsedKey"/>
          <xs:element name="MCCustomsSeal" type="xs:string"/>
          <xs:element name="MCCodeBobina1" type="xs:string"/>
          <xs:element name="MCCodeBobina2" type="xs:string"/>
          <xs:element name="MCMVServices" type="tns:MCServices"/>
          <xs:element name="DynamicPriority" type="xs:int"/>
          <xs:element name="ExternalRefIDExtension" type="xs:int"/>
          <xs:element name="ServiceSummary" type="xs:string"/>
          <xs:element name="StreetSmartJob" type="xs:int"/>
          <xs:element name="RecurrenceTask" type="xs:string"/>
          <xs:element name="RTIsRecurringTask" type="xs:int"/>
          <xs:element name="RTUpdateAllRecurrences" type="xs:int"/>
          <xs:element name="RTIsPrime" type="xs:int"/>
          <xs:element name="ArchiveStatus" type="xs:string"/>
          <xs:element name="AssignedEngineerName" type="xs:string"/>
          <xs:element name="MobileKey" type="xs:string"/>
          <xs:element name="Unit" type="xs:string"/>
          <xs:element name="SupervisorStatusGroup" type="xs:string"/>
          <xs:element name="IsSingleTask" type="xs:int"/>
          <xs:element name="FieldCommentEng" type="xs:string"/>
          <xs:element name="MCCustomerClass" type="xs:string"/>
          <xs:element name="MCOpeningReason" type="xs:string"/>
          <xs:element name="MCZonaRamal" type="xs:string"/>
          <xs:element name="MCTap" type="xs:string"/>
          <xs:element name="MCBoca" type="xs:string"/>
          <xs:element name="MCInfoCustomerSite" type="xs:string"/>
          <xs:element name="MCTaskClosureLongitude" type="xs:int"/>
          <xs:element name="MCTaskClosureLatitude" type="xs:int"/>
          <xs:element name="MCLastLocationInfo" type="xs:string"/>
          <xs:element name="MCTaskClosureGeoToken" type="xs:string"/>
          <xs:element name="MCTaskClosureGeoTokenEntered" type="xs:string"/>
          <xs:element name="URLAuditInfo" type="xs:string"/>
          <xs:element name="URLSurvey" type="xs:string"/>
          <xs:element name="MCBuildingType" type="xs:string"/>
          <xs:element name="MCServicesSignature" type="xs:string"/>
          <xs:element name="MCPlaca" type="xs:string"/>
          <xs:element name="MCCustomerIdentityNumber" type="xs:string"/>
          <xs:element name="IsDESent" type="xs:int"/>
          <xs:element name="ReturnVerification" type="xs:string"/>
          <xs:element name="IsETASent" type="xs:int"/>
          <xs:element name="IsReminder24HourSent" type="xs:int"/>
          <xs:element name="IsSurveySent" type="xs:int"/>
          <xs:element name="ScheduleLowerBound" type="xs:date"/>
          <xs:element name="ScheduleUpperBound" type="xs:date"/>
          <xs:element name="SurveyAnswer" type="xs:string"/>
          <xs:element name="SurveyComment" type="xs:string"/>
          <xs:element name="ServiceAccepted" type="xs:int"/>
          <xs:element name="MCGoogleLatitude" type="xs:float"/>
          <xs:element name="MCGoogleLongitude" type="xs:float"/>
          <xs:element name="MCCustomerAssets" type="xs:string"/>
          <xs:element name="MCPTWorkingArea" type="xs:string"/>
          <xs:element name="MCPTFilter" type="xs:string"/>
          <xs:element name="MCTPPolygonFilter" type="xs:string"/>
          <xs:element name="WorkingArea" type="xs:string"/>
          <xs:element name="MCDispatchOMSent" type="xs:int"/>
          <xs:element name="MCDEComment" type="xs:string"/>
          <xs:element name="MCCEMAttachments" type="xs:string"/>
          <xs:element name="MCContactPhoneNumbers" type="xs:int"/>
          <xs:element name="TaskAppointmentTime" type="xs:string"/>
          <xs:element name="TaskAppointmentUpdated" type="xs:int"/>
          <xs:element name="MCServicePaid" type="xs:boolean"/>
          <xs:element name="MCUnscheduledForNotPaid" type="xs:int"/>
          <xs:element name="MCCEMABCancellationNotification" type="xs:int"/>
          <xs:element name="MCUnscheduledForNotPaidCounter" type="xs:int"/>
          <xs:element name="MCTaskUnpaidReminderSent" type="xs:int"/>
          <xs:element name="MCCEMNoMsgOnDispatcherReschedule" type="xs:int"/>
          <xs:element name="MCIsNonABTaskScheduledNotificationSent" type="xs:int"/>
          <xs:element name="MCNewTaskAssigned" type="xs:int"/>
          <xs:element name="MCWifiCertService" type="xs:string"/>
          <xs:element name="MCShowCertificateWifiInitiate" type="xs:int"/>
          <xs:element name="MCCertificateResultWifi" type="xs:string"/>
          <xs:element name="MCCertificationActionType" type="xs:string"/>
          <xs:element name="MCCertWifiGenerateTrigger__c" type="xs:boolean"/>
          <xs:element name="MCCertificateWifiRequired" type="xs:boolean"/>
          <xs:element name="MCTAPHighFrecuency" type="xs:string"/>
          <xs:element name="MCTAPLowFrecuency" type="xs:string"/>
          <xs:element name="MCSplitterHighFrecuency" type="xs:string"/>
          <xs:element name="MCSplitterLowFrecuency" type="xs:string"/>
          <xs:element name="MCCMHighFrecuency" type="xs:string"/>
          <xs:element name="MCCMLowFrecuency" type="xs:string"/>
          <xs:element name="MCResendClosureOM" type="xs:int"/>
          <xs:element name="MCCMLowFrecuency" type="xs:string"/>
          <xs:element name="MCCMLowFrecuency" type="xs:string"/>
          <xs:element name="MCCMLowFrecuency" type="xs:string"/>
          <xs:element name="MCCMLowFrecuency" type="xs:string"/>
          <xs:element name="CodigoTecnico" type="xs:string"/>
          <xs:element name="ServiceAppointmentID" type="xs:string"/>
          <xs:element name="AppointmentStatus" type="xs:string"/>
          <xs:element name="ServiceTerritory" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Stamp">
        <xs:sequence>
          <xs:element name="CreatedBy" type="xs:string"/>
          <xs:element name="TimeCreated" type="xs:date"/>
          <xs:element name="CreatingProcess" type="xs:int"/>
          <xs:element name="ModifiedBy" type="xs:string"/>
          <xs:element name="TimeModified" type="xs:date"/>
          <xs:element name="ModifyingProcess" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCMVMaterialUsedKey">
        <xs:sequence>
          <xs:element name="attributes" type="tns:Attributes"/>
          <xs:element name="Id" type="xs:string"/>
          <xs:element name="IsDeleted" type="xs:boolean"/>
          <xs:element name="ProductConsumedNumber" type="xs:string"/>
          <xs:element name="CreatedDate" type="xs:date"/>
          <xs:element name="CreatedById" type="xs:string"/>
          <xs:element name="LastModifiedDate" type="xs:date"/>
          <xs:element name="LastModifiedById" type="xs:string"/>
          <xs:element name="SystemModstamp" type="xs:date"/>
          <xs:element name="LastViewedDate" type="xs:date"/>
          <xs:element name="LastReferencedDate" type="xs:date"/>
          <xs:element name="WorkOrderId" type="xs:string"/>
          <xs:element name="WorkOrderLineItemId" type="xs:string"/>
          <xs:element name="ProductItemId" type="xs:string"/>
          <xs:element name="PricebookEntryId" type="xs:string"/>
          <xs:element name="Product2Id" type="xs:string"/>
          <xs:element name="ProductName" type="xs:string"/>
          <xs:element name="QuantityUnitOfMeasure" type="xs:string"/>
          <xs:element name="QuantityConsumed" type="xs:double"/>
          <xs:element name="UnitPrice" type="xs:long"/>
          <xs:element name="Description" type="xs:string"/>
          <xs:element name="Discount" type="xs:string"/>
          <xs:element name="ListPrice" type="xs:double"/>
          <xs:element name="TotalPrice" type="xs:long"/>
          <xs:element name="Subtotal" type="xs:long"/>
          <xs:element name="IsConsumed" type="xs:boolean"/>
          <xs:element name="MCExternalRefID__c" type="xs:string"/>
          <xs:element name="MCAreaOperativa__c" type="xs:string"/>
          <xs:element name="MCSerialNumber__c" type="xs:string"/>
          <xs:element name="MCCountry__c" type="xs:string"/>
          <xs:element name="MCServiceResource__c" type="xs:string"/>
          <xs:element name="MCProduct2__c" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Attributes">
        <xs:sequence>
          <xs:element name="type" type="xs:string"/>
          <xs:element name="url" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomerAccount">
        <xs:sequence>
          <xs:element name="Id" type="xs:string"/>
          <xs:element name="IsDeleted" type="xs:boolean"/>
          <xs:element name="MasterRecordId" type="xs:string"/>
          <xs:element name="Name" type="xs:string"/>
          <xs:element name="LastName" type="xs:string"/>
          <xs:element name="FirstName" type="xs:string"/>
          <xs:element name="Salutation" type="xs:string"/>
          <xs:element name="MiddleName" type="xs:string"/>
          <xs:element name="Suffix" type="xs:string"/>
          <xs:element name="Type" type="xs:string"/>
          <xs:element name="RecordTypeId" type="xs:string"/>
          <xs:element name="BillingStreet" type="xs:string"/>
          <xs:element name="BillingCity" type="xs:string"/>
          <xs:element name="BillingState" type="xs:string"/>
          <xs:element name="BillingPostalCode" type="xs:string"/>
          <xs:element name="BillingCountry" type="xs:string"/>
          <xs:element name="BillingLatitude" type="xs:double"/>
          <xs:element name="BillingLongitude" type="xs:double"/>
          <xs:element name="BillingGeocodeAccuracy" type="xs:string"/>
          <xs:element name="BillingAddress" type="tns:BillingAdress"/>
          <xs:element name="ShippingStreet" type="xs:string"/>
          <xs:element name="ShippingCity" type="xs:string"/>
          <xs:element name="ShippingState" type="xs:string"/>
          <xs:element name="ShippingPostalCode" type="xs:string"/>
          <xs:element name="ShippingCountry" type="xs:string"/>
          <xs:element name="ShippingLatitude" type="xs:double"/>
          <xs:element name="ShippingLongitude" type="xs:double"/>
          <xs:element name="ShippingGeocodeAccuracy" type="xs:string"/>
          <xs:element name="ShippingAddress" type="xs:string"/>
          <xs:element name="Phone" type="xs:int"/>
          <xs:element name="Website" type="xs:string"/>
          <xs:element name="PhotoUrl" type="xs:string"/>
          <xs:element name="Industry" type="xs:string"/>
          <xs:element name="NumberOfEmployees" type="xs:int"/>
          <xs:element name="Description" type="xs:string"/>
          <xs:element name="OwnerId" type="xs:string"/>
          <xs:element name="CreatedDate" type="xs:date"/>
          <xs:element name="CreatedById" type="xs:string"/>
          <xs:element name="LastModifiedDate" type="xs:date"/>
          <xs:element name="LastModifiedById" type="xs:string"/>
          <xs:element name="SystemModstamp" type="xs:date"/>
          <xs:element name="LastActivityDate" type="xs:date"/>
          <xs:element name="LastViewedDate" type="xs:date"/>
          <xs:element name="LastReferencedDate" type="xs:date"/>
          <xs:element name="IsPartner" type="xs:boolean"/>
          <xs:element name="IsCustomerPortal" type="xs:boolean"/>
          <xs:element name="PersonContactId" type="xs:string"/>
          <xs:element name="IsPersonAccount" type="xs:boolean"/>
          <xs:element name="ChannelProgramName" type="xs:string"/>
          <xs:element name="ChannelProgramLevelName" type="xs:string"/>
          <xs:element name="PersonMailingStreet" type="xs:string"/>
          <xs:element name="PersonMailingCity" type="xs:string"/>
          <xs:element name="PersonMailingState" type="xs:string"/>
          <xs:element name="PersonMailingPostalCode" type="xs:string"/>
          <xs:element name="PersonMailingCountry" type="xs:string"/>
          <xs:element name="PersonMailingLatitude" type="xs:double"/>
          <xs:element name="PersonMailingLongitude" type="xs:double"/>
          <xs:element name="PersonMailingGeocodeAccuracy" type="xs:string"/>
          <xs:element name="PersonMailingAddress" type="xs:string"/>
          <xs:element name="PersonMobilePhone" type="xs:int"/>
          <xs:element name="PersonEmail" type="xs:string"/>
          <xs:element name="PersonTitle" type="xs:string"/>
          <xs:element name="PersonDepartment" type="xs:string"/>
          <xs:element name="PersonLastCURequestDate" type="xs:date"/>
          <xs:element name="PersonLastCUUpdateDate" type="xs:date"/>
          <xs:element name="PersonEmailBouncedReason" type="xs:string"/>
          <xs:element name="PersonEmailBouncedDate" type="xs:date"/>
          <xs:element name="Jigsaw" type="xs:string"/>
          <xs:element name="JigsawCompanyId" type="xs:string"/>
          <xs:element name="AccountSource" type="xs:string"/>
          <xs:element name="SicDesc" type="xs:string"/>
          <xs:element name="OperatingHoursId" type="xs:string"/>
          <xs:element name="MCAreaOperativa__c" type="xs:string"/>
          <xs:element name="MCClientCode__c" type="xs:string"/>
          <xs:element name="MCCustomerClassType__c" type="xs:string"/>
          <xs:element name="MCCustomerType__c" type="xs:string"/>
          <xs:element name="MCExternalRefID__c" type="xs:string"/>
          <xs:element name="MCOtherPhones__c" type="xs:int"/>
          <xs:element name="MCPersonIdentNo__c" type="xs:string"/>
          <xs:element name="MCBusinessIdentNo__c" type="xs:string"/>
          <xs:element name="MCEmail__c" type="xs:string"/>
          <xs:element name="MCCountry__c" type="xs:string"/>
          <xs:element name="MCContactType__pc" type="xs:string"/>
          <xs:element name="MCLocation__pc" type="xs:string"/>
          <xs:element name="MCUser__pc" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="BillingAdress">
        <xs:sequence>
          <xs:element name="city" type="xs:string"/>
          <xs:element name="country" type="xs:string"/>
          <xs:element name="geocodeAccuracy" type="xs:string"/>
          <xs:element name="latitude" type="xs:double"/>
          <xs:element name="longitude" type="xs:double"/>
          <xs:element name="postalCode" type="xs:int"/>
          <xs:element name="state" type="xs:string"/>
          <xs:element name="street" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCMVEquipmentUsedKey">
        <xs:sequence>
          <xs:element name="Key" type="xs:int"/>
          <xs:element name="DisplayString" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCMVEquipmentCollectedKey">
        <xs:sequence>
          <xs:element name="Key" type="xs:int"/>
          <xs:element name="DisplayString" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCLaboresUsedKey">
        <xs:sequence>
          <xs:element name="Key" type="xs:int"/>
          <xs:element name="DisplayString" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCServices">
        <xs:sequence>
          <xs:element maxOccurs="unbounded" name="MCService" type="tns:MCService"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="MCService">
        <xs:sequence>
          <xs:element name="Key" type="xs:string"/>
          <xs:element name="DisplayString" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RequiredSkills1">
        <xs:sequence>
          <xs:element name="TaskRequiredSkill1" type="tns:TaskRequiredSkill1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TaskRequiredSkill1">
        <xs:sequence>
          <xs:element name="Key" type="xs:string"/>
          <xs:element name="SkillLevel" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Assignment">
        <xs:sequence>
          <xs:element name="Key" type="xs:int"/>
          <xs:element name="Revision" type="xs:int"/>
          <xs:element name="Stamp" type="tns:Stamp"/>
          <xs:element name="Task" type="tns:TaskAssigment"/>
          <xs:element name="Start" type="xs:date"/>
          <xs:element name="Finish" type="xs:date"/>
          <xs:element name="Engineers" type="tns:Engineer"/>
          <xs:element name="Comment" type="xs:string"/>
          <xs:element name="Location" type="xs:string"/>
          <xs:element name="BinaryData" type="xs:string"/>
          <xs:element name="Latitude" type="xs:int"/>
          <xs:element name="Longitude" type="xs:int"/>
          <xs:element name="GISDataSource" type="xs:int"/>
          <xs:element name="AssignedEngineers" type="xs:string"/>
          <xs:element name="LogicPolicy" type="xs:string"/>
          <xs:element name="IsCrewAssignment" type="xs:int"/>
          <xs:element name="CountryID" type="xs:string"/>
          <xs:element name="Street" type="xs:string"/>
          <xs:element name="City" type="xs:string"/>
          <xs:element name="State" type="xs:string"/>
          <xs:element name="Postcode" type="xs:string"/>
          <xs:element name="NonAvailabilityType" type="xs:string"/>
          <xs:element name="IgnoreInRoster" type="xs:int"/>
          <xs:element name="ContractorIndex" type="xs:int"/>
          <xs:element name="StateSubdivision" type="xs:string"/>
          <xs:element name="CitySubdivision" type="xs:string"/>
          <xs:element name="ID" type="xs:int"/>
          <xs:element name="WorkAgreementID" type="xs:string"/>
          <xs:element name="ExternalRefID" type="xs:string"/>
          <xs:element name="IsBreakIncluded" type="xs:int"/>
          <xs:element name="IncludedBreakDuration" type="xs:int"/>
          <xs:element name="AbsenceRequest" type="xs:string"/>
          <xs:element name="ExternalComment" type="xs:string"/>
          <xs:element name="AssignmentSource" type="xs:int"/>
          <xs:element name="PredictiveArea" type="xs:int"/>
          <xs:element name="ArchiveStatus" type="xs:string"/>
          <xs:element name="StartTimeGMT" type="xs:date"/>
          <xs:element name="FinishTimeGMT" type="xs:date"/>
          <xs:element name="MobileKey" type="xs:string"/>
          <xs:element name="Unit" type="xs:string"/>
          <xs:element name="TaskModifiedTime" type="xs:date"/>
          <xs:element name="TaskCallID" type="xs:string"/>
          <xs:element name="TaskNumber" type="xs:int"/>
          <xs:element name="TaskTypeCategory" type="xs:string"/>
          <xs:element name="CurrentTaskAppointmentTime" type="xs:string"/>
          <xs:element name="NewAssignmentStartDate" type="xs:date"/>
          <xs:element name="CurrentAssignmentStartDate" type="xs:date"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TaskAssigment">
        <xs:sequence>
          <xs:element name="CallID" type="xs:string"/>
          <xs:element name="Number" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Engineer">
        <xs:sequence>
          <xs:element name="ID" type="xs:int"/>
          <xs:element name="District" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="GtTaskRequest">
    <wsdl:part element="tns:GtTaskRequest" name="GtTaskRequest"/>
  </wsdl:message>
  <wsdl:message name="GtTaskResponse">
    <wsdl:part element="tns:GtTaskResponse" name="GtTaskResponse"/>
  </wsdl:message>
  <wsdl:portType name="getTaskPort">
    <wsdl:operation name="GtTask">
      <wsdl:input message="tns:GtTaskRequest" name="GtTaskRequest"/>
      <wsdl:output message="tns:GtTaskResponse" name="GtTaskResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="getTaskPortSoap11" type="tns:getTaskPort">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GtTask">
      <soap:operation soapAction=""/>
      <wsdl:input name="GtTaskRequest">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="GtTaskResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="getTaskPortService">
    <wsdl:port binding="tns:getTaskPortSoap11" name="getTaskPortSoap11">
      <soap:address location="http://fs-ar2-test.fsmillicom.com:8080/wrapper/soapWS/getTask"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>