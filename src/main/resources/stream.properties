###############################################################################
# RABBITMQ
###############################################################################
spring.rabbitmq.host=127.0.0.1
spring.rabbitmq.port=5672
spring.rabbitmq.password=guest
spring.rabbitmq.username=guest
spring.rabbitmq.listener.simple.auto-startup=true
spring.rabbitmq.listener.simple.retry.enabled=true
spring.rabbitmq.listener.simple.retry.initial-interval=1000
spring.rabbitmq.listener.simple.retry.max-attempts=3
spring.rabbitmq.listener.simple.retry.multiplier=2
spring.rabbitmq.listener.simple.retry.max-interval=100000

spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true

spring.cloud.stream.bindings.output.content-type=application/json

#spring.cloud.stream.function.definition=

#####
#### Default configuration
#####
spring.cloud.stream.rabbit.default.consumer.queueBindingArguments.x-match=all

spring.cloud.stream.rabbit.default.consumer.consumerTagPrefix=${spring.application.name}
spring.cloud.stream.rabbit.default.consumer.autoBindDlq=true
spring.cloud.stream.rabbit.default.consumer.republishToDlq=true

spring.cloud.stream.rabbit.default.exchangeType=headers
spring.cloud.stream.rabbit.bindings.updateAcquisitionProspectPaidProducer-out-0.producer.exchangeType=headers
