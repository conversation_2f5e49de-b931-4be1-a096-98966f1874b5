-- liquibase formatted sql
-- changeset onaimi:1.2.0-002-remove_payment

DROP TABLE payment;

-- rollback CREATE TABLE payment
-- rollback (
-- rollback     prospect_reference                VARCHAR(25) PRIMARY KEY,
-- rollback     order_id                          VARCHAR(8)                            NOT NULL,
-- rollback     contact_channel_type              VARCHAR(8)                            NOT NULL,
-- rollback     contact_value                     VARCHAR(255)                          NOT NULL,
-- rollback     provider_reference                VARCHAR(100),
-- rollback     provider_payment_method_reference VARCHAR(100),
-- rollback     usecase                           VARCHAR(50)                           NOT NULL,
-- rollback     status                            VARCHAR(25)                           NOT NULL,
-- rollback     generated_link                    VARCHAR(255),
-- rollback     error_code                        VA<PERSON>HAR(50),
-- rollback     error_message                     VARCHAR(255),
-- rollback     amount                            BIGINT                                NOT NULL,
-- rollback     currency                          VARCHAR(5)                            NOT NULL,
-- rollback     created_at                        TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL,
-- rollback     updated_at                        TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP ()
-- rollback );
