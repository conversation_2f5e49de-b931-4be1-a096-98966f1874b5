-- liquibase formatted sql
-- changeset onaimi:1.1.0-001-create_payment

CREATE TABLE payment
(
    prospect_reference                VARCHAR(25) PRIMARY KEY,
    order_id                          VARCHAR(8)                            NOT NULL,
    contact_channel_type              VARCHAR(8)                            NOT NULL,
    contact_value                     VARCHAR(255)                          NOT NULL,
    provider_reference                VARCHAR(100),
    provider_payment_method_reference VARCHAR(100),
    status                            VARCHAR(25)                           NOT NULL,
    generated_link                    VARCHAR(255),
    amount                            BIGINT                                NOT NULL,
    currency                          VARCHAR(5)                            NOT NULL,
    created_at                        TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    updated_at                        TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP ()
);

-- rollback DROP TABLE payment;