-- liquibase formatted sql
-- changeset mprunier:1.1.0-001-create_contact_identity_document

CREATE TABLE contact_identity_document
(
    reference           VARCHAR(8) PRIMARY KEY,
    document_identifier VARCHAR(255)                          NOT NULL,
    document_type       VARCHAR(255)                          NOT NULL,
    nationality         VARCHAR(255)                          NOT NULL,
    created_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    updated_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP()
);

-- rollback DROP TABLE contact_identity_document;
