-- liquibase formatted sql
-- changeset mprunier:1.2.0-003-create_offer_sim_delivery_type

CREATE TABLE offer_sim_delivery_type
(
    offer_id   BIGINT PRIMARY KEY,
    type       VARCHAR(255)                          NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP()
);

-- rollback DROP TABLE offer_sim_delivery_type;
