-- liquibase formatted sql
-- changeset onaimi:1.1.0-002-create_payment_history

CREATE TABLE payment_history
(
    id                                BIGINT AUTO_INCREMENT PRIMARY KEY,
    prospect_reference                VARCHAR(25)                           NOT NULL,
    provider_reference                VARCHAR(100),
    provider_payment_method_reference VARCHAR(100),
    order_id                          VARCHAR(8)                            NOT NULL,
    status                            VARCHAR(25)                           NOT NULL,
    generated_link                    VARCHAR(255),
    created_at                        TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL
);

ALTER TABLE payment_history
    ADD CONSTRAINT fk__payment_history__payment__prospect_reference FOREIGN KEY (prospect_reference) REFERENCES payment (prospect_reference);

-- rollback DROP TABLE payment_history;