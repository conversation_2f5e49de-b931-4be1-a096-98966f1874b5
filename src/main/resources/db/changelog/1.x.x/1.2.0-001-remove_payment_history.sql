-- liquibase formatted sql
-- changeset onaimi:1.2.0-001-remove_payment_history

DROP TABLE payment_history;

-- rollback CREATE TABLE payment_history
-- rollback (
-- rollback     id                                BIGINT AUTO_INCREMENT PRIMARY KEY,
-- rollback     prospect_reference                VARCHAR(25)                           NOT NULL,
-- rollback     provider_reference                VARCHAR(100),
-- rollback     provider_payment_method_reference VARCHAR(100),
-- rollback     usecase                           VARCHAR(50)                           NOT NULL,
-- rollback     contact_channel_type              VARCHAR(8),
-- rollback     contact_value                     VARCHAR(255),
-- rollback     error_code                        VARCHAR(50),
-- rollback     error_message                     VARCHAR(255),
-- rollback     order_id                          VARCHAR(8)                            NOT NULL,
-- rollback     status                            VARCHAR(25)                           NOT NULL,
-- rollback     generated_link                    VARCHAR(255),
-- rollback     created_at                        TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL
-- rollback );
-- rollback ALTER TABLE payment_history
-- rollback     ADD CONSTRAINT fk__payment_history__payment__prospect_reference FOREIGN KEY (prospect_reference) REFERENCES payment (prospect_reference);
