-- liquibase formatted sql
-- changeset mprunier:1.0.0-001-create_contact_address

CREATE TABLE contact_address
(
    reference  VARCHAR(8)                            NOT NULL,
    type       VARCHAR(255)                          NOT NULL,
    address_id BIGINT                                NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP(),
    PRIMARY KEY (reference, type)
);

-- rollback DROP TABLE contact_address;
