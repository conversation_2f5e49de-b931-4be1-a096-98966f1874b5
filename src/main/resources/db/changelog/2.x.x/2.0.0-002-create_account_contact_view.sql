-- liquibase formatted sql
-- changeset jescalante:2.0.0-002-create_account_contact_view context:production
-- preConditions onFail: MARK_RAN
-- preCondition tableExists tableName=account_contact schemaName=accounts

CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW `sales`.`account_contact_view` AS
select
	c.uuid as id,
	ac.account_id as account_id,
	c.first_name as contact_first_name,
	c.last_name as contact_last_name,
	phone.phone_number as contact_mobile_phone_number,
	email.email as contact_main_email,
	doc.expiration_date as identity_document_expiration_date,
	doc.identifier as identity_document_identifier,
	doc.type as identity_document_type
from 
accounts.account_contact ac 
left join accounts.ref_contact_type rct on ac.type_id = rct.id
left join contacts.contact c on ac.uuid = c.uuid
left join contacts.phone_number as phone on ac.uuid = phone.contact_uuid and phone.type = '<PERSON><PERSON><PERSON><PERSON>'
left join contacts.email as email on ac.uuid = email.contact_uuid and email.type = 'MAIN'
left join contacts.identity_document doc on ac.uuid = doc.contact_uuid
where rct.name = 'OWNER';

-- rollback DROP VIEW sales.account_contact_view;
