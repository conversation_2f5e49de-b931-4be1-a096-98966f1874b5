<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://crmsaleforce.resourcemanager.millicom.com/timeintervalsoap" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:sch="http://crmsaleforce.resourcemanager.millicom.com/timeintervalsoap" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://crmsaleforce.resourcemanager.millicom.com/timeintervalsoap">
  <wsdl:types>
    <xs:schema elementFormDefault="unqualified" targetNamespace="http://crmsaleforce.resourcemanager.millicom.com/timeintervalsoap" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="TimeIntervalRequest">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="ExtendedGetAppointment" type="tns:ExtendedGetAppointment"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:complexType name="ExtendedGetAppointment">
        <xs:sequence>
          <xs:element name="Nodo" type="xs:string"/>
          <xs:element name="Task" type="tns:Task"/>
          <xs:element name="Profile" type="xs:string"/>
          <xs:element name="Period" type="tns:Period"/>
          <xs:element name="ExcludeCurrentAppointment" type="xs:boolean"/>
          <xs:element name="MessageError" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Task">
        <xs:sequence>
          <xs:element name="CallID" type="xs:string"/>
          <xs:element name="Number" type="xs:int"/>
          <xs:element name="OpenDate" type="xs:date"/>
          <xs:element name="EarlyStart" type="xs:date"/>
          <xs:element name="DueDate" type="xs:date"/>
          <xs:element name="Area" type="xs:string"/>
          <xs:element name="Region" type="tns:Region"/>
          <xs:element name="District" type="xs:string"/>
          <xs:element name="Priority" type="xs:int"/>
          <xs:element name="TaskTypeCategory" type="xs:string"/>
          <xs:element name="TaskType" type="xs:string"/>
          <xs:element name="Duration" type="xs:int"/>
          <xs:element name="NumberOfRequiredEngineers" type="xs:int"/>
          <xs:element name="MCBillingAccountInfo" type="xs:string"/>
          <xs:element name="City" type="xs:string"/>
          <xs:element name="CountryID" type="xs:string"/>
          <xs:element name="Latitude" type="xs:string"/>
          <xs:element name="Longitude" type="xs:string"/>
          <xs:element name="LateStart" type="xs:date"/>
          <xs:element name="Street" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Period">
        <xs:sequence>
          <xs:element name="Start" type="xs:date"/>
          <xs:element name="Finish" type="xs:date"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Region">
        <xs:sequence>
          <xs:element name="Name" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="TimeIntervalResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element maxOccurs="unbounded" name="slot" type="tns:slots"/>
            <xs:element name="isError" type="xs:string"/>
            <xs:element name="errorMessage" type="xs:string"/>
            <xs:element name="count" type="xs:int"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:complexType name="slots">
        <xs:sequence>
          <xs:element name="start" type="xs:string"/>
          <xs:element name="grade" type="xs:double"/>
          <xs:element name="finish" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="TimeIntervalRequest">
    <wsdl:part element="tns:TimeIntervalRequest" name="TimeIntervalRequest"/>
  </wsdl:message>
  <wsdl:message name="TimeIntervalResponse">
    <wsdl:part element="tns:TimeIntervalResponse" name="TimeIntervalResponse"/>
  </wsdl:message>
  <wsdl:portType name="timeIntervalPort">
    <wsdl:operation name="TimeInterval">
      <wsdl:input message="tns:TimeIntervalRequest" name="TimeIntervalRequest"/>
      <wsdl:output message="tns:TimeIntervalResponse" name="TimeIntervalResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="timeIntervalPortSoap11" type="tns:timeIntervalPort">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="TimeInterval">
      <soap:operation soapAction=""/>
      <wsdl:input name="TimeIntervalRequest">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="TimeIntervalResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="timeIntervalPortService">
    <wsdl:port binding="tns:timeIntervalPortSoap11" name="timeIntervalPortSoap11">
      <soap:address location="http://fs-ar2-test.fsmillicom.com:8080/wrapper/soapWS/timeInterval"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>