package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.SearchAcquisitionProspectsOffersRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellOfferSearchRequest;
import com.tigo.galaxion.sales.facade.domain.request.GetOfferForAcquisitionProspectRequest;
import com.tigo.galaxion.sales.facade.domain.request.GetOfferForCrossSellRequest;
import com.tigo.galaxion.sales.facade.domain.request.GetTariffPlansForAnOfferRequest;
import com.tigo.galaxion.sales.facade.domain.response.OffersAndConditionalDiscountsResponse;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class CatalogServiceIntegrationTest {

    @Mock
    private AcquisitionProspectClient acquisitionProspectClient;

    @Mock
    private CrossSellClient crossSellClient;

    @InjectMocks
    private CatalogService catalogService;

    @Nested
    class GetOffersForAcquisitionTest {

        @Test
        void offerExist_callAcquisitionProspectAndReturnProspectOffersResponse() {
            // GIVEN
            var prospectRef = "RO6EW4O9";
            var offerSearchRequestArgumentCaptor = ArgumentCaptor.forClass(SearchAcquisitionProspectsOffersRequest.class);
            doReturn(OffersAndConditionalDiscountsResponse.builder().build()).when(acquisitionProspectClient).getOffers(anyString(), any());
            var request = GetOfferForAcquisitionProspectRequest.builder()
                                                               .serviceGroup("DUALPLAY")
                                                               .parentOfferId(1L)
                                                               .build();

            // WHEN
            catalogService.getOffersForAcquisition(prospectRef, request);

            // THEN
            verify(acquisitionProspectClient).getOffers(eq(prospectRef), offerSearchRequestArgumentCaptor.capture());
            var offerSearchRequest = offerSearchRequestArgumentCaptor.getValue();
            assertEquals(request.getServiceGroup(), offerSearchRequest.getServiceGroup());
            assertEquals(request.getParentOfferId(), offerSearchRequest.getParentOfferId());
        }
    }

    @Nested
    class GetOffersForCrossSellTest {

        @Test
        void offerExist_callAcquisitionProspectAndReturnProspectOffersResponse() {
            // GIVEN
            var prospectRef = "RO6EW4O9";
            var offerSearchRequestArgumentCaptor = ArgumentCaptor.forClass(CrossSellOfferSearchRequest.class);
            doReturn(OffersAndConditionalDiscountsResponse.builder().build()).when(crossSellClient).getOffers(anyString(), any());
            var request = GetOfferForCrossSellRequest.builder()
                                                     .serviceGroup("DUALPLAY")
                                                     .parentOfferCode("CODE")
                                                     .build();

            // WHEN
            catalogService.getOffersForCrossSell(prospectRef, request);

            // THEN
            verify(crossSellClient).getOffers(eq(prospectRef), offerSearchRequestArgumentCaptor.capture());
            var offerSearchRequest = offerSearchRequestArgumentCaptor.getValue();
            assertEquals(request.getServiceGroup(), offerSearchRequest.getServiceGroup());
            assertEquals(request.getParentOfferCode(), offerSearchRequest.getParentOfferCode());
        }
    }

    @Nested
    class GetTariffPlansForAnOfferTest {

        @Test
        void offerExist_callAcquisitionProspectAndReturnProspectOffersResponse() {
            // GIVEN
            var prospectRef = "RO6EW4O9";
            var offerCode = "HFC_100_1_4";
            var request = GetTariffPlansForAnOfferRequest.builder()
                                                         .portIn(true)
                                                         .portInType("NO_PORT_IN")
                                                         .build();

            // WHEN
            catalogService.getTariffsPlansForAnOffer(prospectRef, offerCode, request);

            // THEN
            verify(acquisitionProspectClient).getTariffsPlansForAnOffer(eq(prospectRef), eq(offerCode), any());
        }
    }

    @Nested
    class GetAddOnsForAnOfferTest {

        @Test
        void offerExist_callAcquisitionProspectAndReturnProspectOffersResponse() {
            // GIVEN
            var prospectRef = "RO6EW4O9";
            var offerId = 1L;

            // WHEN
            catalogService.getAddOnsForAnOffer(prospectRef, offerId);

            // THEN
            verify(acquisitionProspectClient).getAddOnsForAnOffer(eq(prospectRef), eq(offerId));
        }
    }

    @Nested
    class GetEquipmentsForAnOfferTest {

        @Test
        void offerExist_callAcquisitionProspectAndReturnProspectOffersResponse() {
            // GIVEN
            var prospectRef = "RO6EW4O9";
            var offerId = 1L;

            // WHEN
            catalogService.getEquipmentsForAnOffer(prospectRef, offerId);

            // THEN
            verify(acquisitionProspectClient).getEquipmentForAnOffer(eq(prospectRef), eq(offerId));
        }
    }
}
