package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.account.AccountClient;
import com.tigo.galaxion.sales.facade.connector.account.domain.response.ContactV3Response;
import com.tigo.galaxion.sales.facade.connector.contact.ContactClient;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactIdentityDocumentV2Response;
import com.tigo.galaxion.sales.facade.connector.credit.score.CreditScoreClient;
import com.tigo.galaxion.sales.facade.connector.credit.score.domain.request.UpdateCreditScoreRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.response.TigoCreditScoreResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.OfferTypeEnum;
import com.tigo.galaxion.sales.facade.domain.request.CreateCrossSellRequest;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CreateCrossSellServiceTest {

    @Mock
    CrossSellClient crossSellClient;

    @Mock
    AccountClient accountClient;

    @Mock
    ContactClient contactClient;

    @Mock
    CreditScoreClient creditScoreClient;

    @Mock
    CreditScoreService creditScoreService;

    @Mock
    EligibilityService eligibilityService;

    @InjectMocks
    CreateCrossSellService createCrossSellService;

    @Nested
    class createCrossSell_withValidData_crossSellCreated {

        @Test
        void validRequest_getServiceGroupsEligibility() {
            // GIVEN
            String accountId = "1";
            CreateCrossSellRequest createCrossSellRequest = CreateCrossSellRequest
                    .builder()
                    .offerType(OfferTypeEnum.POSTPAY)
                    .build();

            var ownerContactUuid = "uuid";
            var crossSellReference = "ref";
            var type = "NIT";
            var identifier = "12345";
            var nationality = "CO";
            var newCreditScore = "1";

            var contactV3Response = ContactV3Response
                    .builder()
                    .uuid(ownerContactUuid)
                    .build();
            when(accountClient.searchAccountContact(any())).thenReturn(List.of(contactV3Response));

            var contactIdentityDocumentV2Response = ContactIdentityDocumentV2Response
                    .builder()
                    .type(type)
                    .identifier(identifier)
                    .mainIdentityDocument(true)
                    .nationality(nationality)
                    .build();
            when(contactClient.getIdentityDocuments(ownerContactUuid)).thenReturn(List.of(contactIdentityDocumentV2Response));

            var tigoCreditScoreResponse = TigoCreditScoreResponse.builder().score(newCreditScore).build();
            when(creditScoreService.getTigoCreditScore(any())).thenReturn(tigoCreditScoreResponse);

            when(crossSellClient.createCrossSell(any())).thenReturn(crossSellReference);
            doNothing().when(eligibilityService).checkCrossSellEligibility(any());

            // WHEN
            var response = createCrossSellService.createCrossSell(accountId, createCrossSellRequest);

            // THEN
            assertEquals(crossSellReference, response);

            var updateCreditScoreRequestArgumentCaptor = ArgumentCaptor.forClass(UpdateCreditScoreRequest.class);
            verify(creditScoreClient).updateCreditScore(updateCreditScoreRequestArgumentCaptor.capture());
            verify(eligibilityService).checkCrossSellEligibility(accountId);
            assertEquals(newCreditScore, updateCreditScoreRequestArgumentCaptor.getValue().getCreditScore());
            assertEquals(ownerContactUuid, updateCreditScoreRequestArgumentCaptor.getValue().getContactUuid());
        }
    }
}
