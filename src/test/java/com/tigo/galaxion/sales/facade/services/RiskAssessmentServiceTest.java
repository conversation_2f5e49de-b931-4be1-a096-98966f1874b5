package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.enumeration.IdentificationTypeEnum;
import com.tigo.galaxion.sales.facade.connector.riskassessment.RiskAssessmentClient;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.enumeration.TransactionTypeEnum;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request.Customer;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request.Offer;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request.PropertyObject;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.request.RiskAssessmentRequestBody;
import com.tigo.galaxion.sales.facade.connector.riskassessment.domain.response.RiskAssessmentResponseBody;

@ExtendWith(MockitoExtension.class)
public class RiskAssessmentServiceTest {

    @Mock
    RiskAssessmentClient riskAssessmentClient;

    @InjectMocks
    RiskAssessmentService riskAssessmentService;

    @Nested
    class RiskAssessmentTest {
        @Test
        void RiskAssessmentValidTest() {

            // GIVEN
            var request = RiskAssessmentRequestBody
                    .builder()
                    .traceID("1234-56789-10")
                    .transactionType(TransactionTypeEnum.ACTIVATION)
                    .customer(Customer
                            .builder()
                            .id("1")
                            .typeIdentification(IdentificationTypeEnum.CC)
                            .stratum(3)
                            .build())
                    .offers(new Offer[] { Offer
                            .builder()
                            .id("1")
                            .name("HBO")
                            .value(11)
                            .build() })
                    .properties(new PropertyObject[] { PropertyObject
                            .builder()
                            .key("some")
                            .value("value")
                            .build() })
                    .build();

            // WHEN
            var raResponse = RiskAssessmentResponseBody
                    .builder()
                    .code("100")
                    .message("Approved")
                    .availableCredit(1000)
                    .build();
            when(riskAssessmentClient.riskValidation(any())).thenReturn(raResponse);

            // THEN

            var response = riskAssessmentService.riskValidation(request);

            assertNotNull(response);
        }
    }
}
