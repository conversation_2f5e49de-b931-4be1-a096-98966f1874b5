package com.tigo.galaxion.sales.facade.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogEntity;
import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogId;
import com.tigo.galaxion.sales.facade.model.repository.ProspectusLogRepository;

import com.tigo.galaxion.sales.facade.domain.request.ProspectusLogRequest;
import com.tigo.galaxion.sales.facade.TigoSalesFacadeApplicationTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
 
@SpringBootTest(classes = TigoSalesFacadeApplicationTest.class)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "wfe.signal.SP_DEVICE_REGISTRATION_BB_SYMPHONICA=someSignalName",
    "symphonica.statuses=CANCELLED,REJECTED,PARTIAL,FAILED"
})
public class ProspectusLogServiceControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProspectusLogRepository prospectLogRepository;

    private ProspectusLogRequest request;
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {

        objectMapper = new ObjectMapper();

        request = ProspectusLogRequest.builder()
                .reference("5047EY04")
                .customer_id_type("CC")
                .customer_id_number("9874563210")
                .user_id("mamayer")
                .user_name("María Alejandra Meyer")
                .ip_equipment("***********")
                .sales_channel("TELESALES")
                .authentication_type("NA")
                .transaction("Nueva_Suscripcion")
                .build();
    }

    @Test
    public void testCreateProspectusLog() throws Exception {

        ProspectusLogId prospectusLogId = ProspectusLogId.builder()
                .reference("5047EY04")
                .customer_id_type("CC")
                .customer_id_number("9874563210")
                .build();

        ProspectusLogEntity prospectusLogEntity = ProspectusLogEntity.builder()
                .prospectusLogId(prospectusLogId)
                .user_id("mamayer")
                .user_name("María Alejandra Meyer")
                .ip_equipment("***********")
                .sales_channel("TELESALES")
                .authentication_type("NA")
                .transaction("Nueva_Suscripcion")
                .build(); 

        when(prospectLogRepository.save(any(ProspectusLogEntity.class))).thenReturn(prospectusLogEntity);

        mockMvc.perform(post("/api/v1/prospects")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated());

        verify(prospectLogRepository, times(1)).save(any(ProspectusLogEntity.class));
    }
}
