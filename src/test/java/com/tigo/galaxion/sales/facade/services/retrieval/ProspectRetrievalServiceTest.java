package com.tigo.galaxion.sales.facade.services.retrieval;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.problem.AcquisitionProspectProblem;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.OfferTypeEnum;
import com.tigo.galaxion.sales.facade.domain.response.TigoProspectResponse;
import com.tigo.galaxion.sales.facade.model.entity.ProspectEntity;
import com.tigo.galaxion.sales.facade.model.repository.ProspectRepository;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProspectRetrievalServiceTest {

    @Mock
    private AcquisitionProspectClient acquisitionProspectClient;
    @Mock
    private ProspectRepository prospectRepository;

    @InjectMocks
    private ProspectRetrievalService prospectRetrievalService;

    @Nested
    class GetProspectResponseTest {

        @Test
        void getProspect_prospectWithContractSignatureOption_returnProspect() {
            //GIVEN
            var contractSignatureOption = "SCRIVE";
            var prospectResponse = buildAcquisitionProspectResponse();
            when(acquisitionProspectClient.getAcquisitionProspect(prospectResponse.getReference())).thenReturn(prospectResponse);
            when(prospectRepository.findByReference(prospectResponse.getReference())).thenReturn(
                    Optional.of(ProspectEntity.builder().contractSignatureOption(contractSignatureOption).build())
            );

            //WHEN
            var tigoProspectResponse = prospectRetrievalService.getProspect(prospectResponse.getReference());

            //THEN
            assertEquals(contractSignatureOption, tigoProspectResponse.getContractSignatureOption());
            assertAcquisitionProspect(prospectResponse, tigoProspectResponse);
        }

        @Test
        void getProspect_prospectWithoutContractSignatureOption_returnProspect() {
            //GIVEN
            var prospectResponse = buildAcquisitionProspectResponse();
            when(acquisitionProspectClient.getAcquisitionProspect(prospectResponse.getReference())).thenReturn(prospectResponse);
            when(prospectRepository.findByReference(prospectResponse.getReference())).thenReturn(Optional.empty());

            //WHEN
            var tigoProspectResponse = prospectRetrievalService.getProspect(prospectResponse.getReference());

            //THEN
            assertNull(tigoProspectResponse.getContractSignatureOption());
            assertAcquisitionProspect(prospectResponse, tigoProspectResponse);
        }


        @Test
        void getProspect_prospectNotFound_throwException() {
            //GIVEN
            var prospectRef = "ref";
            when(acquisitionProspectClient.getAcquisitionProspect(prospectRef)).thenThrow(AcquisitionProspectProblem.class);

            //WHEN
            //THEN
            assertThrows(AcquisitionProspectProblem.class, () -> prospectRetrievalService.getProspect(prospectRef));
        }

        private void assertAcquisitionProspect(AcquisitionProspectResponse prospectResponse, TigoProspectResponse tigoProspectResponse) {
            assertEquals(prospectResponse.getBrand(), tigoProspectResponse.getBrand());
            assertEquals(prospectResponse.getCartUuid(), tigoProspectResponse.getCartUuid());
            assertEquals(prospectResponse.getChannelCode(), tigoProspectResponse.getChannelCode());
            assertEquals(prospectResponse.getChannelGroup(), tigoProspectResponse.getChannelGroup());
            assertEquals(prospectResponse.getOfferType(), tigoProspectResponse.getOfferType());
            assertEquals(prospectResponse.getCustomerType(), tigoProspectResponse.getCustomerType());
            assertEquals(prospectResponse.getReference(), tigoProspectResponse.getReference());
        }

        private AcquisitionProspectResponse buildAcquisitionProspectResponse() {
            return AcquisitionProspectResponse.builder()
                    .brand("EPIC")
                    .cartUuid("1012ZX48")
                    .channelGroup("TELESALES")
                    .channelCode("BASIC_TLS")
                    .offerType(OfferTypeEnum.POSTPAY)
                    .customerType("RESIDENTIAL")
                    .reference("V8WMXQV0")
                    .build();
        }

    }

}
