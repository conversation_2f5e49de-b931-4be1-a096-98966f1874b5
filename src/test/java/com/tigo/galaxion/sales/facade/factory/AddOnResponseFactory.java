package com.tigo.galaxion.sales.facade.factory;

import java.util.List;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AddOnResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.RecurringAmountByOccurrenceResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.UsageResponse;

public class AddOnResponseFactory {

	public static AddOnResponse getAddOnResponse(Long longs, String strings){
		String[] itemGroup = new String[]{"ADDONS_TV", "ADDONS_EQUIPMENT"};
		return AddOnResponse.builder()
            .id(longs)
            .description(strings)
            .displayOrder(longs)
			.usages(List.of(
				UsageResponse.builder()
					.catalogCode(strings)
					.description(strings)
					.type(strings)
					.displayOrder(longs)
					.build()
			))
            .catalogCode(strings)
			.amountVatIncluded(AmountResponseFactory.getAmountResponse(longs))
			.amountVatExcluded(AmountResponseFactory.getAmountResponse(longs))
            .max(longs)
            .itemGroup(itemGroup[longs.intValue()-1])
			.recurringAmountsByOccurrence(List.of(
				RecurringAmountByOccurrenceResponse.builder()
					.amountVatIncluded(longs)
					.amountVatExcluded(longs)
					.occurrence(longs.intValue())
					.build()
			))
            .build();
	}
}
