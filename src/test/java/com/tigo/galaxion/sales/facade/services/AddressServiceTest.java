package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.address.AddressClient;
import com.tigo.galaxion.sales.facade.connector.address.domain.request.AddressRequest;
import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import com.tigo.galaxion.sales.facade.connector.georeference.GeoreferenceClient;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.request.GeoreferenceRequest;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.request.GeoreferenceRequestBody;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.response.GeoreferenceResponse;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.response.GeoreferenceResponseBody;
import com.tigo.galaxion.sales.facade.connector.georeference.domain.response.GisCommonInfoDir;
import com.tigo.galaxion.sales.facade.domain.request.AddressNormalizeRequest;
import com.tigo.galaxion.sales.facade.domain.request.AddressNormalizeRequestBody;
import com.tigo.galaxion.sales.facade.domain.response.AddressMSResponse;
import com.tigo.galaxion.sales.facade.mapper.dto.AddressNormalizeMapper;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AddressServiceTest {

    @Mock
    GeoreferenceClient georeferenceClient;

    @Mock
    AddressClient addressClient;

    @Mock
    AddressNormalizeMapper addressNormalizeMapper;

    @InjectMocks
    AddressService addressService;

    @BeforeEach
        void setUp() {
                addressService = new AddressService(georeferenceClient, addressClient);
        }

    @Nested
    class NormalizeAddressTest {
        @Test
        void validNormalizeAddress() {
            // GIVEN
            var addressNormalizeRequest = AddressNormalizeRequest
                    .builder()
                    .countryCode("1")
                    .departmentCode("2")
                    .municipalityCode("3")
                    .naturalAddress("157 San Sebastian")
                    .build();

            var addressNormalizeRequestBody = AddressNormalizeRequestBody
                    .builder()
                    .addressNormalizeRequest(addressNormalizeRequest)
                    .build();

            // var georeferenceRequest =
            // AddressNormalizeMapper.buildAddressRequest(addressNormalizeRequest);
            var georeferenceRequest = GeoreferenceRequest
                    .builder()
                    .countryCode("1")
                    .departmentCode("2")
                    .municipalityCode("3")
                    .naturalAddress("157 San Benito")
                    .build();
            var georeferenceRequestBody = GeoreferenceRequestBody.builder()
                    .georreferenceRequest(georeferenceRequest)
                    .build();

            var georeferenceResponse = GeoreferenceResponse
                    .builder()
                    .naturalAddress("Natural Address Response")
                    .gisCommonInfoDir(new GisCommonInfoDir())
                    .build();
            var georeferenceResponseBody = GeoreferenceResponseBody
                    .builder()
                    .georreferenceResponse(georeferenceResponse)
                    .build();

            // when(AddressNormalizeMapper.buildAddressRequest(addressNormalizeRequest)).thenReturn(new
            // GeoreferenceRequest());
            when(georeferenceClient.normalize(any())).thenReturn(georeferenceResponseBody);
            // WHENÑ
            var response = addressService.normalizeAddress(addressNormalizeRequestBody);

            // THEN
            assertNotNull(response);

        }

        @Test
        void normalizeAddressWithNullStratum() {
        // GIVEN
        var addressNormalizeRequest = AddressNormalizeRequest
                .builder()
                .countryCode("1")
                .departmentCode("2")
                .municipalityCode("3")
                .naturalAddress("100 Main St")
                .build();

        var addressNormalizeRequestBody = AddressNormalizeRequestBody
                .builder()
                .addressNormalizeRequest(addressNormalizeRequest)
                .build();

        var georeferenceResponse = GeoreferenceResponse
                .builder()
                .naturalAddress("100 Main St Response")
                .gisCommonInfoDir(new GisCommonInfoDir())
                .build();
        var georeferenceResponseBody = GeoreferenceResponseBody
                .builder()
                .georreferenceResponse(georeferenceResponse)
                .build();

        when(georeferenceClient.normalize(any())).thenReturn(georeferenceResponseBody);
        // WHEN
        var response = addressService.normalizeAddress(addressNormalizeRequestBody);

        // THEN
        assertNotNull(response);
        assertNotNull(response.getAddressNormalizeResponse().getGisCommonInfoDir().getStratum(), "Stratum should not be null");
    }

    @Test
    void normalizeAddressThrowsExceptionWhenClientFails() {
        // GIVEN
        var addressNormalizeRequestBody = AddressNormalizeRequestBody
                .builder()
                .addressNormalizeRequest(AddressNormalizeRequest.builder().build())
                .build();

        when(georeferenceClient.normalize(any())).thenThrow(new RuntimeException("Service unavailable"));
        // WHEN & THEN
        assertThrows(RuntimeException.class, () -> addressService.normalizeAddress(addressNormalizeRequestBody),
                "Expected normalizeAddress to throw, but it didn't");
    }
    }

// getAddress test
    @Nested
    class GetAddressTest {
        @Test
        void validGetAddress() {
            // GIVEN
            var addressId = 1L;
            var addressResponse = AddressResponse.builder().build();

            when(addressClient.getAddress(addressId)).thenReturn(addressResponse);
            // WHEN
            var response = addressService.getAddress(addressId);

            // THEN
            assertNotNull(response);
        }

        @Test
        void getAddressThrowsExceptionWhenClientFails() {
            // GIVEN
            var addressId = 1L;

            when(addressClient.getAddress(addressId)).thenThrow(new RuntimeException("Service unavailable"));
            // WHEN & THEN
            assertThrows(RuntimeException.class, () -> addressService.getAddress(addressId),
                    "Expected getAddress to throw, but it didn't");
        }
    }

    // createAddress test
    @Nested
    class CreateAddressTest {
        @Test
        void validCreateAddress() {
            // GIVEN
            var address = AddressMSResponse.builder().build();
        //     var addressRequest = AddressRequest.builder().build();

            when(addressClient.createAddress(argThat(request -> "Colombia".equals(request.getCountry())))).thenReturn(1L);
            // WHEN
            var response = addressService.createAddress(address);

            // THEN
            assertNotNull(response);
        }

        @Test
        void createAddressThrowsExceptionWhenClientFails() {
            // GIVEN
            var address = AddressMSResponse.builder().build();
            var addressRequest = AddressRequest.builder().build();

            when(addressClient.createAddress(addressRequest)).thenThrow(new RuntimeException("Service unavailable"));
            // WHEN & THEN
            assertThrows(RuntimeException.class, () -> addressService.createAddress(address),
                    "Expected createAddress to throw, but it didn't");
        }
    }

        // updateAddress test
        @Nested
        class UpdateAddressTest {
            @Test
            void validUpdateAddress() {
                // GIVEN
                var addressId = 1L;
                var address = AddressMSResponse.builder().build();
    
                doNothing().when(addressClient).updateAddress(eq(addressId), any(AddressRequest.class));
                // WHEN
                addressService.updateAddress(addressId, address);
    
                // THEN
                assertNotNull(address);
            }
    
            @Test
            void updateAddressThrowsExceptionWhenClientFails() {
                // GIVEN
                var addressId = 1L;
                var address = AddressMSResponse.builder().build();
                var addressRequest = AddressRequest.builder().build();
    
                doThrow(new RuntimeException("Service unavailable")).when(addressClient).updateAddress(addressId, addressRequest);
                // WHEN & THEN
                assertThrows(RuntimeException.class, () -> addressService.updateAddress(addressId, address),
                        "Expected updateAddress to throw, but it didn't");
            }
        }

}
