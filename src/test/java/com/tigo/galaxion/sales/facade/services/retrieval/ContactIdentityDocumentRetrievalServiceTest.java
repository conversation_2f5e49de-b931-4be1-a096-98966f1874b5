package com.tigo.galaxion.sales.facade.services.retrieval;

import com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum;
import com.tigo.galaxion.sales.facade.domain.problem.ContactIdentityDocumentNotFoundProblem;
import com.tigo.galaxion.sales.facade.model.entity.ContactIdentityDocumentEntity;
import com.tigo.galaxion.sales.facade.model.repository.ContactIdentityDocumentRepository;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ContactIdentityDocumentRetrievalServiceTest {

    @Mock
    private ContactIdentityDocumentRepository contactIdentityDocumentRepository;

    @InjectMocks
    private ContactIdentityDocumentRetrievalService contactIdentityDocumentRetrievalService;

    @Nested
    class GetContactIdentityDocumentEntityTest {

        @Test
        void identityDocumentNotExist_throwContactIdentityDocumentNotFoundProblem() {
            //GIVEN
            var prospectRef = "ref";
            when(contactIdentityDocumentRepository.findById(prospectRef)).thenReturn(Optional.empty());

            //WHEN
            //THEN
            assertThrows(ContactIdentityDocumentNotFoundProblem.class, () -> contactIdentityDocumentRetrievalService.getContactIdentityDocumentResponse(prospectRef));
        }
    }

    @Nested
    class GetContactIdentityDocumentResponseTest {

        @Test
        void identityDocumentExist_returnContactIdentityDocumentResponse() {
            //GIVEN
            var prospectRef = "ref";

            var documentIdentifier = "0123456789";
            var documentType = IdDocumentTypeEnum.NIT;
            var nationality = "CO";
            var deliveryContactAddressEntity = ContactIdentityDocumentEntity
                    .builder()
                    .documentIdentifier(documentIdentifier)
                    .documentType(documentType)
                    .nationality(nationality)
                    .build();
            when(contactIdentityDocumentRepository.findById(prospectRef)).thenReturn(Optional.of(deliveryContactAddressEntity));

            //WHEN
            var response = contactIdentityDocumentRetrievalService.getContactIdentityDocumentResponse(prospectRef);

            //THEN
            assertEquals(documentIdentifier, response.getDocumentIdentifier());
            assertEquals(documentType, response.getDocumentType());
            assertEquals(nationality, response.getNationality());
        }
    }
}
