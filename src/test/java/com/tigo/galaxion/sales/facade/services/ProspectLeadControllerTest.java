package com.tigo.galaxion.sales.facade.services;

import java.util.ArrayList;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Address;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.ChannelNotification;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Customer;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.CustomerReference;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Scoring;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.request.ProspectLeadRequest;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponseByEntity;
import com.tigo.galaxion.sales.facade.controller.ProspectLeadController;

public class ProspectLeadControllerTest {

    private MockMvc mockMvc;

    @Mock
    private ProspectLeadService service;

    @InjectMocks
    private ProspectLeadController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    void testGetProspectInfoBaseByEntityParameter() throws Exception {
        String prospectId = "1040948G";
        String entity = "address";
        Integer entityType = 1;
        
        // Crear el objeto de respuesta esperado
        ProspectLeadResponseByEntity response = new ProspectLeadResponseByEntity();
        Address address = new Address();
        address.setAddressType(1);
        address.setDepartmentCode("57");
        address.setDepartment("Antioquia");
        address.setMunicipalityCode("5631000");
        address.setMunicipality("Sabaneta");
        address.setAddress("Cl 33 No. 30-57");
        address.setLatitude("4.9537");
        address.setLongitude("94.8231");
        address.setStratum(1);
        address.setMicrozone("CALDAS");
        address.setCountryCode("57");
        address.setAddressCode("1431065734");
        address.setNeighborhoodName("Barrio 1");
        address.setDescriptionAddress(null);
        // Asigna la dirección al objeto de respuesta
        response.setAddress(new Address[]{address});
        
        when(service.getProspectInfoByEntityType(prospectId, entity, entityType)).thenReturn(response);

        mockMvc.perform(get("/api/v1/prospects-lead/{prospectId}", prospectId)
            .param("entity", entity)
            .param("entityType", entityType.toString()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.address[0].addressType").value(1))
            .andExpect(jsonPath("$.address[0].departmentCode").value("57"))
            .andExpect(jsonPath("$.address[0].department").value("Antioquia"))
            .andExpect(jsonPath("$.address[0].municipalityCode").value("5631000"))
            .andExpect(jsonPath("$.address[0].municipality").value("Sabaneta"))
            .andExpect(jsonPath("$.address[0].address").value("Cl 33 No. 30-57"))
            .andExpect(jsonPath("$.address[0].latitude").value("4.9537"))
            .andExpect(jsonPath("$.address[0].longitude").value("94.8231"))
            .andExpect(jsonPath("$.address[0].stratum").value(1))
            .andExpect(jsonPath("$.address[0].microzone").value("CALDAS"))
            .andExpect(jsonPath("$.address[0].countryCode").value("57"))
            .andExpect(jsonPath("$.address[0].addressCode").value("1431065734"))
            .andExpect(jsonPath("$.address[0].neighborhoodName").value("Barrio 1"))
            .andExpect(jsonPath("$.address[0].descriptionAddress").doesNotExist());

        verify(service, times(1)).getProspectInfoByEntityType(prospectId, entity, entityType);
    }
   
    @Test
    void testGetAllProspectInfo() throws Exception {
        String prospectId = "1040948G";
        ProspectLeadResponse response = new ProspectLeadResponse();
        when(service.getAllProspectInfo(prospectId)).thenReturn(response);

        mockMvc.perform(get("/api/v1/prospects-lead/{prospectId}/all", prospectId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(service, times(1)).getAllProspectInfo(prospectId);
    }

    @SuppressWarnings("deprecation")
    @Test
    void testUpdateAllProspectInfo() throws Exception {
        String prospectId = "1040948G";
        
        // Crear el objeto ProspectLeadRequest con los datos proporcionados
        ProspectLeadRequest request;
    
        Address address = new Address();
        address.setAddressType(1);
        address.setDepartment("ANTIOQUIA");
        address.setMunicipality("SABANETA");
        address.setAddress("KR 43 A # 53 D - 46 SUR IN 1609");
        address.setLatitude("6.157284259796143");
        address.setLongitude("-75.604972839355468");
        address.setStratum(2);
        address.setMicrozone("CALDAS");
        address.setCountryCode("57");
        address.setDepartmentCode("5");
        address.setMunicipalityCode("5631000");
        address.setAddressCode("1431065734");
        address.setNeighborhoodName("Barrio 1");
        address.setDescriptionAddress("1431065734 * KR 43 A # 53 D - 46 SUR IN 1609 * Rural *  CR 43 A  # 53 D SUR - 46, INTERIOR 1609 Los Arias");
    
        Customer customer = new Customer();
        customer.setDocumentType("CC");
        customer.setDocumentId("12321113");
        customer.setExpeditionDate("2023-07-28T08:00:00");
        customer.setNames("Juan");
        customer.setLastName("perez");
        customer.setContactEmail("<EMAIL>");
        customer.setContactPhone("57 ************");
        customer.setAdditionalPhone1("57 ************");
        customer.setAdditionalPhone2("57 ************");
        customer.setAdditionalEmail1("<EMAIL>");
        customer.setAdditionalEmail2("string");
        customer.setHabeasdata(1);
        customer.setInvoiceDelivery("P");
        customer.setAppointmentStartDate("2023-07-28T08:00:00");
        customer.setAppointmentEndDate("2023-07-28T08:00:00");
        customer.setRating(0);
        customer.setContractNumber(null);
    
        CustomerReference customerReference = new CustomerReference();
        customerReference.setReferenceType(1);
        customerReference.setDocumentType("CC");
        customerReference.setDocumentId("12321113");
        customerReference.setNames("Juan");
        customerReference.setLastName("perez");
        customerReference.setEmail("<EMAIL>");
        customerReference.setPhone("57 ************");
    
        Scoring scoring = new Scoring();
        scoring.setFraudResult(99);
        scoring.setRiskResult(99);
        scoring.setAvailableCredit(5000);
    
        ChannelNotification channelNotification = new ChannelNotification();
        channelNotification.setNotificationChannel(1);
        channelNotification.setNotificationId(0);
    
        ArrayList<Address> addressList = new ArrayList<>();
        addressList.add(address);

        ArrayList<CustomerReference> customerReferenceList = new ArrayList<>();
        customerReferenceList.add(customerReference);

        ArrayList<ChannelNotification> channelNotificationList = new ArrayList<>();
        channelNotificationList.add(channelNotification);

        request = new ProspectLeadRequest(
            addressList,
            customer,
            customerReferenceList,
            scoring,
            channelNotificationList
        );
    
        // Convertir el objeto request a JSON
        ObjectMapper objectMapper = new ObjectMapper();
        String requestJson = objectMapper.writeValueAsString(request);
    
        // Capturar el argumento pasado al método updateAllProspectInfo
        ArgumentCaptor<ProspectLeadRequest> captor = ArgumentCaptor.forClass(ProspectLeadRequest.class);
    
        mockMvc.perform(patch("/api/v1/prospects-lead/{prospectId}/all", prospectId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isNoContent());
    
        verify(service, times(1)).updateAllProspectInfo(eq(prospectId), captor.capture());
        
        // Verificar que el objeto capturado es igual al objeto request esperado
        ProspectLeadRequest capturedRequest = captor.getValue();
        
        // Comparación detallada
        assertThat(capturedRequest.customer()).isEqualToComparingFieldByField(request.customer());
        assertThat(capturedRequest.scoring()).isEqualToComparingFieldByField(request.scoring());
        assertNotNull(capturedRequest.address());
        assertNotNull(capturedRequest.customerReference());
        assertNotNull(capturedRequest.channelNotification());
    }

    @Test
    void testUpdateProspectInfoByEntityParameter() throws Exception {
        String prospectId = "1040948G";
        String entity = "address";
        
        // Create a valid JsonNode request
        ObjectMapper objectMapper = new ObjectMapper();
        String requestJson = "{\"key\": \"value\"}";  // Replace this with a valid JSON string
        JsonNode request = objectMapper.readTree(requestJson);

        mockMvc.perform(patch("/api/v1/prospects-lead/{prospectId}", prospectId)
                .param("entity", entity)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isNoContent());

        verify(service, times(1)).updateProspectInfoByEntity(prospectId, entity, request);
    }
} 