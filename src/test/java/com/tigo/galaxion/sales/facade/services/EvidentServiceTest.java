package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.tigo.galaxion.sales.facade.connector.evident.EvidentClient;
import com.tigo.galaxion.sales.facade.connector.evident.domain.AnswerIdentificationVerify;
import com.tigo.galaxion.sales.facade.connector.evident.domain.CodeDataOTP;
import com.tigo.galaxion.sales.facade.connector.evident.domain.ExpeditionDate;
import com.tigo.galaxion.sales.facade.connector.evident.domain.GenerationResult;
import com.tigo.galaxion.sales.facade.connector.evident.domain.GenerationResultGenerate;
import com.tigo.galaxion.sales.facade.connector.evident.domain.Identification;
import com.tigo.galaxion.sales.facade.connector.evident.domain.QuestionnaireData;
import com.tigo.galaxion.sales.facade.connector.evident.domain.QuestionnaireResponse;
import com.tigo.galaxion.sales.facade.connector.evident.domain.RecognizeData;
import com.tigo.galaxion.sales.facade.connector.evident.domain.ValidationResult;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentGenerateRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentIdentificationVerifyRequest;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentInitializeRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentValidationRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.request.EvidentVerifyRequestBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentGenerateResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentIdentificationVerifyResponse;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentInitializeResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentQuestionResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentValidationResponseBody;
import com.tigo.galaxion.sales.facade.connector.evident.domain.response.EvidentVerifyResponseBody;

@ExtendWith(MockitoExtension.class)
public class EvidentServiceTest {
    @Mock
    EvidentClient evidentClient;

    @InjectMocks
    EvidentService evidentService;

    @Test
    void EvidentValidTestIdentificationValid() {
        var request = EvidentValidationRequestBody.builder()
                .identification(Identification.builder()
                        .number("57106159")
                        .type("CC")
                        .build())
                .surname("PAREJA MORALES")
                .names("MARIA PATRICIA")
                .expeditionDate(ExpeditionDate
                        .builder()
                        .timestamp("997056000000")
                        .build())
                .build();

        var expectedResponse = EvidentValidationResponseBody
                .builder()
                .valSurname("true")
                .valName("true")
                .valDateExp("true")
                .excludeCustomer("false")
                .alerts("false")
                .responseAlert("03")
                .codeAlert("00")
                .result("05")
                .validationRegister("366094")
                .numAttemps("")
                .resultProcess("true")
                .availableQueries("1")
                .identification(
                        Identification.builder()
                                .number("00080036190")
                                .type("CC")
                                .build())
                .name("FORERO PARRA FREDDY")
                .expeditionDate(ExpeditionDate.builder().timestamp("986947200000").build())
                .build();

        String name = "FORERO PARRA FREDDY";
        String number = "00080036190";
        String type = "CC";
        String valSurname = "true";
        String valName = "true";
        String valDateExp = "true";
        String excludeCustomer = "false";
        String alerts = "false";
        String responseAlert = "03";
        String codeAlert = "00";
        String result = "05";
        String regValidation = "366094";
        String resultProcess = "true";
        String consultationsAvailable = "1";
        String expeditionDate = "986947200000";

        when(evidentClient.validateIdentification((any()))).thenReturn(expectedResponse);
        var actualResponse = evidentService.validateIdentification(request);
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        assertEquals(number, actualResponse.getIdentification().getNumber());
        assertEquals(type, actualResponse.getIdentification().getType());
        assertEquals(name, actualResponse.getName());
        assertEquals(valSurname, actualResponse.getValSurname());
        assertEquals(valName, actualResponse.getValName());
        assertEquals(valDateExp, actualResponse.getValDateExp());
        assertEquals(excludeCustomer, actualResponse.getExcludeCustomer());
        assertEquals(alerts, actualResponse.getAlerts());
        assertEquals(responseAlert, actualResponse.getResponseAlert());
        assertEquals(codeAlert, actualResponse.getCodeAlert());
        assertEquals(result, actualResponse.getResult());
        assertEquals(regValidation, actualResponse.getValidationRegister());
        assertEquals(resultProcess, actualResponse.getResultProcess());
        assertEquals(consultationsAvailable, actualResponse.getAvailableQueries());
        assertEquals(expeditionDate, actualResponse.getExpeditionDate().getTimestamp());
    }

    @Test
    void EvidentInitializeTest() {
        var request = EvidentInitializeRequestBody.builder()
                .Identification(Identification.builder()
                        .number("57106159")
                        .type("CC")
                        .build())
                .questionnaireData(
                        QuestionnaireData.builder()
                                .validationRegister("8635478")
                                .evidentProcess("VALDCN")
                                .build())
                .build();

        var expectedResponse = EvidentInitializeResponseBody.builder()
                .questionnaireData(
                        QuestionnaireData.builder()
                                .evidentProcess("8635478")
                                .validationRegister("VALDCN")
                                .build())
                .generationResult(
                        GenerationResult.builder()
                                .codeResultOTP("223")
                                .transactionIDOTP("23211")
                                .resultOTP("true")
                                .build())
                .build();

        when(evidentClient.initializeOtp((any()))).thenReturn(expectedResponse);
        var actualResponse = evidentService.initializeOtp(request);
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    void EvidentGenerateOtpTest() {
        var request = EvidentGenerateRequestBody.builder()
                .identification(Identification.builder()
                        .number("232323")
                        .type("CC")
                        .build())
                .questionnaireData(QuestionnaireData
                        .builder()
                        .validationRegister("8635478")
                        .evidentProcess("VALDCN")
                        .build())
                .recognizeData(RecognizeData
                        .builder()
                        .mobilePhoneNumber("3106924306")
                        .build())
                .codeDataOTP(
                        CodeDataOTP
                                .builder()
                                .transactionIDOTP("12356")
                                .build())
                .build();

        var expectedResponse = EvidentGenerateResponseBody.builder()
                .validationResult(ValidationResult.builder()
                        .rankingRecognize("")
                        .typevalidNumber("")
                        .build())
                .generationResult(GenerationResultGenerate.builder()
                        .codeResultOTP("null")
                        .transactionIDOTP("")
                        .resultOTP("false")
                        .timestampOTP("")
                        .requireQuestionnaire("")
                        .build())
                .questionnaireData(
                        QuestionnaireResponse.builder()
                                .evidentProcess("")
                                .build())
                .build();

        when(evidentClient.generateOtp((any()))).thenReturn(expectedResponse);
        var actualResponse = evidentService.generateOtp(request);
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    void EvidentVerifyOtpTest() {
        var request = EvidentVerifyRequestBody.builder()
                .identificacion(Identification.builder()
                        .number("211212")
                        .type("CC")
                        .build())
                .questionnaireData(QuestionnaireData
                        .builder()
                        .validationRegister(null)
                        .evidentProcess("VALDCN")
                        .build())
                .codeOTP("2211")
                .transactionIDOTP("2231")
                .build();

        var expectedResponse = EvidentVerifyResponseBody
                .builder()
                .validationResult("")
                .validationMessage("")
                .validCode("")
                .transactionIDOTP("")
                .build();

        when(evidentClient.verifyOtp((any()))).thenReturn(expectedResponse);
        var actualResponse = evidentService.verifyOtp(request);
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    void EvidenQuestionIdentification() {
        var identification = "CC";
        var typeIdentification = "";
        var validationRegister = "120";
        var onlyQuestionnaire = "true"; 

        var expectedResponse = EvidentQuestionResponseBody.builder()
        .question(null)
        .alerts("false")
        .codeAlert("")
        .excludeCustomer("false")
        .id("")
        .record("4760613")
        .responseAlert("")
        .result("02")
        .build();

        when(evidentClient.questionIdentification(identification, validationRegister, typeIdentification, onlyQuestionnaire)).thenReturn(expectedResponse);
        var actualResponse = evidentService.questionIdentification(identification, validationRegister, typeIdentification, onlyQuestionnaire);
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
    }

    List<AnswerIdentificationVerify> answers = Arrays.asList(
        new AnswerIdentificationVerify("01", "001"),
        new AnswerIdentificationVerify("02", "006")
    );


    @Test
    void EvidentIdentificationVerify(){
        var request = EvidentIdentificationVerifyRequest.builder()
            .idQuestionary(null)
            .regQuestionary("null")
            .Identification(Identification.builder()
                .number("null")
                .type("null")
                .build()
            ).onlyQuestionnaire("true")
            .answer(answers)
            .build();
        
        var expectedResponse = EvidentIdentificationVerifyResponse.builder()
        .result("false")
        .approval("false")
        .completeQuestions("false")
        .score("000")
        .securityCode("0")
        .idQuestionary("22")
        .regQuestionary("4760616")
        .approved100PercentOK("false")
        .build(); 

        when(evidentClient.verifyIdentification((any()))).thenReturn(expectedResponse);
        var actualResponse = evidentService.verifyIdentification(request);

        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
            

    }
    
}
