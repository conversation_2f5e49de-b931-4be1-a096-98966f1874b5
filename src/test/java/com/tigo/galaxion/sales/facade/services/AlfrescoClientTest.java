package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.alfresco.AlfrescoClient;
import com.tigo.galaxion.sales.facade.connector.alfresco.request.AlfrescoDocumentRequest;
import com.tigo.galaxion.sales.facade.connector.alfresco.request.AlfrescoGenerateTokenRequest;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoAuthResponse;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoDocumentResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.jupiter.api.Assertions.assertEquals;

import static org.mockito.Mockito.*;

class AlfrescoClientTest {

    @Mock
    private AlfrescoClient alfrescoClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getToken() {
        AlfrescoAuthResponse mockResponse = new AlfrescoAuthResponse();

        var body = AlfrescoGenerateTokenRequest.builder().username("username")
                .password("password").grant_type("password").build();

        when(alfrescoClient.getToken("authorization", body)).thenReturn(mockResponse);

        AlfrescoAuthResponse response = alfrescoClient.getToken("authorization", body);
        assertEquals(mockResponse, response);
    }

    @Test
    void createFile() {
        AlfrescoDocumentRequest request = new AlfrescoDocumentRequest();
        AlfrescoDocumentResponse mockResponse = new AlfrescoDocumentResponse();
        when(alfrescoClient.createFile("authorization", request)).thenReturn(mockResponse);

        AlfrescoDocumentResponse response = alfrescoClient.createFile("authorization", request);
        assertEquals(mockResponse, response);
    }
}
