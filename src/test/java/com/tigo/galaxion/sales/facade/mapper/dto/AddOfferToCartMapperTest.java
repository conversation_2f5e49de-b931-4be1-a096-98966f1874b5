package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.AcquisitionProspectsAddOfferToCartRequest;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.request.CrossSellAddOfferToCartRequest;
import com.tigo.galaxion.sales.facade.domain.enumeration.PortInTypeEnum;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferToAcquisitionProspectsCartRequest;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferToCrossSellCartRequest;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class AddOfferToCartMapperTest {

    @Test
    void buildCrossSellAddOfferToCartRequest() {

        AddOfferToCrossSellCartRequest sourceRequest = AddOfferToCrossSellCartRequest.builder()
                .catalogOfferCode("GOMO")
                .catalogTariffPlanCode("BASIC-24")
                .portInType(PortInTypeEnum.PREPAY_EXTERNAL)
                .parentOfferId(100L)
                .parentSubscriptionId(1L)
                .installationAddressId(800L)
                .build();

        CrossSellAddOfferToCartRequest result = AddOfferToCartMapper.buildCrossSellAddOfferToCartRequest(sourceRequest);

        assertNotNull(result);
        assertEquals(sourceRequest.getCatalogOfferCode(), result.getCatalogOfferCode());
        assertEquals(sourceRequest.getCatalogTariffPlanCode(), result.getCatalogTariffPlanCode());
        assertEquals(sourceRequest.getPortInType(), result.getPortInType());
        assertEquals(sourceRequest.getParentOfferId(), result.getParentOfferId());
        assertEquals(sourceRequest.getParentSubscriptionId(), result.getParentSubscriptionId());
        assertEquals(sourceRequest.getInstallationAddressId(), result.getInstallationAddressId());
    }

    @Test
    void buildAcquisitionProspectsAddOfferToCartRequest() {

        AddOfferToAcquisitionProspectsCartRequest sourceRequestWithId = AddOfferToAcquisitionProspectsCartRequest
                .builder()
                .catalogOfferCode("GOMO")
                .catalogTariffPlanCode("BASIC-24")
                .portInType(PortInTypeEnum.NO_PORT_IN)
                .installationAddressId(1L)
                .build();

        AcquisitionProspectsAddOfferToCartRequest resultWithId = AddOfferToCartMapper
                .buildAcquisitionProspectsAddOfferToCartRequest(sourceRequestWithId);

        assertNotNull(resultWithId);
        assertEquals(sourceRequestWithId.getCatalogOfferCode(), resultWithId.getCatalogOfferCode());
        assertEquals(sourceRequestWithId.getCatalogTariffPlanCode(), resultWithId.getCatalogTariffPlanCode());
        assertEquals(sourceRequestWithId.getPortInType(), resultWithId.getPortInType());
        assertEquals(sourceRequestWithId.getInstallationAddressId(), resultWithId.getInstallationAddressId());

        AddOfferToAcquisitionProspectsCartRequest sourceRequestNullId = AddOfferToAcquisitionProspectsCartRequest
                .builder()
                .catalogOfferCode("GOMO")
                .catalogTariffPlanCode("BASIC-24")
                .portInType(PortInTypeEnum.NO_PORT_IN)
                .build();

        AcquisitionProspectsAddOfferToCartRequest resultNullId = AddOfferToCartMapper
                .buildAcquisitionProspectsAddOfferToCartRequest(sourceRequestNullId);

        assertNotNull(resultNullId);
        assertEquals(sourceRequestNullId.getCatalogOfferCode(), resultNullId.getCatalogOfferCode());
        assertEquals(sourceRequestNullId.getCatalogTariffPlanCode(), resultNullId.getCatalogTariffPlanCode());
        assertEquals(sourceRequestNullId.getPortInType(), resultNullId.getPortInType());
        assertNull(resultNullId.getInstallationAddressId()); // Assert null for addressOfferId

    }

}
