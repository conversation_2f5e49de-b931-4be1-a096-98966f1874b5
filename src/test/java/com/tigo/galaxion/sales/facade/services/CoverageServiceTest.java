package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.tigo.galaxion.sales.facade.connector.coverage.CoverageClient;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.request.CoverageRequest;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.response.CoverageResponse;

@ExtendWith(MockitoExtension.class)
public class CoverageServiceTest {

    @Mock
    CoverageClient coverageClient;

    @InjectMocks
    CoverageService coverageService;

    @Test
    void coverageValidTest() {
        // GIVEN
        var request = CoverageRequest.builder()
                .idBarrio("718")
                .idCalle("51494")
                .idCasa("316265")
                .build();

        var expectedResponse = CoverageResponse.builder()
                .response("complete")
                .message("Hay cobertura")
                .successful(true)
                .tecnologia("HFC")
                .coordenadaX("9.04544")
                .coordenadaY("-79.45744")
                .segmento("STDALHFC")
                .zona("PA-JUAN DIAZ")
                .build();

        when(coverageClient.getCoverage(any(), any(), any())).thenReturn(expectedResponse);

        // WHEN
        var actualResponse = coverageService.getCoverage(request.getIdBarrio(), request.getIdCalle(), request.getIdCasa());

        // THEN
        assertNotNull(actualResponse);
        assertNotNull(actualResponse.getResponse());
        assertNotNull(actualResponse.getMessage());
        assertNotNull(actualResponse.getTecnologia());
        assertNotNull(actualResponse.getCoordenadaX());
        assertNotNull(actualResponse.getCoordenadaY());
        assertNotNull(actualResponse.getSegmento());
        assertNotNull(actualResponse.getZona());

        assertTrue(actualResponse.isSuccessful());
    }
}