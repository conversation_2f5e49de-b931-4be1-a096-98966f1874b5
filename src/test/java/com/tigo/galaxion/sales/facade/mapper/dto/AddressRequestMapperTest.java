package com.tigo.galaxion.sales.facade.mapper.dto;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoAddressRequest;
import com.tigo.galaxion.sales.facade.connector.address.domain.request.AddressRequest;
import com.tigo.galaxion.sales.facade.domain.response.AddressMSResponse;
import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.JsonNode;

@ExtendWith(MockitoExtension.class)
class AddressRequestMapperTest {

    @Test
    void buildAddressRequest_completeInput_mapsCorrectly() {

        TigoAddressRequest tigoAddressRequest = mock(TigoAddressRequest.class);
        when(tigoAddressRequest.getArea()).thenReturn("1");
        when(tigoAddressRequest.getTown()).thenReturn("Sabaneta");
        when(tigoAddressRequest.getStreetName()).thenReturn("13");
        when(tigoAddressRequest.getStreetNumber()).thenReturn("Carrera 46");
        when(tigoAddressRequest.getPostCode()).thenReturn("123456");

        AddressRequest result = AddressRequestMapper.buildAddressRequest(tigoAddressRequest);

        assertEquals("1", result.getArea());
        assertEquals("Sabaneta", result.getTown());
        assertEquals("13", result.getStreet());
        assertEquals("Carrera 46", result.getStreetNumber());
        assertEquals("123456", result.getCode());
        assertEquals("Carrera 46 13", result.getAddressLine1());
    }

    @Test
    void buildAddressCompRequest_completeInput_mapsCorrectly() {

        AddOfferAddressRequest offerAddressRequest = mock(AddOfferAddressRequest.class);
        when(offerAddressRequest.getCountryCode()).thenReturn(57);
        when(offerAddressRequest.getDepartmentCode()).thenReturn(1);
        when(offerAddressRequest.getMunicipalityCode()).thenReturn(1);
        when(offerAddressRequest.getNormalizedAddress()).thenReturn("CA 46 # 75");
        when(offerAddressRequest.getLatitude()).thenReturn(14.6085);
        when(offerAddressRequest.getLongitude()).thenReturn(-90.5252);
        when(offerAddressRequest.getStratum()).thenReturn(3);

        AddressRequest result = AddressRequestMapper.buildAddressRequest(offerAddressRequest);

        assertEquals("57", result.getCounty());
        assertEquals("1", result.getTown());
        assertEquals("1", result.getArea());
        assertEquals("CA 46 # 75", result.getStreet());
        assertEquals("14.6085", result.getAddressLine1());
        assertEquals("-90.5252", result.getAddressLine2());
        assertEquals("3", result.getAddressLine3());
    }

    @Test
    void buildAddressCompRequest_normalizedAddressNull_mapsCorrectly() {

        AddOfferAddressRequest offerAddressRequest = mock(AddOfferAddressRequest.class);
        when(offerAddressRequest.getCountryCode()).thenReturn(57);
        when(offerAddressRequest.getDepartmentCode()).thenReturn(1);
        when(offerAddressRequest.getMunicipalityCode()).thenReturn(1);
        when(offerAddressRequest.getLatitude()).thenReturn(14.6085);
        when(offerAddressRequest.getLongitude()).thenReturn(-90.5252);
        when(offerAddressRequest.getStratum()).thenReturn(1);

        AddressRequest result = AddressRequestMapper.buildAddressRequest(offerAddressRequest);

        assertEquals("57", result.getCounty());
        assertEquals("1", result.getTown());
        assertEquals("1", result.getArea());
        assertNull(result.getStreet());
        assertEquals("14.6085", result.getAddressLine1());
        assertEquals("-90.5252", result.getAddressLine2());
        assertEquals("1", result.getAddressLine3());
    }

    // Test for buildAddressRequestForMS
    @Test
    void buildAddressRequestForMS_completeInput_mapsCorrectly() {

        AddressMSResponse addressMSResponse = mock(AddressMSResponse.class);
        when(addressMSResponse.getCountry()).thenReturn("Colombia");
        when(addressMSResponse.getCountryCode()).thenReturn("57");
        when(addressMSResponse.getDepartment()).thenReturn("Antioquia");
        when(addressMSResponse.getMunicipality()).thenReturn("Sabaneta");
        when(addressMSResponse.getLatitude()).thenReturn("6.157284259796143");
        when(addressMSResponse.getStratum()).thenReturn("2");
        when(addressMSResponse.getLongitude()).thenReturn("-75.60497283935547");
        when(addressMSResponse.getAddress()).thenReturn("KR 43 A # 53 D - 46 SUR IN 1609");
        when(addressMSResponse.getMicrozone()).thenReturn("Caldas");
        when(addressMSResponse.getAddressCode()).thenReturn("1431065734");

        AddressRequest result = AddressRequestMapper.buildAddressRequestForMS(addressMSResponse);

        assertEquals("Colombia", result.getCountry());
        assertEquals("57", result.getArea());
        assertEquals("Antioquia", result.getTown());
        assertEquals("Sabaneta", result.getStreet());
        assertEquals("6.157284259796143", result.getStreetNumber());
        assertEquals("2", result.getStreetQualifier());
        assertEquals("-75.60497283935547", result.getCode());
        assertEquals("KR 43 A # 53 D - 46 SUR IN 1609", result.getAddressLine1());
        assertEquals("Caldas", result.getAddressLine2());
        assertEquals("1431065734", result.getAddressLine3());
    }

    // Test for buildAddressResponseForMS
    @Test
    void buildAddressResponseForMS_completeInput_mapsCorrectly() {

        AddressResponse addressResponse = mock(AddressResponse.class);
        when(addressResponse.getCountry()).thenReturn("Colombia");
        when(addressResponse.getArea()).thenReturn("57");
        when(addressResponse.getTown()).thenReturn("Antioquia");
        when(addressResponse.getStreet()).thenReturn("Sabaneta");
        when(addressResponse.getStreetNumber()).thenReturn("6.157284259796143");
        when(addressResponse.getStreetQualifier()).thenReturn("2");
        when(addressResponse.getCode()).thenReturn("-75.60497283935547");
        when(addressResponse.getAddressLine1()).thenReturn("KR 43 A # 53 D - 46 SUR IN 1609");
        when(addressResponse.getAddressLine2()).thenReturn("Caldas");
        when(addressResponse.getAddressLine3()).thenReturn("1431065734");

        AddressMSResponse result = AddressRequestMapper.buildAddressResponseForMS(addressResponse);

        assertEquals("Colombia", result.getCountry());
        assertEquals("57", result.getCountryCode());
        assertEquals("Antioquia", result.getDepartment());
        assertEquals("Sabaneta", result.getMunicipality());
        assertEquals("6.157284259796143", result.getLatitude());
        assertEquals("2", result.getStratum());
        assertEquals("-75.60497283935547", result.getLongitude());
        assertEquals("KR 43 A # 53 D - 46 SUR IN 1609", result.getAddress());
        assertEquals("Caldas", result.getMicrozone());
        assertEquals("1431065734", result.getAddressCode());
    }

    // Test for builAddressMSResponseForJsonNode
    @Test
    void builAddressMSResponseForJsonNode_completeInput_mapsCorrectly() {

        JsonNode jsonNode = mock(JsonNode.class);
        when(jsonNode.get("countryCode")).thenReturn(null);
        when(jsonNode.get("department")).thenReturn(null);
        when(jsonNode.get("municipality")).thenReturn(null);
        when(jsonNode.get("latitude")).thenReturn(null);
        when(jsonNode.get("stratum")).thenReturn(null);
        when(jsonNode.get("longitude")).thenReturn(null);
        when(jsonNode.get("address")).thenReturn(null);
        when(jsonNode.get("microzone")).thenReturn(null);
        when(jsonNode.get("addressCode")).thenReturn(null);

        AddressMSResponse result = AddressRequestMapper.builAddressMSResponseForJsonNode(jsonNode);

        assertEquals("Colombia", result.getCountry());
        assertNull(result.getCountryCode());
        assertNull(result.getDepartment());
        assertNull(result.getMunicipality());
        assertNull(result.getLatitude());
        assertNull(result.getStratum());
        assertNull(result.getLongitude());
        assertNull(result.getAddress());
        assertNull(result.getMicrozone());
        assertNull(result.getAddressCode());
    }
}
