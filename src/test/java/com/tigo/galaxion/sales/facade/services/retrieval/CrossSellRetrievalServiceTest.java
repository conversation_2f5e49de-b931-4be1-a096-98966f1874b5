package com.tigo.galaxion.sales.facade.services.retrieval;

import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.problem.CrossSellProblem;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.OfferTypeEnum;
import com.tigo.galaxion.sales.facade.domain.response.TigoCrossSellResponse;
import com.tigo.galaxion.sales.facade.model.entity.ProspectEntity;
import com.tigo.galaxion.sales.facade.model.repository.ProspectRepository;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CrossSellRetrievalServiceTest {

    @Mock
    private CrossSellClient crossSellClient;
    @Mock
    private ProspectRepository prospectRepository;

    @InjectMocks
    private CrossSellRetrievalService crossSellRetrievalService;

    @Nested
    class GetProspectResponseTest {

        @Test
        void getCrossSell_crossSellWithContractSignatureOption_returnCrossSell() {
            //GIVEN
            var contractSignatureOption = "SCRIVE";
            var crossSellResponse = buildCrossSellResponse();
            when(crossSellClient.getCrossSell(crossSellResponse.getReference())).thenReturn(crossSellResponse);
            when(prospectRepository.findByReference(crossSellResponse.getReference())).thenReturn(
                    Optional.of(ProspectEntity.builder().contractSignatureOption(contractSignatureOption).build())
            );

            //WHEN
            var tigoCrossSellResponse = crossSellRetrievalService.getCrossSell(crossSellResponse.getReference());

            //THEN
            assertEquals(contractSignatureOption, tigoCrossSellResponse.getContractSignatureOption());
            assertCrossSell(crossSellResponse, tigoCrossSellResponse);
        }

        @Test
        void getCrossSell_crossSellWithoutContractSignatureOption_returnCrossSell() {
            //GIVEN
            var crossSellResponse = buildCrossSellResponse();
            when(crossSellClient.getCrossSell(crossSellResponse.getReference())).thenReturn(crossSellResponse);
            when(prospectRepository.findByReference(crossSellResponse.getReference())).thenReturn(Optional.empty());

            //WHEN
            var tigoCrossSellResponse = crossSellRetrievalService.getCrossSell(crossSellResponse.getReference());

            //THEN
            assertNull(tigoCrossSellResponse.getContractSignatureOption());
            assertCrossSell(crossSellResponse, tigoCrossSellResponse);
        }


        @Test
        void getCrossSell_crossSellNotFound_throwException() {
            //GIVEN
            var crossSellRef = "ref";
            when(crossSellClient.getCrossSell(crossSellRef)).thenThrow(CrossSellProblem.class);

            //WHEN
            //THEN
            assertThrows(CrossSellProblem.class, () -> crossSellRetrievalService.getCrossSell(crossSellRef));
        }

        private void assertCrossSell(CrossSellResponse crossSellResponse, TigoCrossSellResponse tigoCrossSellResponse) {
            assertEquals(crossSellResponse.getBrand(), tigoCrossSellResponse.getBrand());
            assertEquals(crossSellResponse.getCartUuid(), tigoCrossSellResponse.getCartUuid());
            assertEquals(crossSellResponse.getChannelCode(), tigoCrossSellResponse.getChannelCode());
            assertEquals(crossSellResponse.getChannelGroup(), tigoCrossSellResponse.getChannelGroup());
            assertEquals(crossSellResponse.getOfferType(), tigoCrossSellResponse.getOfferType());
            assertEquals(crossSellResponse.getCustomerType(), tigoCrossSellResponse.getCustomerType());
            assertEquals(crossSellResponse.getReference(), tigoCrossSellResponse.getReference());
            assertEquals(crossSellResponse.getAccountId(), tigoCrossSellResponse.getAccountId());
        }

        private CrossSellResponse buildCrossSellResponse() {
            return CrossSellResponse.builder()
                    .brand("EPIC")
                    .cartUuid("1012ZX48")
                    .channelGroup("TELESALES")
                    .channelCode("BASIC_TLS")
                    .offerType(OfferTypeEnum.POSTPAY)
                    .customerType("RESIDENTIAL")
                    .reference("V8WMXQV0")
                    .accountId("********")
                    .build();
        }

    }

}
