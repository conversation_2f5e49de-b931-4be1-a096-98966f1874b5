package com.tigo.galaxion.sales.facade.factory;

import java.util.List;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AdditionalAllowedOfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.RecurringAmountByOccurrenceResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.AcquisitionTypeEnum;

public class CartResponseFactroy {

	public static CartResponse getCartResponse(Long longs, String strings) {
		return CartResponse.builder()
            .uuid(strings)
            .acquisitionType(AcquisitionTypeEnum.NEW_ACQUISITION)
            .creditScore(strings)
            .agreedTermsAndConditions(true)
            .amountVatIncluded(AmountResponseFactory.getAmountResponse(longs))
            .amountVatExcluded(AmountResponseFactory.getAmountResponse(longs))
            .offers(List.of(
				OfferResponseFactory.getOfferResponse(1L, 1, "1"),
				OfferResponseFactory.getOfferResponse(2L, 2, "2")
			))
            .channelGroup("channelGroup")
            .numberAdditionalAllowedOffers(List.of(
				AdditionalAllowedOfferResponse.builder()
                    .serviceGroup("serviceGroup")
                    .number(1L)
                    .build(),
				AdditionalAllowedOfferResponse.builder()
                    .serviceGroup("serviceGroup")
                    .number(2L)
                    .build()
				)
			)
            .recurringAmountsByOccurrence(List.of(
				RecurringAmountByOccurrenceResponse.builder()
				.amountVatIncluded(longs)
				.amountVatExcluded(longs)
				.occurrence(1)
				.build()
				)
			)
            .build();
	}
}
