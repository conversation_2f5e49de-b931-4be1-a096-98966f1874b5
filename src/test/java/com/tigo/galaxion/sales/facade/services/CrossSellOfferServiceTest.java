package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.domain.enumeration.PortInTypeEnum;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferToCrossSellCartRequest;
import com.tigo.galaxion.sales.facade.mapper.dto.TigoCartResponseMapper;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CrossSellOfferServiceTest {

    @InjectMocks
    private CrossSellOfferService crossSellOfferService;

    @Mock
    private CrossSellClient crossSellClient;

    @Mock
    private OfferSimDeliveryChoiceService offerSimDeliveryChoiceService;

    @Mock
    private TigoCartResponseMapper tigoCartResponseMapper;

    @Nested
    class AddOfferTest {

        @Test
        void validData_returnCartResponse() {
            // GIVEN
            var prospectRef = "RO6EW4O9";
            var request = AddOfferToCrossSellCartRequest.builder()
                                                        .catalogOfferCode("GOMO")
                                                        .catalogTariffPlanCode("BASIC-24")
                                                        .portInType(PortInTypeEnum.NO_PORT_IN)
                                                        .parentOfferId(1L)
                                                        .build();

            var cartResponse = CartResponse.builder().build();
            when(crossSellClient.addOfferToCart(anyString(), any())).thenReturn(cartResponse);

            // WHEN
            var response = crossSellOfferService.addOffer(prospectRef, request);

            // THEN
            verify(crossSellClient).addOfferToCart(anyString(), any());
            verify(tigoCartResponseMapper).buildTigoCartResponse(cartResponse);
        }

    }

    @Nested
    class DeleteOfferTest {

        @Test
        void validData_returnCartResponse() {
            // GIVEN
            var prospectRef = "RO6EW4O9";
            var offerId = 1L;

            var cartResponse = CartResponse.builder().build();
            when(crossSellClient.deleteOfferToCart(prospectRef, offerId)).thenReturn(cartResponse);

            // WHEN
            var response = crossSellOfferService.deleteOffer(prospectRef, offerId);

            // THEN
            verify(crossSellClient).deleteOfferToCart(prospectRef, offerId);
            verify(offerSimDeliveryChoiceService).delete(offerId);
            verify(tigoCartResponseMapper).buildTigoCartResponse(cartResponse);
        }

    }
}
