package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.TigoSalesFacadeApplication;
import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogEntity;
import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogId;
import com.tigo.galaxion.sales.facade.model.repository.ProspectusLogRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
 
@SpringBootTest(classes = TigoSalesFacadeApplication.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {
        "wfe.signal.upfrontpayment=WAIT_PAYUPFRONT",
        "wfe.signal.SP_WAIT_BB_PROV_REQUEST=SOM_WAIT_PROVISIONING",
        "wfe.signal.SP_DEVICE_REGISTRATION_BB_SYMPHONICA=someSignalName",
        "symphonica.statuses=CANCELLED,REJECTED,PARTIAL,FAILED"
})
public class ProspectusLogRepositoryIntegrationTest {

    @Autowired
    private ProspectusLogRepository prospectLogRepository;

    @Test
    public void testSaveProspectusLog() {
        LocalDateTime create_at = LocalDateTime.now(); // Obtiene la hora actual con zona horaria

        // Arrange
        ProspectusLogId prospectusLogId = ProspectusLogId.builder()
                .reference("12345")
                .customer_id_type("DNI")
                .customer_id_number("987654321")
                .build();

        ProspectusLogEntity prospectusLogEntity = ProspectusLogEntity.builder()
                .prospectusLogId(prospectusLogId)
                .user_id("user1")
                .user_name("John Doe")
                .ip_equipment("***********")
                .sales_channel("Online")
                .authentication_type("OTP")
                .transaction("Purchase")
                .createdAt(create_at)
                .build();

        // Act
        ProspectusLogEntity savedEntity = prospectLogRepository.save(prospectusLogEntity);

        // Assert
        assertThat(savedEntity).isNotNull();
        assertThat(savedEntity.getProspectusLogId().getReference()).isEqualTo("12345");
        assertThat(savedEntity.getUser_id()).isEqualTo("user1");
    }
}
