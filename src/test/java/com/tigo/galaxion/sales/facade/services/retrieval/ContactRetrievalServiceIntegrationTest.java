package com.tigo.galaxion.sales.facade.services.retrieval;

import com.tigo.galaxion.sales.facade.TigoSalesFacadeApplicationTest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.problem.AcquisitionProspectContactNotFoundProblem;
import com.tigo.galaxion.sales.facade.connector.contact.ContactClient;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactPermissionGroupV2Response;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactPermissionListV2Response;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactPermissionV2Response;
import com.tigo.galaxion.sales.facade.domain.response.TigoContactResponse;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = TigoSalesFacadeApplicationTest.class)
@ActiveProfiles(profiles = "test")
@Transactional
@TestPropertySource(properties = {
    "wfe.signal.SP_DEVICE_REGISTRATION_BB_SYMPHONICA=someSignalName",
    "symphonica.statuses=CANCELLED,REJECTED,PARTIAL,FAILED"
})
class ContactRetrievalServiceIntegrationTest {

    @Autowired
    private ContactRetrievalService contactRetrievalService;

    @Autowired
    private AcquisitionProspectClient acquisitionProspectClient;

    @Autowired
    private ContactClient contactClient;

    @Nested
    class GetContactResponseTest {

        @Test
        @Sql(scripts = {"classpath:sql/identity-document/insert-identity-document.sql"})
        void contactExist_returnContactResponse() {
            // GIVEN
            var prospectReference = "ref01";
            var tigoContactResponse = TigoContactResponse.builder().build();
            when(acquisitionProspectClient.getContact(prospectReference)).thenReturn(tigoContactResponse);

            // WHEN
            var response = contactRetrievalService.getContactResponseForAcquisition(prospectReference);

            // THEN
            assertEquals(tigoContactResponse, response);
        }

        @Test
        void contactNotExist_returnContactResponse() {
            // GIVEN
            var prospectReference = "RO6EW4O9";
            when(acquisitionProspectClient.getContact(prospectReference)).thenThrow(AcquisitionProspectContactNotFoundProblem.class);
            var contactPermissionGroupV2Responses = Collections.singletonList(ContactPermissionGroupV2Response.builder()
                                                                                                              .name("permissionNameGroup")
                                                                                                              .permissionGroup("permissionGroup")
                                                                                                              .build());
            var contactPermissionV2Responses = Collections.singletonList(ContactPermissionV2Response.builder()
                                                                                                    .name("permissionName")
                                                                                                    .permission("permission")
                                                                                                    .build());
            var contactPermissionListV2Response = ContactPermissionListV2Response.builder()
                                                                                 .permissionGroups(contactPermissionGroupV2Responses)
                                                                                 .permissions(contactPermissionV2Responses)
                                                                                 .build();
            when(contactClient.getPermissions()).thenReturn(contactPermissionListV2Response);

            // WHEN
            var response = contactRetrievalService.getContactResponseForAcquisition(prospectReference);

            // THEN
            assertNotNull(response);

            var permissions = response.getPermissions();
            assertNotNull(permissions);
            assertFalse(permissions.isAllowThirdParty());
            assertFalse(permissions.getPermissionGroups().isEmpty());

        }
    }
}
