package com.tigo.galaxion.sales.facade.factory;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.DirectoryPreferenceEnum;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.ChargeResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.EquipmentFinancingResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.EquipmentResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.OfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.PortInResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.PricePlanResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.RecurringAmountByOccurrenceResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.SimCardResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.SubsidyResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.equipments.InclusiveEquipmentResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;

public class OfferResponseFactory {

	public static OfferResponse getOfferResponse(Long longs, int ints, String strings) {
		return OfferResponse.builder()
        .id(longs)
        .baseOffer(BaseOfferResponseFactory.getBaseOfferResponse(longs, strings))
        .amountVatIncluded(AmountResponseFactory.getAmountResponse(longs))
        .amountVatExcluded(AmountResponseFactory.getAmountResponse(longs))
        .addOns(List.of(
			AddOnResponseFactory.getAddOnResponse(1L, "1"),
			AddOnResponseFactory.getAddOnResponse(2L, "2")
        ))
		.discounts(List.of(
			
		))
        .simCard(SimCardResponse.builder()
            .msisdn("123456789")
            .iccid("123456789")
            .imsi("123456789")
            .build()
        )
        .topUpAmount(longs)
        .number(strings)
        .directoryPreference(DirectoryPreferenceEnum.LISTED)
        .applyDeposit(true)
        .depositAmountVatIncluded(longs)
        .depositAmountVatExcluded(longs)
        .depositTerm(longs)
        .equipments(List.of(EquipmentResponse.builder()
            .id(longs)
            .catalogCode(strings)
            .imei(strings)
            .amountVatIncluded(AmountResponseFactory.getAmountResponse(longs))
            .amountVatExcluded(AmountResponseFactory.getAmountResponse(longs))
            .color(strings)
            .colorCode(strings)
            .description(strings)
            .manufacturer(strings)
            .model(strings)
            .inventoryCode(strings)
            .charge(ChargeResponse.builder()
                .catalogCode(strings)
                .pricePlan(PricePlanResponse.builder()
                    .catalogCode(strings)
                    .amountVatIncluded(longs)
                    .amountVatExcluded(longs)
                    .build()
                )
                .build()
            )
            .addon(AddOnResponseFactory.getAddOnResponse(1L, "1"))
            .type(EquipmentTypeEnum.RESIDENTIAL_GATEWAY)
            .subsidy(SubsidyResponse.builder()
                .amountVatIncluded(longs)
                .amountVatExcluded(longs)
                .build()
            )
            .subsidyApplied(true)
            .equipmentFinancing(EquipmentFinancingResponse.builder()
                .id(longs)
                .code(strings)
                .occurrence(longs)
                .amountUpFrontChosen(longs)
                .build()
            ).build()
        ))
        .inclusiveEquipments(List.of(InclusiveEquipmentResponse.builder()
            .main(true)
            .catalogCode(strings)
            .inclusiveEquipmentCatalogCode(strings)
            .color(strings)
            .colorCode(strings)
            .description(strings)
            .manufacturer(strings)
            .model(strings)
            .inventoryCode(strings)
            .type(EquipmentTypeEnum.ACCESSORY)
            .inclusiveEquipmentGroupCatalogCode(strings)
            .serviceDomains(Set.of(strings, "DSL", "TV"))
            .name(strings)
            .serialNumber(strings)
            .partNumber(strings)
            .build()
        ))
        .activationFeeVatIncluded(AmountResponseFactory.getAmountResponse(longs))
        .activationFeeVatExcluded(AmountResponseFactory.getAmountResponse(longs))
        .parentOfferId(longs)
        .parentSubscriptionId(longs)
        .portInInfo(PortInResponse.builder()
            .portInReference(strings)
            .msisdn(strings)
            .portStartDateTime(ZonedDateTime.now())
            .build()
        )
        .installationAddressId(longs)
        .activationAt(LocalDate.now())
        .subscriberBirthDate(LocalDate.now())
        .recurringAmountsByOccurrence(List.of(
            RecurringAmountByOccurrenceResponse.builder()
            .amountVatIncluded(longs)
            .amountVatExcluded(longs)
            .occurrence(1)
            .build()
        ))
        .build();
	}
}
