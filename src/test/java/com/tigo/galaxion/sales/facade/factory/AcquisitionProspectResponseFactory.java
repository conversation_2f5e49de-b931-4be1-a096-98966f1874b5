package com.tigo.galaxion.sales.facade.factory;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.PaymentSettingsResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.OfferTypeEnum;

public class AcquisitionProspectResponseFactory {
	public static AcquisitionProspectResponse getAcquisitionProspectResponse(String strings) {
		return AcquisitionProspectResponse.builder()
            .reference(strings)
            .status("ACTIVE")
            .channelCode(strings)
            .channelGroup(strings)
            .brand(strings)
            .offerType(OfferTypeEnum.POSTPAY)
            .customerType(strings)
            .cartUuid(strings)
            .creditScore(strings)
            .contractFileId(1L)
            .contractFileName(strings)
            .csrAgentEmail(strings)
            .paymentSettings(PaymentSettingsResponse.builder()
                    .payerReference(strings)
                    .build())
            .isBlacklisted(true)
            .contractSignatureOption(strings)
            .build();
	}
}
