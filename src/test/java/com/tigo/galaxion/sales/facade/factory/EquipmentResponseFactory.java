package com.tigo.galaxion.sales.facade.factory;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.ChargeResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.EquipmentFinancingResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.EquipmentResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.PricePlanResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.SubsidyResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.EquipmentTypeEnum;

public class EquipmentResponseFactory {

	public static EquipmentResponse getEquipmentResponse(Long longs, String strings) {
		return EquipmentResponse.builder()
            .id(longs)
            .catalogCode(strings)
            .imei(strings)
            .amountVatIncluded(AmountResponseFactory.getAmountResponse(longs))
            .amountVatExcluded(AmountResponseFactory.getAmountResponse(longs))
            .color(strings)
            .colorCode(strings)
            .description(strings)
            .manufacturer(strings)
            .model(strings)
            .inventoryCode(strings)
            .charge(ChargeResponse.builder()
                .catalogCode(strings)
                .pricePlan(PricePlanResponse.builder()
                    .catalogCode(strings)
                    .amountVatIncluded(longs)
                    .amountVatExcluded(longs)
                    .build()
                )
                .build()
            )
            .addon(AddOnResponseFactory.getAddOnResponse(longs, strings))
            .type(EquipmentTypeEnum.RESIDENTIAL_GATEWAY)
            .subsidy(SubsidyResponse.builder()
                .amountVatIncluded(longs)
                .amountVatExcluded(longs)
                .build()
            )
            .subsidyApplied(true)
            .equipmentFinancing(EquipmentFinancingResponse.builder()
                .id(longs)
                .code(strings)
                .occurrence(longs)
                .amountUpFrontChosen(longs)
                .build()
            ).build();
	}
}
