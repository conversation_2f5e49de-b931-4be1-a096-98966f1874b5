package com.tigo.galaxion.sales.facade;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.contact.ContactClient;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.mock.mockito.MockBean;

@SpringBootApplication
public class TigoSalesFacadeApplicationTest {

    @MockBean
    private AcquisitionProspectClient acquisitionProspectClient;

    @MockBean
    private ContactClient contactClient;

    public static void main(final String[] args) {
        SpringApplication.run(TigoSalesFacadeApplicationTest.class, args);
    }
}
