package com.tigo.galaxion.sales.facade.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.fge.jsonpatch.JsonPatchException;
import com.github.fge.jsonpatch.mergepatch.JsonMergePatch;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart.CartAddOnRequest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.cart.CartEquipmentRequest;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.mapper.dto.TigoCartResponseMapper;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CartServiceTest {

    @Mock
    private AcquisitionProspectClient acquisitionProspectClient;

    @Mock
    private CrossSellClient crossSellClient;

    @Mock
    private TigoCartResponseMapper tigoCartResponseMapper;

    @InjectMocks
    private CartService cartService;

    @Nested
    class GetCartForAcquisitionProspectsTest {

        @Test
        void acquisitionProspectsReturnCart_returnMappedResponse() {
            //GIVEN
            var prospectRef = "ref";
            var cartResponse = CartResponse.builder().build();

            when(acquisitionProspectClient.getProspectCart(prospectRef)).thenReturn(cartResponse);
            when(tigoCartResponseMapper.buildTigoCartResponse(cartResponse)).thenReturn(new TigoCartResponse());

            //WHEN
            var response = cartService.getCartForProspect(prospectRef);

            //THEN
            assertNotNull(response);
        }
    }

    @Nested
    class GetCartForCrossSellTest {

        @Test
        void crossSellReturnCart_returnMappedResponse() {
            //GIVEN
            var ref = "ref";
            var cartResponse = CartResponse.builder().build();

            when(crossSellClient.getCrossSellCart(ref)).thenReturn(cartResponse);
            when(tigoCartResponseMapper.buildTigoCartResponse(cartResponse)).thenReturn(new TigoCartResponse());

            //WHEN
            var response = cartService.getCartForCrossSell(ref);

            //THEN
            assertNotNull(response);
        }
    }

    @Nested
    class UpdateOfferInCartTest {
        @Test
        void updateOfferInCart_returnMappedResponse() {
            //GIVEN
            var prospectRef = "ref";
            var offerId = 1L;
            ObjectMapper mapper = new ObjectMapper();
            JsonMergePatch patchRequest;
            try {
                patchRequest = JsonMergePatch.fromJson(mapper.readTree("{\"directoryPreference\": \"UNLISTED\", \"topUpAmount\" : 50, \"subscriberBirthDate\": \"1951-03-25\"}"));
            } catch (JsonProcessingException | JsonPatchException e) {
                throw new RuntimeException(e);
            }
            var cartResponse = CartResponse.builder().build();

            when(acquisitionProspectClient.updateOfferCart(prospectRef, offerId, patchRequest)).thenReturn(cartResponse);
            when(tigoCartResponseMapper.buildTigoCartResponse(cartResponse)).thenReturn(new TigoCartResponse());

            //WHEN
            var response = cartService.updateOffer(prospectRef, offerId, patchRequest);

            //THEN
            assertNotNull(response);
        }
    }

    @Nested
    class AddAddOnToCartTest {

        @Test
        void addAddOnToCart_returnMappedResponse() {
            //GIVEN
            var prospectRef = "ref";
            var offerId = 1L;
            var addOn = CartAddOnRequest.builder().build();
            var cartResponse = CartResponse.builder().build();

            when(acquisitionProspectClient.addAddOnToOffer(prospectRef, offerId, addOn)).thenReturn(cartResponse);
            when(tigoCartResponseMapper.buildTigoCartResponse(cartResponse)).thenReturn(new TigoCartResponse());

            //WHEN
            var response = cartService.addAddOnToCart(prospectRef, offerId, addOn);

            //THEN
            assertNotNull(response);
        }
    }

    @Nested
    class RemoveAddOnFromCartTest {

        @Test
        void removeAddOnFromCart_returnMappedResponse() {
            //GIVEN
            var prospectRef = "ref";
            var offerId = 1L;
            var addOnId = 1L;
            var cartResponse = CartResponse.builder().build();

            when(acquisitionProspectClient.deleteAddOnFromOffer(prospectRef, offerId, addOnId)).thenReturn(cartResponse);
            when(tigoCartResponseMapper.buildTigoCartResponse(cartResponse)).thenReturn(new TigoCartResponse());

            //WHEN
            var response = cartService.deleteAddOnFromCart(prospectRef, offerId, addOnId);

            //THEN
            assertNotNull(response);
        }
    }

    @Nested
    class addEquipmentToCartTest {

        @Test
        void addEquipmentToCart_returnMappedResponse() {
            //GIVEN
            var prospectRef = "ref";
            var offerId = 1L;
            var equipment = CartEquipmentRequest.builder().build();
            var cartResponse = CartResponse.builder().build();

            when(acquisitionProspectClient.addEquipmentToOffer(prospectRef, offerId, equipment)).thenReturn(cartResponse);
            when(tigoCartResponseMapper.buildTigoCartResponse(cartResponse)).thenReturn(new TigoCartResponse());

            //WHEN
            var response = cartService.addEquipmentToCart(prospectRef, offerId, equipment);

            //THEN
            assertNotNull(response);
        }
    }

    @Nested
    class DeleteEquipmentFromCartTest {

        @Test
        void deleteEquipmentFromCart_returnMappedResponse() {
            //GIVEN
            var prospectRef = "ref";
            var offerId = 1L;
            var equipmentId = 1L;
            var cartResponse = CartResponse.builder().build();

            when(acquisitionProspectClient.deleteEquipmentFromOffer(prospectRef, offerId, equipmentId)).thenReturn(cartResponse);
            when(tigoCartResponseMapper.buildTigoCartResponse(cartResponse)).thenReturn(new TigoCartResponse());

            //WHEN
            var response = cartService.deleteEquipmentFromCart(prospectRef, offerId, equipmentId);

            //THEN
            assertNotNull(response);
        }
    }
}
