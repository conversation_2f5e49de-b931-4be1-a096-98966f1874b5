package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.FraudManagementClient;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.enumeration.IdentificationTypeEnum;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.FraudsRequestBody;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.frauds.Customer;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.response.FraudsResponseBody;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FraudManagementServiceTest {

        @Mock
        FraudManagementClient fraudClient;

        @InjectMocks
        FraudManagementService fraudService;

        @Nested
        class FraudsTest {
                @ParameterizedTest
                @CsvSource({
                                "1, 100",
                                "2, 404",
                                "3, 500",
                })
                void frauds(String id, String code) {
                        // GIVEN
                        var fraudsRequestBody = FraudsRequestBody
                                        .builder()
                                        .customer(Customer.builder()
                                                        .id(id)
                                                        .typeIdentification(IdentificationTypeEnum.CC)
                                                        .build())
                                        .transactionType("Activation")
                                        .build();

                        var fraudResponse = FraudsResponseBody.builder().code(code).build();
                        when(fraudClient.frauds(fraudsRequestBody)).thenReturn(fraudResponse);

                        // WHEN
                        var response = fraudService.frauds(fraudsRequestBody);

                        // THEN
                        var fraudArgumentCaptor = ArgumentCaptor.forClass(FraudsRequestBody.class);
                        verify(fraudClient, times(1)).frauds(fraudArgumentCaptor.capture());
                        var fraudRequests = fraudArgumentCaptor.getAllValues();

                        assertEquals(fraudsRequestBody.getTransactionType(), fraudRequests.get(0).getTransactionType());
                        assertEquals(fraudsRequestBody.getCustomer().getId(),
                                        fraudRequests.get(0).getCustomer().getId());
                        assertEquals(fraudsRequestBody.getCustomer().getTypeIdentification(),
                                        fraudRequests.get(0).getCustomer().getTypeIdentification());

                        assertNotNull(response);
                        assertEquals(code, response.getCode());
                }
        }
}
