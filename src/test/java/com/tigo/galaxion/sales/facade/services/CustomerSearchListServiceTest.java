package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.domain.problem.CustomerIdentityDocumentNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.request.CustomerSearchRequest;
import com.tigo.galaxion.sales.facade.domain.response.CustomerSearchResponse;
import com.tigo.galaxion.sales.facade.model.entity.AccountContactView;
import com.tigo.galaxion.sales.facade.model.repository.AccountContactViewRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomerSearchListServiceTest {

    @InjectMocks
    private CustomerSearchService customerSearchService;

    @Mock
    private AccountContactViewRepository customerRepo;

    @Test
    void testGetCustomerSearch_ExistingIdentityDocument() {
        // Arrange
        CustomerSearchRequest request = new CustomerSearchRequest("*********", "CC");
        AccountContactView identityDocument = new AccountContactView(
                "1",
                "2",
                "John",
                "Doe",
                "*********",
                "CC",
                new Date(),
                "**********",
                "<EMAIL>");

        when(customerRepo.findByIdentityDocumentIdentifierAndIdentityDocumentType(request.getIdentifier(),
                request.getType()))
                .thenReturn(List.of(identityDocument));

        // Act
        CustomerSearchResponse response = customerSearchService.getCustomerSearch(request);

        // Assert
        assertEquals("CC", response.getType());
        assertEquals("*********", response.getIdentifier());
        assertEquals("John", response.getFirst_name());
        assertEquals("Doe", response.getLast_name());
        assertEquals("<EMAIL>", response.getEmail());
        assertEquals("**********", response.getPhone_number());
    }

    @Test
    void testGetCustomerSearch_NonExistingIdentityDocument() {
        // Arrange
        CustomerSearchRequest request = new CustomerSearchRequest("*********", "CC");
        when(customerRepo.findByIdentityDocumentIdentifierAndIdentityDocumentType(request.getIdentifier(),
                request.getType()))
                .thenReturn(List.of());

        // Act and Assert
        assertThrows(
                CustomerIdentityDocumentNotFoundProblem.class,
                () -> customerSearchService.getCustomerSearch(request));
    }

    @Test
    void testGetCustomerSearchList_ExistingIdentityDocuments() {
        // Arrange
        CustomerSearchRequest request = new CustomerSearchRequest("*********", "CC");
        AccountContactView identityDocument1 = new AccountContactView(
                "1",
                "2",
                "John",
                "Doe",
                "*********",
                "CC",
                new Date(),
                "**********",
                "<EMAIL>");
        AccountContactView identityDocument2 = new AccountContactView(
                "2",
                "3",
                "Jane",
                "Doe",
                "*********",
                "CC",
                new Date(),
                "**********",
                "<EMAIL>");

        when(customerRepo.findByIdentityDocumentIdentifierAndIdentityDocumentType(request.getIdentifier(),
                request.getType()))
                .thenReturn(List.of(identityDocument1, identityDocument2));

        // Act
        List<CustomerSearchResponse> responseList = customerSearchService.getCustomerSearchList(request);

        // Assert
        assertEquals(2, responseList.size());
        assertEquals("CC", responseList.get(0).getType());
        assertEquals("*********", responseList.get(0).getIdentifier());
        assertEquals("John", responseList.get(0).getFirst_name());
        assertEquals("Doe", responseList.get(0).getLast_name());
        assertEquals("<EMAIL>", responseList.get(0).getEmail());
        assertEquals("**********", responseList.get(0).getPhone_number());

        assertEquals("CC", responseList.get(1).getType());
        assertEquals("*********", responseList.get(1).getIdentifier());
        assertEquals("Jane", responseList.get(1).getFirst_name());
        assertEquals("Doe", responseList.get(1).getLast_name());
        assertEquals("<EMAIL>", responseList.get(1).getEmail());
        assertEquals("**********", responseList.get(1).getPhone_number());
    }

    @Test
    void testGetCustomerSearchList_NonExistingIdentityDocuments() {
        // Arrange
        CustomerSearchRequest request = new CustomerSearchRequest("*********", "CC");
        when(customerRepo.findByIdentityDocumentIdentifierAndIdentityDocumentType(request.getIdentifier(),
                request.getType()))
                .thenReturn(List.of());

        // Act and Assert
        assertThrows(
                CustomerIdentityDocumentNotFoundProblem.class,
                () -> customerSearchService.getCustomerSearchList(request));
    }
}