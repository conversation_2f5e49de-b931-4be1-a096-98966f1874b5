package com.tigo.galaxion.sales.facade.services.order_notification;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Value;

import com.tigo.galaxion.sales.facade.connector.crm_api.workflow_engine_facade.WEFClient;
import com.tigo.galaxion.sales.facade.connector.workflow_query.WorkflowQueryClient;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.changeStatusFieldService.changeStatusFsRequest;


class FieldServiceChangeStatusServiceTest {

    @InjectMocks
    private FieldServiceChangeStatusService fieldServiceChangeStatusService;

    @Mock
    private WorkflowQueryClient workflowQueryClient;

    @Mock
    private WEFClient wefClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

   /* @Test
    void testChangeStatus_ExitTransaction(){
        
        //Arrange
        String orderId="200024";
        changeStatusFsRequest request = new changeStatusFsRequest();
        request.setTransactionId("8e515728cade4bfaad1a3dd17f9df8dd");
        request.setNewStatus("CANCELLED");

        Order order = new Order("RUNNING");
        BasicOrderInformacion mockOrder = new BasicOrderInformacion(order);

        when(workflowQueryClient.getStatus(orderId)).thenReturn(mockOrder);
        doNothing().when(wefClient).deleteOrder(orderId);
        
        //Act
        var val = fieldServiceChangeStatusService.changeStatus(request, orderId);

        //Assert
        assertNotNull(val);
        assertNotEquals(val, "NO");
        verify(workflowQueryClient, times(1)).getStatus(orderId);
    }*/

    @Test
    void testChangeStatus_ErrorNewStatus(){
        
        //Arrange
        String orderId="200024";
        changeStatusFsRequest request = new changeStatusFsRequest();
        request.setTransactionId("8e515728cade4bfaad1a3dd17f9df8dd");
        request.setNewStatus("CANCEL");
       
        //Act
        var val = fieldServiceChangeStatusService.changeStatus(request, orderId);

        //Assert
        assertNotNull(val);
        assertEquals(val, "NO");
    }

   /* @Test
    void testChangeStatus_ErrorStatusInvalid(){
        
        //Arrange
        String orderId="200024";
        changeStatusFsRequest request = new changeStatusFsRequest();
        request.setTransactionId("8e515728cade4bfaad1a3dd17f9df8dd");
        request.setNewStatus("CANCELLED");

        Order order = new Order("COMPLETED");
        BasicOrderInformacion mockOrder = new BasicOrderInformacion(order);

        when(workflowQueryClient.getStatus(orderId)).thenReturn(mockOrder);
        //doNothing().when(wefClient).deleteOrder(orderId);
        
        //Act
        var val = fieldServiceChangeStatusService.changeStatus(request, orderId);

        //Assert
        if(val.contains("/")) val="/";
        assertNotNull(val);
        assertEquals(val, "/");
        verify(workflowQueryClient, times(1)).getStatus(orderId);
    }*/
    
}
