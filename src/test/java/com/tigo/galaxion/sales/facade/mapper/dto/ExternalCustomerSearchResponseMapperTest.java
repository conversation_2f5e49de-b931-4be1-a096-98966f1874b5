package com.tigo.galaxion.sales.facade.mapper.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.ArrayList;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.Address;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.AgeRange;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.BasicInformation;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.Emails;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomer;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomerFullDetail;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.MobilePhones;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.Phones;

@ExtendWith(MockitoExtension.class)
public class ExternalCustomerSearchResponseMapperTest {

  @Test
  void validExternalCustomer() throws Exception {
    BasicInformation basicInformation = new BasicInformation(
        "economicActivivty",
        "CIIU",
        "documentStatus",
        "2024-01-01T00:00:00.000+00:00",
        "M",
        "1",
        "CO",
        "hola nuevo mundo !",
        "mundo",
        "!",
        "hola",
        "nuevo",
        "12345",
        "CC",
        new AgeRange("18", "99"));

    Address address = new Address(
        "12345",
        "asdfghjklñ",
        "1",
        "MiDC",
        "algo",
        "12",
        "algo2",
        "345",
        "8",
        "1",
        "20",
        "2024-01-02",
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        "null",
        "2024-01-31");
    ArrayList<Address> listOfAddresses = new ArrayList<Address>();
    listOfAddresses.add(address);

    Phones phones = new Phones(
        "CAS",
        "12345678",
        "algo",
        "11",
        "algo2",
        "001",
        "1",
        "1",
        "1",
        "2024-01-01",
        "1138221",
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        "null",
        "2024-01-31",
        "502");

    MobilePhones cellPhones = new MobilePhones(
        "3127124026",
        "RC",
        "1",
        "1",
        "1",
        "2024-01-01",
        "2024-01-01",
        "COL");

    Emails emails = new Emails(
        "<EMAIL>",
        "RC",
        "1",
        "1",
        "1",
        "free",
        "2024-01-01",
        "2024-01-01");

    ExternalCustomerFullDetail externalCustomerFullDetail = new ExternalCustomerFullDetail(
        basicInformation,
        listOfAddresses,
        phones,
        cellPhones,
        emails);

    ExternalCustomer externalCustomer = ExternalCustomerSearchResponseMapper
        .buildExternalCustomer(externalCustomerFullDetail);

    assertEquals(externalCustomerFullDetail.basicInformation().firstName().concat(" ")
        .concat(externalCustomerFullDetail.basicInformation().secondName()), externalCustomer.nombre());
    assertEquals(externalCustomerFullDetail.basicInformation().firstLastName().concat(" ")
        .concat(externalCustomerFullDetail.basicInformation().secondLastName()), externalCustomer.apellidos());
    assertEquals(externalCustomerFullDetail.addresses().get(0).data(), externalCustomer.direccion());
    assertEquals(externalCustomerFullDetail.addresses().get(0).daneCode().substring(0, 2),
        externalCustomer.departamento());
    assertEquals(externalCustomerFullDetail.addresses().get(0).daneCode().substring(2, 5),
        externalCustomer.municipio());
    assertEquals(externalCustomerFullDetail.emails().email(), externalCustomer.email());
    assertEquals(externalCustomerFullDetail.cellPhones().mobileNumber(), externalCustomer.mobileNumber());
    assertEquals(externalCustomerFullDetail.basicInformation().expeditionDate(), externalCustomer.expeditionDate());

  }
}
