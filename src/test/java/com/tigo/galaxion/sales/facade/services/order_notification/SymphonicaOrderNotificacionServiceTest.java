package com.tigo.galaxion.sales.facade.services.order_notification;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.util.List;

import com.tigo.galaxion.sales.facade.TigoSalesFacadeApplicationTest;
import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.symphonica.SymphonicaRequest;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.symphonica.SymphonicaRequest.Payload;
import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(classes = TigoSalesFacadeApplicationTest.class)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "wfe.signal.SP_DEVICE_REGISTRATION_BB_SYMPHONICA=someSignalName",
    "symphonica.statuses=CANCELLED,REJECTED,PARTIAL,FAILED"
})
class SymphonicaOrderNotificacionServiceTest {

    @InjectMocks
    private SymphonicaOrderNotificacionService symphonicaOrderNotificacionService;

    @Mock
    private WorkflowEngineClient workflowEngineClient;

    @Value("${wfe.signal.SP_DEVICE_REGISTRATION_BB_SYMPHONICA}")
    private String signalName = "DEVICE_REGISTRATION_SIGNAL";

    @Value("${symphonica.statuses}")
    private List<String> statusList;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testProcessNotification_StateCompleted() {
        // Arrange
        Payload payload = new Payload();
        payload.setState("COMPLETED");
        payload.setExternalId("12345");

        SymphonicaRequest request = new SymphonicaRequest();
        request.setPayload(payload);

        // Act
        symphonicaOrderNotificacionService.processNotification(request);

        // Assert
        //verify(workflowEngineClient).processSignal(any(WorkflowSignalRequestDTO.class));
    }

    @Test
    void testProcessNotification_StateNotCompleted() {
        // Arrange
        Payload payload = new Payload();
        payload.setState("PENDING");
        payload.setExternalId("12345");

        SymphonicaRequest request = new SymphonicaRequest();
        request.setPayload(payload);

        // Act
        symphonicaOrderNotificacionService.processNotification(request);

        // Assert
        // No interaction with workflowEngineClient when the state is not COMPLETED
        verify(workflowEngineClient, times(0)).processSignal(any(WorkflowSignalRequestDTO.class));
    }
}
