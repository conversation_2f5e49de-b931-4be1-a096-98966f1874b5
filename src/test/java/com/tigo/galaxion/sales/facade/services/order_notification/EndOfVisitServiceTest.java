package com.tigo.galaxion.sales.facade.services.order_notification;

import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointment;
import mc.monacotelecom.workflow.app.dto.VariableUpdateDTO;
import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;
import mc.monacotelecom.workflow.base.enumeration.ProcessType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

public class EndOfVisitServiceTest {
    @InjectMocks
    private EndOfVisitService endOfVisitService;

    @Mock
    private WorkflowEngineClient workflowEngineClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSendNotification_success() throws Exception {
        // Arrange
        String callId = "call123";
        String serviceId = "service456";

        FieldServiceConfirmAppointment request = new FieldServiceConfirmAppointment();
        request.setCallId(callId);
        request.setCustomer("SFD");

        ArgumentCaptor<WorkflowSignalRequestDTO> signalCaptor = ArgumentCaptor.forClass(WorkflowSignalRequestDTO.class);
        ArgumentCaptor<VariableUpdateDTO> variableCaptor = ArgumentCaptor.forClass(VariableUpdateDTO.class);


        String trackingNumber = endOfVisitService.sendNotification(callId, serviceId, request);

        assertNotNull(trackingNumber);


        verify(workflowEngineClient, times(1)).updateConnectorData(eq(callId), variableCaptor.capture());
        VariableUpdateDTO capturedVariable = variableCaptor.getValue();
        assertEquals(ProcessType.COM, capturedVariable.getType());
        assertEquals("fieldservice", capturedVariable.getVariable());
        assertEquals(request, capturedVariable.getData());

        verify(workflowEngineClient, times(1)).processSignal(signalCaptor.capture());
        WorkflowSignalRequestDTO capturedSignal = signalCaptor.getValue();
        assertEquals(serviceId, capturedSignal.getOrderId());
        assertEquals("SFD", capturedSignal.getContext().get("customer"));
        assertEquals(callId, capturedSignal.getContext().get("callId"));
    }
}
