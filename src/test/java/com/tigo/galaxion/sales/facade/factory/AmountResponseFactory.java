package com.tigo.galaxion.sales.facade.factory;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AmountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.RecurringAmountResponse;


public class AmountResponseFactory {

	public static AmountResponse getAmountResponse(Long longs) {
		return AmountResponse.builder()
                .oneOffAmount(longs)
                .discountedOneOffAmount(longs)
                .upFrontAmount(longs)
                .discountedUpFrontAmount(longs)
                .recurringAmount(RecurringAmountResponse.builder()
                    .amount(longs)
                    .discountedAmount(longs)
                    .discountFrequency("MONTHLY")
                    .discountOccurrence(1)
                    .build()
                )
                .build();
	}
}
