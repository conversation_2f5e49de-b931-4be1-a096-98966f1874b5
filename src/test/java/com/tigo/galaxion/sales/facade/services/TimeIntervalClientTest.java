package com.tigo.galaxion.sales.facade.services;

import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;

import java.util.Date;
import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.XMLGregorianCalendar;

import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ws.client.core.WebServiceTemplate;

import com.tigo.galaxion.sales.facade.domain.request.FieldServiceSlotsRequest;
import com.tigo.galaxion.sales.facade.services.field_service.time_interval.TimeIntervalClient;
import com.tigo.galaxion.sales.facade.soap.time_interval.ExtendedGetAppointment;
import com.tigo.galaxion.sales.facade.soap.time_interval.Task;
import com.tigo.galaxion.sales.facade.soap.time_interval.TimeIntervalRequest;
import com.tigo.galaxion.sales.facade.soap.time_interval.TimeIntervalResponse;

@ExtendWith(MockitoExtension.class)
public class TimeIntervalClientTest {

    @Mock
    private WebServiceTemplate webServiceTemplate;

    @InjectMocks
    private TimeIntervalClient timeIntervalClient;

    @Nested
    class TimeIntervalTest {
        @Test
        void testGetTimeIntervals() throws Exception {
            // Configurar datos de prueba
            FieldServiceSlotsRequest data = new FieldServiceSlotsRequest();
            data.setTaskTypeCategory("category");
            data.setTaskType("type");
            data.setStartDate("2026-01-21T08:00:00+00:00");
            data.setEndDate("2026-01-21T21:00:00+00:00");
            data.setAddress("123 Main St");
            data.setLatitude("40.7128");
            data.setLongitude("-74.0060");

            TimeIntervalResponse mockResponse = new TimeIntervalResponse();
            when(webServiceTemplate.marshalSendAndReceive(any(TimeIntervalRequest.class))).thenReturn(mockResponse);

            TimeIntervalResponse response = timeIntervalClient.getTimeIntervals(data);

            assertNotNull(response);
        }
    }

}
