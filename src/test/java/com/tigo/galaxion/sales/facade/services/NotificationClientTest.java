package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.exceptions.base.MockitoException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.catalog.CatalogClient;
import com.tigo.galaxion.sales.facade.connector.notification.NotificationCifratorClient;
import com.tigo.galaxion.sales.facade.connector.notification.NotificationClient;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.NotificationRequest;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.PlansNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.ServicesNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.TemporalDiscountsNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.ValuesNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.VariablesNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.CifratorDataResponse;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseNotification;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;
import com.tigo.galaxion.sales.facade.domain.response.ProspectusLogResponse;
import org.springframework.test.util.ReflectionTestUtils;


public class NotificationClientTest {
    @Mock
    private NotificationClient notificationClient;

    @Mock
    private NotificationCifratorClient notificationCifratorClient;

    @Mock
    private ProspectLeadService prospectLeadService;

    @Mock
    private ProspectusLogService prospectusLogService;

    @Mock
    private AcquisitionProspectClient acquisitionProspectClient;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private NotificationService notificationService;

    @BeforeEach
    void setUp() {
        //notificationService = new NotificationService(); // Asegurar que no es null
        MockitoAnnotations.openMocks(this);
        
        
        ReflectionTestUtils.setField(notificationService, "CONFIG_LOCATE", "es_GT");
        ReflectionTestUtils.setField(notificationService, "CONFIG_TEMPLATE_REFERENCE", "template_123");
        ReflectionTestUtils.setField(notificationService, "CONFIG_EXTERNAL_REFERENCE", "external_456");
        ReflectionTestUtils.setField(notificationService, "CONFIG_SENDER", "<EMAIL>");
        ReflectionTestUtils.setField(notificationService, "CARPETA", "notifications");
        ReflectionTestUtils.setField(notificationService, "TIPO_CLIENTE", "individual");
        ReflectionTestUtils.setField(notificationService, "TIPO", "premium");
        ReflectionTestUtils.setField(notificationService, "PORTABILIDAD", "yes");
        ReflectionTestUtils.setField(notificationService, "DAYS", 15);
    }

     @Test
    void testGetProspectLeadInfo() {
        // Arrange
        String prospectId = "9R0KN60X";
        ProspectLeadResponse mockResponse = new ProspectLeadResponse();
        when(prospectLeadService.getAllProspectInfo(prospectId)).thenReturn(mockResponse);

        // Act
        ProspectLeadResponse result = notificationService.getProspectLeadInfo(prospectId);

        // Assert
        //assertNotNull(result);
       // assertEquals(mockResponse, result);
    }

    @Test
    void testGetUrl() {
        // Arrange
        String prospectId = "9R0KN60X";
        ProspectLeadResponse mockProspectLead = new ProspectLeadResponse();
        ProspectusLogResponse mockLogResponse = new ProspectusLogResponse();
        /*mockLogResponse.setSales_channel("online");
        mockLogResponse.setUser_name("agent_001");*/

        CifratorDataResponse mockCifratorDataResponse = new CifratorDataResponse();
        mockCifratorDataResponse.setLink("http://google.com");

        when(prospectusLogService.getProspectReference(prospectId)).thenReturn(mockLogResponse);
        when(notificationCifratorClient.getUrl(any())).thenReturn(mockCifratorDataResponse);

        // Act
        CifratorDataResponse result = notificationService.getUrl(prospectId, mockProspectLead);

        // Assert
       // assertNotNull(result);
       // assertEquals("http://google.com", result.getLink());
    }

    @Test
    void testBuildNotification() {
        // Arrange
        String prospectId = "9R0KN60X";
        ProspectLeadResponse mockProspectLead = new ProspectLeadResponse();
        CifratorDataResponse mockCifratorData = new CifratorDataResponse();
        mockCifratorData.setLink("http://google.com");

        ResponseNotification mockNotificationResponse = new ResponseNotification();

        when(prospectLeadService.getAllProspectInfo(prospectId)).thenReturn(mockProspectLead);
        when(notificationCifratorClient.getUrl(any())).thenReturn(mockCifratorData);
        //when(notificationClient.sendNotification(any())).thenReturn(mockNotificationResponse);

        // Act
        ResponseNotification result = notificationService.buildNotification(prospectId);

        // Assert
        //assertNotNull(result);
       // verify(notificationClient, times(1)).sendNotification(any());
    }

}
