package com.tigo.galaxion.sales.facade.factory;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Customer;

public class CustomerFactory {

	public static Customer getCustomer(String strings) {
		return Customer.builder()
        .documentType("CC")
        .documentId("1232111"+strings)
        .expeditionDate("2024-12-31T23:59:59")
        .names("hola")
        .lastName("mundo")
        .contactEmail("<EMAIL>")
        .contactPhone("111111111111")
        .habeasdata(1)
        .invoiceDelivery(strings)
        .appointmentStartDate("2024-12-31T23:59:59")
        .appointmentEndDate("2025-12-31T23:59:59")
        .rating(0)
        .contractNumber(strings)
        .callId(strings)
        .appointmentTimeSlot(strings) //TODO validar formato
        .habeasSms(true)
        .habeasEmail(true)
        .habeasTelemercado(true)
        .habeasDataAuthorization(true)
        .build();
	}
}
