package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.services.field_service.get_task.GetTaskClient;
import com.tigo.galaxion.sales.facade.services.field_service.get_task.GetTaskClientConfig;
import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;


@SpringBootTest(classes = GetTaskClientConfig.class)
@TestPropertySource(properties = "environment.url.get-task=https://glx-castlemock-pa-stg.tigo.cam/castlemock/mock/soap/project/3Gaohp/getTaskPortSoap11")
class GetTaskClientConfigTest {

    @Autowired
    private Jaxb2Marshaller marshallerGetTask;

    @Autowired
    private GetTaskClient getTaskClient;

    @Test
    void contextLoads_andBeansAreConfigured() {
        assertThat(marshallerGetTask).isNotNull();
        assertThat(getTaskClient).isNotNull();
        assertThat(getTaskClient.getWebServiceTemplate().getMarshaller()).isSameAs(marshallerGetTask);
        assertThat(getTaskClient.getDefaultUri()).isEqualTo("https://glx-castlemock-pa-stg.tigo.cam/castlemock/mock/soap/project/3Gaohp/getTaskPortSoap11");
    }
}