package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellEligibilityResponse;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellOfferTypeEligibilityResponse;
import com.tigo.galaxion.sales.facade.domain.problem.AccountInCollectionProblem;
import com.tigo.galaxion.sales.facade.domain.response.OfferTypeEligibilityResponse;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CrossSellServiceTest {

    @Mock
    CrossSellClient crossSellClient;

    @Mock
    EligibilityService eligibilityService;

    @InjectMocks
    CrossSellService crossSellService;

    @Nested
    class GetOfferTypesEligibilityTest {

        @Test
        void validRequest_getOfferTypesEligibility() {
            // GIVEN
            String accountId = "1";
            when(crossSellClient.getOfferTypesEligibility(any()))
                    .thenReturn(new ArrayList<>(List.of(CrossSellOfferTypeEligibilityResponse.builder().eligible(true).offerType("POSTPAY").build(),
                            CrossSellOfferTypeEligibilityResponse.builder().eligible(true).offerType("PREPAY").build())));

            doNothing().when(eligibilityService).checkAccountIsInCollection(any());

            // WHEN
            List<OfferTypeEligibilityResponse> eligibilities = crossSellService.getOfferTypesEligibility(accountId);

            // THEN
            verify(eligibilityService).checkAccountIsInCollection(accountId);
            assertTrue(eligibilities.stream().anyMatch(e -> "POSTPAY".equals(e.getOfferType())));
            assertTrue(eligibilities.stream().anyMatch(e -> "PREPAY".equals(e.getOfferType())));

        }

        @Test
        void validRequest_getOfferTypesEligibilityWhenCollectionProblemThenNotEligible() {
            // GIVEN
            String accountId = "1";
            when(crossSellClient.getOfferTypesEligibility(any()))
                    .thenReturn(new ArrayList<>(List.of(CrossSellOfferTypeEligibilityResponse.builder().eligible(true).offerType("POSTPAY").build())));

            doThrow(new AccountInCollectionProblem(accountId)).when(eligibilityService).checkAccountIsInCollection(any());

            // WHEN
            List<OfferTypeEligibilityResponse> eligibilities = crossSellService.getOfferTypesEligibility(accountId);

            // THEN
            verify(eligibilityService).checkAccountIsInCollection(accountId);
            assertTrue(eligibilities.stream().anyMatch(e -> "POSTPAY".equals(e.getOfferType()) && !e.isEligible()));

        }
    }

    @Nested
    class GetServiceGroupsEligibilityTest {

        @Test
        void validRequest_getServiceGroupsEligibility() {
            //GIVEN
            var accountId = "1";
            var crossSellReference = "ABC";
            var causes = Collections.singletonList("CAUSE");
            var crossSellEligibilityResponses = Collections.singletonList(CrossSellEligibilityResponse.builder().eligible(true).serviceGroup("TV").causes(causes).build());
            when(crossSellClient.getServiceGroupsEligibility(eq(accountId), any())).thenReturn(crossSellEligibilityResponses);

            //WHEN
            var serviceGroupEligibility = crossSellService.getServiceGroupsEligibility(accountId, null, crossSellReference);

            //THEN
            assertNotNull(serviceGroupEligibility);
            assertEquals("TV", serviceGroupEligibility.get(0).getServiceGroup());
            assertTrue(serviceGroupEligibility.get(0).isEligible());
            assertEquals("CAUSE", serviceGroupEligibility.get(0).getCauses().get(0));
        }
    }
}
