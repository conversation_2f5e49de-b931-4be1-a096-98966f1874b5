package com.tigo.galaxion.sales.facade.factory;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.DiscountResponse;

public class DiscountResponseFactory {
	public static DiscountResponse getDiscountResponse(Long longs, String strings){
		return DiscountResponse.builder()
			.id(longs)
			.catalogCode(strings)
			.description(strings)
			.value(longs)
			.vatExcludedAmount(longs)
			.type(strings)
			.frequency(strings)
			.occurrence(longs.intValue())
			.billingType(strings)
			.reason(strings)
			.discountItemType(strings)
			.build();
	}
}
