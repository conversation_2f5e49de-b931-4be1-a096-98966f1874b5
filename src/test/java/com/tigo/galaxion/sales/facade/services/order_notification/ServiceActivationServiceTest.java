package com.tigo.galaxion.sales.facade.services.order_notification;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.ServiceActivationRequest;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.ServiceActivationRequest.Equipment;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.ServiceActivationRequest.Equipment.Identifier;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.ServiceActivationRequest.Equipment.Identifier.IdentifierType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

@TestPropertySource(properties = {
        "wfe.signal.upfrontpayment=WAIT_PAYUPFRONT",
        "wfe.signal.SP_WAIT_BB_PROV_REQUEST=SOM_WAIT_PROVISIONING",
        "wfe.signal.SP_DEVICE_REGISTRATION_BB_SYMPHONICA=someSignalName",
        "symphonica.statuses=CANCELLED,REJECTED,PARTIAL,FAILED"
})
class ServiceActivationServiceTest {

    @InjectMocks
    private ServiceActivationService serviceActivationService;

    @Mock
    private WorkflowEngineClient workflowEngineClient;

    @Value("${wfe.signal.SP_WAIT_BB_PROV_REQUEST}")
    private String signalName = "WAIT_SIGNAL_NAME";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSendNotification() {
        // Arrange
        String orderId = "12345";
        String serviceId = "67890";
        Equipment equipment = new Equipment();
        Identifier identifier1 = new Identifier();
        identifier1.setType(IdentifierType.MAC1);
        identifier1.setId("00:1A:2B:3C:4D:5E");

        Identifier identifier2 = new Identifier();
        identifier2.setType(IdentifierType.SMART_CARD_ID);
        identifier2.setId("SMARTCARD123");

        equipment.setIdentifiers(List.of(identifier1, identifier2));
        equipment.setBrand("CISCO");

        ServiceActivationRequest request = new ServiceActivationRequest();
        request.setEquipment(equipment);
        request.setTransactionId("1");
        request.setTransactionType("activated");

        // Act
        String id;
        try {
            id = serviceActivationService.sendNotification(orderId, serviceId, request);
            // Assert
            assertNotNull(id);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
