package com.tigo.galaxion.sales.facade.factory;

import java.util.List;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.CommitmentDurationEnum;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.BaseOfferResponse;

public class BaseOfferResponseFactory {

	public static BaseOfferResponse getBaseOfferResponse(Long longs, String strings) {
		String[] serviceGroup = new String[]{"HFC", "TV",};
		return BaseOfferResponse.builder()
            .catalogCode(strings)
            .serviceGroup(serviceGroup[longs.intValue()-1])
            .serviceDomains(List.of("DSL", "TV"))
            .description(strings)
            .comment(strings)
            .catalogTariffPlanCode(strings)
            .simOnly(false)
            .tariffPlanDescription(strings)
            .commitmentDuration(CommitmentDurationEnum.ONE)
            .amountVatIncluded(AmountResponseFactory.getAmountResponse(longs))
            .amountVatExcluded(AmountResponseFactory.getAmountResponse(longs))
            .displayOrder(longs)
            .broadbandTechnology("DSL")
            .build();
	}
}
