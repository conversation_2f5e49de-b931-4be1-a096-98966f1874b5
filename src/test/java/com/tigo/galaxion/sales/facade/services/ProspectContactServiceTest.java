package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.address.AddressClient;
import com.tigo.galaxion.sales.facade.connector.address.domain.request.AddressRequest;
import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import com.tigo.galaxion.sales.facade.domain.request.AddOfferAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoProspectContactRequest;
import com.tigo.galaxion.sales.facade.model.entity.ContactAddressEntity;
import com.tigo.galaxion.sales.facade.model.entity.ContactAddressId;
import com.tigo.galaxion.sales.facade.model.entity.ContactIdentityDocumentEntity;
import com.tigo.galaxion.sales.facade.model.repository.ContactAddressRepository;
import com.tigo.galaxion.sales.facade.services.retrieval.ContactIdentityDocumentRetrievalService;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProspectContactServiceTest {

        @Mock
        private AcquisitionProspectClient acquisitionProspectClient;

        @Mock
        private AddressClient addressClient;

        @Mock
        private ContactAddressRepository contactAddressRepository;

        @Mock
        private ContactIdentityDocumentRetrievalService contactIdentityDocumentRetrievalService;

        @InjectMocks
        private ProspectContactService prospectContactService;

        @Nested
        class UpdateContactTest {

                @Test
                void createContact_createBillingAndDeliveryAddress() {
                        // GIVEN
                        var prospectRef = "ref";
                        var tigoBillingAddress = TigoAddressRequest.builder().streetName("street billing").build();
                        var tigoDeliveryAddress = TigoAddressRequest.builder().streetName("street delivery").build();
                        var tigoContactRequest = TigoProspectContactRequest.builder()
                                        .billingAddress(tigoBillingAddress)
                                        .deliveryAddress(tigoDeliveryAddress)
                                        .build();

                        when(contactAddressRepository.findAllByContactAddressId_Reference(prospectRef))
                                        .thenReturn(List.of());
                        when(contactIdentityDocumentRetrievalService.getContactIdentityDocumentEntity(prospectRef))
                                        .thenReturn(ContactIdentityDocumentEntity.builder().build());

                        // WHEN
                        prospectContactService.updateContact(prospectRef, tigoContactRequest);

                        // THEN
                        var addressArgumentCaptor = ArgumentCaptor.forClass(AddressRequest.class);
                        verify(addressClient, times(2)).createAddress(addressArgumentCaptor.capture());
                        var addressRequests = addressArgumentCaptor.getAllValues();
                        assertEquals(tigoDeliveryAddress.getStreetName(), addressRequests.get(0).getStreet());
                        assertEquals(tigoBillingAddress.getStreetName(), addressRequests.get(1).getStreet());

                        verify(contactAddressRepository, times(2)).save(any());
                        verify(acquisitionProspectClient).createOrUpdateContact(prospectRef, tigoContactRequest);
                }

                @Test
                void updateContact_updateBillingAndDeliveryAddress() {
                        // GIVEN
                        var prospectRef = "ref";
                        var tigoBillingAddress = TigoAddressRequest.builder().streetName("street billing").build();
                        var tigoDeliveryAddress = TigoAddressRequest.builder().streetName("street delivery").build();
                        var tigoContactRequest = TigoProspectContactRequest.builder()
                                        .billingAddress(tigoBillingAddress)
                                        .deliveryAddress(tigoDeliveryAddress)
                                        .build();

                        var billingAddressEntity = ContactAddressEntity.builder().addressId(20L)
                                        .contactAddressId(ContactAddressId.builder().type(AddressTypeEnum.BILLING)
                                                        .build())
                                        .build();
                        var deliveryAddressEntity = ContactAddressEntity.builder().addressId(50L)
                                        .contactAddressId(ContactAddressId.builder().type(AddressTypeEnum.DELIVERY)
                                                        .build())
                                        .build();
                        when(contactAddressRepository.findAllByContactAddressId_Reference(prospectRef))
                                        .thenReturn(List.of(billingAddressEntity, deliveryAddressEntity));
                        when(contactIdentityDocumentRetrievalService.getContactIdentityDocumentEntity(prospectRef))
                                        .thenReturn(ContactIdentityDocumentEntity.builder().build());

                        // WHEN
                        prospectContactService.updateContact(prospectRef, tigoContactRequest);

                        // THEN
                        var addressIdArgumentCaptor = ArgumentCaptor.forClass(Long.class);
                        var addressArgumentCaptor = ArgumentCaptor.forClass(AddressRequest.class);
                        verify(addressClient, times(2)).updateAddress(addressIdArgumentCaptor.capture(),
                                        addressArgumentCaptor.capture());
                        var addressIds = addressIdArgumentCaptor.getAllValues();
                        var addressRequests = addressArgumentCaptor.getAllValues();
                        assertEquals(deliveryAddressEntity.getAddressId(), addressIds.get(0));
                        assertEquals(tigoDeliveryAddress.getStreetName(), addressRequests.get(0).getStreet());
                        assertEquals(billingAddressEntity.getAddressId(), addressIds.get(1));
                        assertEquals(tigoBillingAddress.getStreetName(), addressRequests.get(1).getStreet());

                        verify(addressClient, times(0)).createAddress(any());
                        verify(contactAddressRepository, times(0)).save(any());
                        verify(acquisitionProspectClient).createOrUpdateContact(prospectRef, tigoContactRequest);
                }
        }

        @Nested
        class createInstallationAddressTest {

                @Test
                void createAddress_GetAddress() {
                        // GIVEN
                        String prospectRef = "ref1";
                        var installationAddressEntity = ContactAddressEntity
                                        .builder()
                                        .addressId(20L)
                                        .contactAddressId(
                                                        ContactAddressId
                                                                        .builder()
                                                                        .type(AddressTypeEnum.INSTALLATION)
                                                                        .build())
                                        .build();

                        when(contactAddressRepository
                                        .findByContactAddressId_ReferenceAndContactAddressId_Type(prospectRef,
                                                        AddressTypeEnum.INSTALLATION))
                                        .thenReturn(Optional.of(installationAddressEntity));

                        var addressResponse = AddressResponse.builder().id(installationAddressEntity.getAddressId())
                                        .build();
                        when(addressClient.getAddress(installationAddressEntity.getAddressId()))
                                        .thenReturn(addressResponse);

                        // WHEN
                        var response = prospectContactService.getInstallationAddress(prospectRef);

                        // THEN
                        assertNotNull(response);
                }

                @Test
                void createAddress_GetAddress404() {
                        // GIVEN
                        String prospectRef = "ref1";

                        when(contactAddressRepository
                                        .findByContactAddressId_ReferenceAndContactAddressId_Type(prospectRef,
                                                        AddressTypeEnum.INSTALLATION))
                                        .thenReturn(Optional.empty());

                        // WHEN
                        var response = prospectContactService.getInstallationAddress(prospectRef);

                        // THEN
                        assertNull(response);
                }

                @Test
                void createAddress_NewAddress() {
                        // GIVEN
                        var prospectRef = "ref";
                        var tigoInstallationAddress = AddOfferAddressRequest.builder()
                                        .addressCode(1)
                                        .countryCode(57)
                                        .departmentCode(1)
                                        .latitude(14.0)
                                        .longitude(-90.10)
                                        .municipalityCode(2)
                                        .normalizedAddress("Calle 2 avenida 1")
                                        .stratum(5)
                                        .build();

                        // WHEN
                        prospectContactService.createInstallationAddress(prospectRef, tigoInstallationAddress);

                        // THEN
                        var addressArgumentCaptor = ArgumentCaptor.forClass(AddressRequest.class);
                        verify(addressClient, times(1)).createAddress(addressArgumentCaptor.capture());
                        var addressRequests = addressArgumentCaptor.getAllValues();
                        assertEquals(tigoInstallationAddress.getCountryCode().toString(),
                                        addressRequests.get(0).getCounty());
                        assertEquals(tigoInstallationAddress.getDepartmentCode().toString(),
                                        addressRequests.get(0).getTown());
                        assertEquals(tigoInstallationAddress.getMunicipalityCode().toString(),
                                        addressRequests.get(0).getArea());
                        assertEquals(tigoInstallationAddress.getNormalizedAddress(),
                                        addressRequests.get(0).getStreet());
                        assertEquals(tigoInstallationAddress.getLatitude().toString(),
                                        addressRequests.get(0).getAddressLine1());
                        assertEquals(tigoInstallationAddress.getLongitude().toString(),
                                        addressRequests.get(0).getAddressLine2());
                        assertEquals(tigoInstallationAddress.getStratum().toString(),
                                        addressRequests.get(0).getAddressLine3());
                        assertEquals(tigoInstallationAddress.getAddressCode().toString(),
                                        addressRequests.get(0).getCode());

                        verify(contactAddressRepository, times(1)).save(any());
                }

                @Test
                void createAddress_ExistingAddress() {
                        // GIVEN
                        var prospectRef = "ref";
                        var tigoInstallationAddress = AddOfferAddressRequest.builder()
                                        .addressCode(1)
                                        .countryCode(57)
                                        .departmentCode(1)
                                        .latitude(14.0)
                                        .longitude(-90.10)
                                        .municipalityCode(2)
                                        .normalizedAddress("Calle 2 avenida 1")
                                        .stratum(5)
                                        .build();

                        var installationAddressEntity = ContactAddressEntity.builder().addressId(20L)
                                        .contactAddressId(ContactAddressId.builder().type(AddressTypeEnum.INSTALLATION)
                                                        .build())
                                        .build();
                        when(contactAddressRepository
                                        .findByContactAddressId_ReferenceAndContactAddressId_Type(prospectRef,
                                                        AddressTypeEnum.INSTALLATION))
                                        .thenReturn(Optional.of(installationAddressEntity));

                        // WHEN
                        var response = prospectContactService.createInstallationAddress(prospectRef,
                                        tigoInstallationAddress);

                        // THEN
                        var addressArgumentCaptor = ArgumentCaptor.forClass(AddressRequest.class);
                        var refArgumentCaptor = ArgumentCaptor.forClass(Long.class);
                        verify(addressClient, times(1)).updateAddress(refArgumentCaptor.capture(),
                                        addressArgumentCaptor.capture());
                        var addressRequests = addressArgumentCaptor.getAllValues();
                        assertEquals(tigoInstallationAddress.getCountryCode().toString(),
                                        addressRequests.get(0).getCounty());
                        assertEquals(tigoInstallationAddress.getDepartmentCode().toString(),
                                        addressRequests.get(0).getTown());
                        assertEquals(tigoInstallationAddress.getMunicipalityCode().toString(),
                                        addressRequests.get(0).getArea());
                        assertEquals(tigoInstallationAddress.getNormalizedAddress(),
                                        addressRequests.get(0).getStreet());
                        assertEquals(tigoInstallationAddress.getLatitude().toString(),
                                        addressRequests.get(0).getAddressLine1());
                        assertEquals(tigoInstallationAddress.getLongitude().toString(),
                                        addressRequests.get(0).getAddressLine2());
                        assertEquals(tigoInstallationAddress.getStratum().toString(),
                                        addressRequests.get(0).getAddressLine3());
                        assertEquals(tigoInstallationAddress.getAddressCode().toString(),
                                        addressRequests.get(0).getCode());

                        assertNotNull(response);
                        assertEquals(installationAddressEntity.getAddressId(), response.getAddressId());
                }

        }

}
