package com.tigo.galaxion.sales.facade.services;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;

import java.io.IOException;

import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.tigo.galaxion.sales.facade.connector.prospectlead.ProspectLeadClient;

@ExtendWith(MockitoExtension.class)
public class ProspectLeadServiceAdditionalInfoTest {

    @Mock
    ProspectLeadClient prospectLeadClient;

    @InjectMocks
    ProspectLeadService prospectLeadService;

    @Nested
    class ProspectLeadTest {
        @Test
        void PatchAdditionalContactTest() throws JsonParseException, IOException {
            // GIVEN

            String jsonString = "{\"referenceType\":\"AUT\",\"documentType\":\"CC\",\"documentId\":\"111111\",\"names\":\"Jane2\",\"lastName\":\"Smith2\",\"phone\":\"5555678223\",\"email\":\"<EMAIL>\",\"referentType\":\"AUT\"}";

            ObjectMapper mapper = new ObjectMapper();
            JsonNode actualObj = mapper.readTree(jsonString);

            var request = actualObj;

            String prospectReference = "E0G4QGOJ";
            String entityType = "customerReference";

            // Configurar el comportamiento del método void
            doNothing().when(prospectLeadClient).updateProspectEntyByEntityType(prospectReference, entityType, request);

            // Llamar al método que estás probando
            prospectLeadService.updateProspectInfoByEntity(prospectReference, entityType, request);

            // Verificar que el método fue llamado con los argumentos correctos
            verify(prospectLeadClient).updateProspectEntyByEntityType(prospectReference, entityType, request);
        }
    }
}
