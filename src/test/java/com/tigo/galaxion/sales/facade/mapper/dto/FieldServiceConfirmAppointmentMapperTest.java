package com.tigo.galaxion.sales.facade.mapper.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.Test;

import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointment;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.ProcessTaskExRequest;
import com.tigo.galaxion.sales.facade.soap.process_task_ex.TaskRequest;
import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.XMLGregorianCalendar;

import java.text.ParseException;

public class FieldServiceConfirmAppointmentMapperTest {

    @Test
    public void testBuildProcessTaskExRequest() throws DatatypeConfigurationException, ParseException {
        // Arrange
        FieldServiceConfirmAppointment request = new FieldServiceConfirmAppointment();
        request.setCallId("1234");
        request.setNumber(1);
        request.setPriority(2);
        request.setDuration(3);
        request.setEarlyStart("2024-11-02T23:59:59.000Z");
        request.setLateStart("2024-11-02T23:59:59.000Z");
        request.setDueDate("2024-11-02T23:59:59.000Z");
        request.setOpenDate("2024-11-02T23:59:59.000Z");
        request.setArea("Area1");
        request.setRegion("Region1");
        request.setDistrict("District1");
        request.setStreet("Street1");
        request.setCity("City1");
        request.setMcState("State1");
        request.setCountryId("Country1");
        request.setTdRequired(true);
        request.setRequiredCrewSize(5);
        request.setNumberOfRequiredEngineers(10);
        request.setTaskTypeCategory("Category1");
        request.setTaskType("Type1");
        request.setMcComment("Comment1");
        request.setMcCrmComment("CRM Comment1");
        request.setMcContactEmail("<EMAIL>");
        request.setMcCustomerCode("CustCode1");
        request.setMcCustomerPhoneNumber(**********L);
        request.setMcStatusFCVToken("Token1");
        request.setCustomer("Customer1");
        request.setContactPhoneNumber(*********L);
        request.setMcWorkPackageDescription("WorkPackage1");
        request.setMcConnectionData("ConnectionData1");
        request.setMcBillingAccountInfo("BillingInfo1");
        request.setAppointmentStart("2024-11-02T23:59:59.000Z");
        request.setAppointmentFinish("2024-11-02T23:59:59.000Z");
        request.setLatitude("10.12345");
        request.setLongitude("20.12345");

        // Act
        ProcessTaskExRequest processTaskExRequest = FieldServiceConfirmAppointmentMapper.buildProcessTaskExRequest(request);

        // Assert
        assertNotNull(processTaskExRequest);
        TaskRequest taskRequest = processTaskExRequest.getProcessTaskEx().getTask();
        assertNotNull(taskRequest);

        // Check each field
        assertEquals("1234", taskRequest.getCallID());
        assertEquals(2, taskRequest.getPriority());
        assertEquals("Area1", taskRequest.getArea());
        assertEquals("Region1", taskRequest.getRegion().getName());
        assertEquals("District1", taskRequest.getDistrict());
        assertEquals("Street1", taskRequest.getStreet());
        assertEquals("City1", taskRequest.getCity());
        assertEquals("State1", taskRequest.getMCState());
        assertEquals("Country1", taskRequest.getCountryID());
        assertEquals(true, taskRequest.isTDRequired());
        assertEquals(5, taskRequest.getRequiredCrewSize());
        assertEquals(10, taskRequest.getNumberOfRequiredEngineers());
        assertEquals("Category1", taskRequest.getTaskTypeCategory());
        assertEquals("Type1", taskRequest.getTaskType());
        assertEquals("Comment1", taskRequest.getMCComment());
        assertEquals("CRM Comment1", taskRequest.getMCCRMComment());
        assertEquals("<EMAIL>", taskRequest.getMCContactEmail());
        assertEquals("CustCode1", taskRequest.getMCCustomerCode());
        assertEquals(**********, taskRequest.getMCCustomerPhoneNumber());
        assertEquals("Token1", taskRequest.getMCStatusFCVToken());
        assertEquals("Customer1", taskRequest.getCustomer());
        assertEquals(*********, taskRequest.getContactPhoneNumber());
        assertEquals("WorkPackage1", taskRequest.getMCWorkPackageDescription());
        assertEquals("ConnectionData1", taskRequest.getMCConnectionData());
        assertEquals("BillingInfo1", taskRequest.getMCBillingAccountInfo());
        assertEquals("10.12345", taskRequest.getLatitude());
        assertEquals("20.12345", taskRequest.getLongitude());
    }

    @Test
    public void testToXMLGregorianCalendar() throws DatatypeConfigurationException, ParseException {
        // Given
        String dateString = "2024-11-02T23:59:59.000Z";

        // When
        XMLGregorianCalendar xmlGregorianCalendar = FieldServiceConfirmAppointmentMapper.toXMLGregorianCalendar(dateString);

        // Then
        assertNotNull(xmlGregorianCalendar);
        assertEquals(2024, xmlGregorianCalendar.getYear());
        assertEquals(11, xmlGregorianCalendar.getMonth());
        assertEquals(2, xmlGregorianCalendar.getDay());
    }

    @Test
    public void testToXMLGregorianCalendarInvalidDate() {
        // Given
        String dateString = "invalid-date";

        // When & Then
        try {
            FieldServiceConfirmAppointmentMapper.toXMLGregorianCalendar(dateString);
        } catch (Exception e) {
            assertNotNull(e);
            assertEquals(ParseException.class, e.getClass());
        }
    }
}