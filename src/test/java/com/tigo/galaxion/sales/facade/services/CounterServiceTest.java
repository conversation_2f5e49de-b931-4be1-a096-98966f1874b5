package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.tigo.galaxion.sales.facade.model.entity.CounterEntity;
import com.tigo.galaxion.sales.facade.model.repository.CounterRepository;

@ExtendWith(MockitoExtension.class)
public class CounterServiceTest {

    @Mock
    private CounterRepository counterRepository;

    @InjectMocks
    private CounterService counterService;

    private CounterEntity counterEntity;

    @BeforeEach
    void setUp() {
        counterEntity = new CounterEntity();
        counterEntity.setId(1);
        counterEntity.setName("testCounter");
        counterEntity.setValue(10L);
    }

    @Test
    void incrementCounter_whenCounterExists() {
        // GIVEN
        when(counterRepository.findByName("testCounter")).thenReturn(Optional.of(counterEntity));
        doNothing().when(counterRepository).updateValueById(1, 11L);

        // WHEN
        Long result = counterService.incrementCounter("testCounter");

        // THEN
        assertEquals(10L, result);
        verify(counterRepository, times(1)).findByName("testCounter");
        verify(counterRepository, times(1)).updateValueById(1, 11L);
    }

    @Test
    void incrementCounter_whenCounterDoesNotExist() {
        // GIVEN
        when(counterRepository.findByName("testCounter")).thenReturn(Optional.empty());

        // WHEN & THEN
        assertThrows(RuntimeException.class, () -> counterService.incrementCounter("testCounter"));
        verify(counterRepository, times(1)).findByName("testCounter");
        verify(counterRepository, never()).updateValueById(anyInt(), anyLong());
    }
}
