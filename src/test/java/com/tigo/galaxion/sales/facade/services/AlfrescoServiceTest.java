package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.alfresco.AlfrescoAuthServiceProperties;
import com.tigo.galaxion.sales.facade.connector.alfresco.AlfrescoClient;
import com.tigo.galaxion.sales.facade.connector.alfresco.request.AlfrescoDocumentRequest;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoAuthResponse;
import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoDocumentResponse;
import com.tigo.galaxion.sales.facade.connector.catalog.CatalogClient;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;
import com.tigo.galaxion.sales.facade.factory.AcquisitionProspectResponseFactory;
import com.tigo.galaxion.sales.facade.factory.CartResponseFactroy;
import com.tigo.galaxion.sales.facade.factory.ProspectLeadResponseFactory;

@ExtendWith(MockitoExtension.class)
class AlfrescoServiceTest {
    
    @InjectMocks
    private AlfrescoService alfrescoService;

    @Mock
    private AlfrescoClient alfrescoClient;

    @Mock
    private CatalogClient catalogClient;
    
    @Mock
    private AlfrescoAuthServiceProperties authServiceProperties;

    @Mock
    private ProspectLeadService prospectLeadService;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Setup for getAccessToken() method
        AlfrescoAuthResponse mockAuthResponse = new AlfrescoAuthResponse();
        mockAuthResponse.setAccessToken("mockToken");
        mockAuthResponse.setExpiresIn(5000L);
        when(alfrescoClient.getToken(anyString(), any())).thenReturn(mockAuthResponse);
        when(authServiceProperties.getAuthorization()).thenReturn("authorization");
        when(authServiceProperties.getUsername()).thenReturn("username");
        when(authServiceProperties.getPassword()).thenReturn("password");
    }

    @Test
    public void testGetJsonAccessToken_whenTokenIsNull_shouldGenerateNewToken() {
        // Arrange
        when(authServiceProperties.getUsername()).thenReturn("user");
        when(authServiceProperties.getPassword()).thenReturn("password");
        when(authServiceProperties.getAuthorization()).thenReturn("authHeader");

        // Act
        AlfrescoAuthResponse response = alfrescoService.getJsonAccessToken();

        // Assert
        assertNotNull(response);
        assertEquals("mockToken", response.getAccessToken());
        verify(alfrescoClient, times(1)).getToken(anyString(), any());
    }

    @Test
    public void testGetJsonAccessToken_whenTokenIsValid_shouldReturnExistingToken() {
        // Arrange
        alfrescoService.getJsonAccessToken(); // Genera el primer token

        // Act
        AlfrescoAuthResponse response = alfrescoService.getJsonAccessToken();

        // Assert
        assertNotNull(response);
        assertEquals("mockToken", response.getAccessToken());
        verify(alfrescoClient, times(1)).getToken(anyString(), any()); // El token no debería ser generado nuevamente
    }

    @Test
    void getAccessToken() {
        AlfrescoAuthResponse mockResponse = new AlfrescoAuthResponse();
        mockResponse.setAccessToken("mockToken");

        // Configuración de los valores de retorno de los mocks
        when(authServiceProperties.getAuthorization()).thenReturn("authorization");
        when(authServiceProperties.getUsername()).thenReturn("username");
        when(authServiceProperties.getPassword()).thenReturn("password");

        // El método alfrescoClient.getToken debe ser configurado para devolver el
        // mockResponse
        when(alfrescoClient.getToken(anyString(), any())).thenReturn(mockResponse);

        // Llamada al método que estamos probando
        String token = alfrescoService.getAccessToken();

        // Verificación de los resultados
        assertEquals("mockToken", token);
    }

    @Test
    void documentFileAlfresco() {
        AlfrescoDocumentRequest request = new AlfrescoDocumentRequest();
        AlfrescoDocumentResponse mockResponse = new AlfrescoDocumentResponse();

        // Setup for documentFileAlfresco method
        when(alfrescoClient.createFile(anyString(), any(AlfrescoDocumentRequest.class))).thenReturn(mockResponse);

        // Call the method under test
        AlfrescoDocumentResponse response = alfrescoService.documentFileAlfresco(request);

        // Assert the response
        assertEquals(mockResponse, response);
    }

    @Test
    void testGetAccessToken_whenTokenIsNull_shouldGenerateNewToken() {
        // Arrange
        when(authServiceProperties.getUsername()).thenReturn("user");
        when(authServiceProperties.getPassword()).thenReturn("password");
        when(authServiceProperties.getAuthorization()).thenReturn("authHeader");

        // Act
        AlfrescoAuthResponse response = alfrescoService.getJsonAccessToken();

        // Assert
        assertNotNull(response);
        assertEquals("mockToken", response.getAccessToken());
        verify(alfrescoClient, times(1)).getToken(anyString(), any());
    }

    @Test
    void testGetAccessToken_whenTokenIsValid_shouldReturnExistingToken() {
        // Arrange
        alfrescoService.getJsonAccessToken(); // Genera el primer token

        // Act
        AlfrescoAuthResponse response = alfrescoService.getJsonAccessToken();

        // Assert
        assertNotNull(response);
        assertEquals("mockToken", response.getAccessToken());
        verify(alfrescoClient, times(1)).getToken(anyString(), any()); // El token no debería ser generado nuevamente
    }

    @Test
    void getProspectLeadInfo_whenProspectIdIsValid_shouldReturnProspectLeadInfo() {
        // Arrange
        ProspectLeadResponse prospectLeadResponse = new ProspectLeadResponseFactory().getProspectLeadResponse();
        when(prospectLeadService.getAllProspectInfo(anyString())).thenReturn(prospectLeadResponse);

        // Act
        ProspectLeadResponse response = alfrescoService.getProspectLeadInfo("prospectId");

        // Assert
        assertEquals(prospectLeadResponse.getAddress()[0].getAddress(), response.getAddress()[0].getAddress());
        assertEquals(prospectLeadResponse.getAddress()[0].getAddressType(), response.getAddress()[0].getAddressType());
        assertEquals(prospectLeadResponse.getCustomer().getDocumentId(), response.getCustomer().getDocumentId());
        assertEquals(prospectLeadResponse.getCustomer().getDocumentType(), response.getCustomer().getDocumentType());

        // Act
        AlfrescoAuthResponse response2 = alfrescoService.getJsonAccessToken();

        // Assert
        assertNotNull(response2);
        assertEquals("mockToken", response2.getAccessToken());
    }

    @Test
    void getProspectCart_whenProspectIdIsNull_shouldThrowNull(){
        // Act
        NullPointerException exception = assertThrows(NullPointerException.class, () -> alfrescoService.getProspectCart(null));
    
        // Assert
        assertEquals(NullPointerException.class, exception.getClass());
        assertTrue(exception.getMessage().contains("is null"));
        
        // Act
        AlfrescoAuthResponse response2 = alfrescoService.getJsonAccessToken();

        // Assert
        assertNotNull(response2);
        assertEquals("mockToken", response2.getAccessToken());
    }

    @Test
    void getAcquisitionProspect_whenProspectIdIsNull_shouldReturnNull(){
        // Act
        NullPointerException exception = assertThrows(NullPointerException.class, () -> alfrescoService.getAcquisitionProspect(null));
    
        // Assert
        assertEquals(NullPointerException.class, exception.getClass());
        assertTrue(exception.getMessage().contains("is null"));

        // Act
        AlfrescoAuthResponse response2 = alfrescoService.getJsonAccessToken();

        // Assert
        assertNotNull(response2);
        assertEquals("mockToken", response2.getAccessToken());
    }

    @Test
    void buildAlfrescoDocumentRequest_whenAllIsValid_shouldReturnAlfrescoDocumentRequest() {
        // Arrange
        ProspectLeadResponse prospectLeadResponse = new ProspectLeadResponseFactory().getProspectLeadResponse();
        CartResponse cartResponse = CartResponseFactroy.getCartResponse(1L, "1");
        AcquisitionProspectResponse acquisitionProspectResponse = AcquisitionProspectResponseFactory.getAcquisitionProspectResponse("1");

        ReflectionTestUtils.setField(alfrescoService, "idTramite", 1);
        ReflectionTestUtils.setField(alfrescoService, "idCanal", 1);
        ReflectionTestUtils.setField(alfrescoService, "reqFirmaRemota", "false");
        ReflectionTestUtils.setField(alfrescoService, "reqFirmaManuscrita", "true");
        ReflectionTestUtils.setField(alfrescoService, "reqFirmaElectronica", "false");
        ReflectionTestUtils.setField(alfrescoService, "urlRespuesta", "localhost:8081");

        // Act
        AlfrescoDocumentRequest response = alfrescoService.buildAlfrescoDocumentRequest(
                prospectLeadResponse, cartResponse, acquisitionProspectResponse);

        // Assert
        assertEquals(prospectLeadResponse.getCustomer().getContactEmail(), response.getCorreo());
        // Act
        AlfrescoAuthResponse response2 = alfrescoService.getJsonAccessToken();

        // Assert
        assertNotNull(response2);
        assertEquals("mockToken", response2.getAccessToken());
    }

    // I couldn't mock the acquisitionProspectClient because it's a Feign Client
    /*@Test
    void testDocumentFileAlfrescoByProspectId() {
        //when(alfrescoClient.getToken(anyString(), any(AlfrescoGenerateTokenRequest.class))).thenReturn(AlfrescoAuthResponseFactory.getAuthResponse(1, "1"));
        ProspectLeadResponse prospectLeadResponse = new ProspectLeadResponseFactory().getProspectLeadResponse();
        when(prospectLeadService.getAllProspectInfo(anyString())).thenReturn(prospectLeadResponse);
        CartResponse cartResponse = CartResponseFactroy.getCartResponse(1L, "1");
        when(acquisitionProspectClient.getProspectCart(anyString())).thenReturn(cartResponse);
        AcquisitionProspectResponse acquisitionProspectResponse = AcquisitionProspectResponseFactory.getAcquisitionProspectResponse("1");
        when(acquisitionProspectClient.getAcquisitionProspect(anyString())).thenReturn(acquisitionProspectResponse);
        
        AlfrescoDocumentResponse documentResponse = AlfrescoDocumentResponse.builder()
        .idDocumento("idDocumento")
        .estado("estado")
        .observacion("observacion")
        .build();
        
        // Mocking document response
        when(alfrescoClient.createFileByProspectId(anyString(), any())).thenReturn(documentResponse);
        
        // Call the method under test
        String prospectId = "some_prospect_id";
        AlfrescoDocumentResponse response = alfrescoService.documentFileAlfrescoByProspectId(prospectId);

        // Assertions to verify the expected behavior
        assertNotNull(response);
        assertEquals(documentResponse, response);
        verify(alfrescoClient, times(1)).getToken(anyString(), any(AlfrescoGenerateTokenRequest.class));
        verify(acquisitionProspectClient, times(1)).getProspectCart(anyString());
        verify(acquisitionProspectClient, times(1)).getAcquisitionProspect(anyString());
    }*/

    @Test
    void documentAlfrescoByProspectId_throwsNull() {
        NullPointerException exception = assertThrows(NullPointerException.class, () -> alfrescoService.documentFileAlfrescoByProspectId("mockId"));
        System.out.println("Message: " + exception.getMessage());
        assertNotNull(exception);
        verify(alfrescoClient, times(1)).getToken(anyString(), any()); // El token no debería ser generado nuevamente
        verify(prospectLeadService, times(1)).getAllProspectInfo(anyString());
    }
}
