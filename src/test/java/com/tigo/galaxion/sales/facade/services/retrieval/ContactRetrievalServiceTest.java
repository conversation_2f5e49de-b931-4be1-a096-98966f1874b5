package com.tigo.galaxion.sales.facade.services.retrieval;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.address.AddressClient;
import com.tigo.galaxion.sales.facade.connector.address.domain.response.AddressResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import com.tigo.galaxion.sales.facade.domain.response.ContactIdentityDocumentResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoContactResponse;
import com.tigo.galaxion.sales.facade.model.entity.ContactAddressEntity;
import com.tigo.galaxion.sales.facade.model.repository.ContactAddressRepository;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum.NIT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ContactRetrievalServiceTest {

    @Mock
    private AcquisitionProspectClient acquisitionProspectClient;
    @Mock
    private AddressClient addressClient;
    @Mock
    private ContactAddressRepository contactAddressRepository;
    @Mock
    private ContactIdentityDocumentRetrievalService contactIdentityDocumentRetrievalService;

    @InjectMocks
    private ContactRetrievalService contactRetrievalService;

    @Nested
    class GetContactResponseForAcquisitionTest {

        @Test
        void addressesExists_returnContactWithBillingAndDeliveryAddress() {
            //GIVEN
            var prospectRef = "ref";
            var billingAddress = AddressResponse.builder().street("street billing").build();
            var deliveryAddress = AddressResponse.builder().street("street delivery").build();

            when(acquisitionProspectClient.getContact(prospectRef)).thenReturn(TigoContactResponse.builder().build());

            var billingContactAddressEntity = ContactAddressEntity.builder().addressId(1L).build();
            when(contactAddressRepository.findByContactAddressId_ReferenceAndContactAddressId_Type(prospectRef, AddressTypeEnum.BILLING)).thenReturn(Optional.of(billingContactAddressEntity));
            var deliveryContactAddressEntity = ContactAddressEntity.builder().addressId(2L).build();
            when(contactAddressRepository.findByContactAddressId_ReferenceAndContactAddressId_Type(prospectRef, AddressTypeEnum.DELIVERY)).thenReturn(Optional.of(deliveryContactAddressEntity));

            when(addressClient.getAddress(1L)).thenReturn(billingAddress);
            when(addressClient.getAddress(2L)).thenReturn(deliveryAddress);
            when(contactIdentityDocumentRetrievalService.getContactIdentityDocumentResponse(prospectRef)).thenReturn(ContactIdentityDocumentResponse.builder().build());

            //WHEN
            var response = contactRetrievalService.getContactResponseForAcquisition(prospectRef);

            //THEN
            assertEquals(deliveryAddress.getStreet(), response.getDeliveryAddress().getStreetName());
            assertEquals(billingAddress.getStreet(), response.getBillingAddress().getStreetName());
        }

        @Test
        void addressesNotExists_returnContactWithoutBillingAndDeliveryAddress() {
            //GIVEN
            var prospectRef = "ref";

            when(acquisitionProspectClient.getContact(prospectRef)).thenReturn(TigoContactResponse.builder().build());
            when(contactAddressRepository.findByContactAddressId_ReferenceAndContactAddressId_Type(prospectRef, AddressTypeEnum.BILLING)).thenReturn(Optional.empty());
            when(contactAddressRepository.findByContactAddressId_ReferenceAndContactAddressId_Type(prospectRef, AddressTypeEnum.DELIVERY)).thenReturn(Optional.empty());
            when(contactIdentityDocumentRetrievalService.getContactIdentityDocumentResponse(prospectRef)).thenReturn(ContactIdentityDocumentResponse.builder().build());

            //WHEN
            var response = contactRetrievalService.getContactResponseForAcquisition(prospectRef);

            //THEN
            assertNull(response.getDeliveryAddress());
            assertNull(response.getBillingAddress());
        }

        @Test
        void contactWithIdentityDocument_returnContactWithIdentityDocument() {
            //GIVEN
            var prospectRef = "ref";
            var identityDocument = ContactIdentityDocumentResponse.builder()
                    .documentIdentifier("0123456789")
                    .nationality("CO")
                    .documentType(NIT)
                    .build();
            when(acquisitionProspectClient.getContact(prospectRef)).thenReturn(TigoContactResponse.builder().build());
            when(contactIdentityDocumentRetrievalService.getContactIdentityDocumentResponse(prospectRef)).thenReturn(identityDocument);

            //WHEN
            var response = contactRetrievalService.getContactResponseForAcquisition(prospectRef);

            //THEN
            assertEquals(identityDocument, response.getIdentityDocument());
        }
    }

}
