package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AdditionalAllowedOfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AmountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TigoCartResponseMapperTest {

    @Mock
    private TigoOfferResponseMapper tigoOfferResponseMapper;

    @InjectMocks
    private TigoCartResponseMapper cartResponseMapper;

    @Nested
    class BuildTigoCartResponseTest {

        @Test
        void withValidCart_returnMaltCartResponse() {
            // GIVEN
            var amount = 100L;
            var discountedAmount = 101L;
            var discountFrequency = "MONTHLY";
            var discountOccurrence = 7;

            var oneOffAmount = 100L;
            var discountedOneOffAmount = 101L;
            var upFrontAmount = 102L;
            var discountedUpFrontAmount = 103L;
            var amountVatIncluded = AmountResponse.builder()
                                                  .oneOffAmount(oneOffAmount)
                                                  .discountedOneOffAmount(discountedOneOffAmount)
                                                  .upFrontAmount(upFrontAmount)
                                                  .discountedUpFrontAmount(discountedUpFrontAmount)
                                                  .build();

            var serviceGroup = "serviceGroup";
            var numberOffer = 1L;
            var numberAdditionalAllowedOffer = AdditionalAllowedOfferResponse.builder()
                                                                             .serviceGroup(serviceGroup)
                                                                             .number(numberOffer)
                                                                             .build();

            var uuid = "uuid";
            var creditScore = "creditScore";
            var channelGroup = "channelGroup";
            var cartResponse = CartResponse.builder()
                                           .uuid(uuid)
                                           .creditScore(creditScore)
                                           .agreedTermsAndConditions(true)
                                           .channelGroup(channelGroup)
                                           .amountVatIncluded(amountVatIncluded)
                                           .numberAdditionalAllowedOffers(List.of(numberAdditionalAllowedOffer))
                                           .build();

            when(tigoOfferResponseMapper.buildTigoOfferResponses(any())).thenReturn(List.of());

            // WHEN
            var tigoCartResponse = cartResponseMapper.buildTigoCartResponse(cartResponse);

            // THEN
            assertEquals(uuid, tigoCartResponse.getUuid());
            assertEquals(creditScore, tigoCartResponse.getCreditScore());
            assertTrue(tigoCartResponse.getAgreedTermsAndConditions());
            assertEquals(channelGroup, tigoCartResponse.getChannelGroup());

            var tigoAmountResponse = tigoCartResponse.getAmountVatIncluded();
            assertEquals(oneOffAmount, tigoAmountResponse.getOneOffAmount());
            assertEquals(discountedOneOffAmount, tigoAmountResponse.getDiscountedOneOffAmount());
            assertEquals(upFrontAmount, tigoAmountResponse.getUpFrontAmount());
            assertEquals(discountedUpFrontAmount, tigoAmountResponse.getDiscountedUpFrontAmount());

            var mataNumberAdditionalAllowedOffer = tigoCartResponse.getNumberAdditionalAllowedOffers().get(0);
            assertEquals(numberOffer, mataNumberAdditionalAllowedOffer.getNumber());
            assertEquals(serviceGroup, mataNumberAdditionalAllowedOffer.getServiceGroup());
        }

    }
}
