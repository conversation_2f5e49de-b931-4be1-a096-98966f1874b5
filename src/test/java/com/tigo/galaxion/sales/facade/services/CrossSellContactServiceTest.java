package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.contact.ContactClient;
import com.tigo.galaxion.sales.facade.connector.contact.domain.enumeration.TypeEmailEnum;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PatchAddressRequest;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PatchEmailRequest;
import com.tigo.galaxion.sales.facade.connector.contact.domain.request.PatchPhoneNumberRequest;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactAddressResponse;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactEmailV2Response;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactPhoneNumberV2Response;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactV2Response;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.domain.response.CrossSellResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.AddressTypeEnum;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoAddressRequest;
import com.tigo.galaxion.sales.facade.domain.request.contact.TigoCrossSellDeliveryContactRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CrossSellContactServiceTest {

    @Captor
    ArgumentCaptor<PatchEmailRequest> patchEmailRequestCaptor;
    @Captor
    ArgumentCaptor<PatchPhoneNumberRequest> patchPhoneNumberRequestCaptor;
    @Captor
    ArgumentCaptor<PatchAddressRequest> patchAddressRequestCaptor;
    @Mock
    private CrossSellClient crossSellClient;
    @Mock
    private ContactClient contactClient;
    @InjectMocks
    private CrossSellContactService crossSellContactService;

    @Test
    void givenCrossSellWithDeliveryContactUuidWithoutChanges_doNothing() {
        // GIVEN
        var crossSellReference = "reference";
        var contactUuid = "deliveryContactUuid";

        var crossSellResponse = CrossSellResponse
                .builder()
                .contactDeliveryUuid(contactUuid)
                .build();
        when(crossSellClient.getCrossSell(crossSellReference)).thenReturn(crossSellResponse);

        when(contactClient.getContact(contactUuid)).thenReturn(new ContactV2Response());

        // WHEN
        var actual = crossSellContactService.updateContact(crossSellReference, new TigoCrossSellDeliveryContactRequest());

        // THEN
        assertNotNull(actual);
        verify(contactClient, never()).updateEmail(any(), any());
        verify(contactClient, never()).updatePhoneNumber(any(), any());
        verify(contactClient, never()).updateOrCreateAddress(any(), any());
    }

    @Test
    void givenCrossSellWithoutDeliveryContactUuid_shouldCallCreateContact() {
        // GIVEN
        var crossSellReference = "reference";
        var newPhoneNUmber = "newPhoneNumber";
        var newFirstName = "newFirstName";
        var newLastname = "newLastname";
        var newEmail = "newEmail";

        var crossSellResponse = CrossSellResponse
                .builder()
                .build();
        when(crossSellClient.getCrossSell(crossSellReference)).thenReturn(crossSellResponse);

        var newArea = "newArea";
        var newTown = "newTown";
        var newStreetName = "newStreetName";
        var newStreetNumber = "newStreetNumber";
        var newPostCode = "newPostCode";
        var newDeliveryAddress = TigoAddressRequest
                .builder()
                .area(newArea)
                .town(newTown)
                .streetName(newStreetName)
                .streetNumber(newStreetNumber)
                .postCode(newPostCode)
                .build();
        var crossSellDeliveryContactRequest = TigoCrossSellDeliveryContactRequest
                .builder()
                .firstName(newFirstName)
                .lastName(newLastname)
                .email(newEmail)
                .phoneNumber(newPhoneNUmber)
                .deliveryAddress(newDeliveryAddress)
                .build();

        // WHEN
        var actual = crossSellContactService.updateContact(crossSellReference, crossSellDeliveryContactRequest);

        // THEN
        verify(contactClient).createContact(any());
        verify(crossSellClient).updateCrossSell(anyString(), any());
        verify(contactClient, never()).updateEmail(any(), any());
        verify(contactClient, never()).updatePhoneNumber(any(), any());
        verify(contactClient, never()).updateOrCreateAddress(any(), any());
    }

    @Test
    void givenCrossSellWithDeliveryContactUuid_shouldCallUpdateContact() {
        // GIVEN
        var crossSellReference = "reference";
        var contactUuid = "deliveryContactUuid";
        var newPhoneNUmber = "newPhoneNumber";
        var newFirstName = "newFirstName";
        var newLastname = "newLastname";
        var newEmail = "newEmail";

        var crossSellResponse = CrossSellResponse
                .builder()
                .contactDeliveryUuid(contactUuid)
                .build();
        when(crossSellClient.getCrossSell(crossSellReference)).thenReturn(crossSellResponse);

        var email = ContactEmailV2Response
                .builder()
                .id(10L)
                .type(TypeEmailEnum.MAIN)
                .email("oldEmail")
                .build();
        var phoneNumber = ContactPhoneNumberV2Response
                .builder()
                .id(20L)
                .type("MOBILE")
                .phoneNumber("oldPhoneNumber")
                .build();
        var address = ContactAddressResponse
                .builder()
                .code("oldCode")
                .type(AddressTypeEnum.DELIVERY)
                .build();
        var contact = ContactV2Response
                .builder()
                .email(email)
                .phoneNumber(phoneNumber)
                .address(address)
                .build();
        when(contactClient.getContact(contactUuid)).thenReturn(contact);

        var newArea = "newArea";
        var newTown = "newTown";
        var newStreetName = "newStreetName";
        var newStreetNumber = "newStreetNumber";
        var newPostCode = "newPostCode";
        var newDeliveryAddress = TigoAddressRequest
                .builder()
                .area(newArea)
                .town(newTown)
                .streetName(newStreetName)
                .streetNumber(newStreetNumber)
                .postCode(newPostCode)
                .build();
        var crossSellDeliveryContactRequest = TigoCrossSellDeliveryContactRequest
                .builder()
                .firstName(newFirstName)
                .lastName(newLastname)
                .email(newEmail)
                .phoneNumber(newPhoneNUmber)
                .deliveryAddress(newDeliveryAddress)
                .build();

        // WHEN
        var actual = crossSellContactService.updateContact(crossSellReference, crossSellDeliveryContactRequest);

        // THEN
        assertNotNull(actual);

        verify(contactClient).updateEmail(eq(10L), patchEmailRequestCaptor.capture());
        var patchEmailRequest = patchEmailRequestCaptor.getValue();
        assertEquals(crossSellDeliveryContactRequest.getEmail(), patchEmailRequest.getEmail());

        verify(contactClient).updatePhoneNumber(eq(20L), patchPhoneNumberRequestCaptor.capture());
        var patchPhoneNumberRequest = patchPhoneNumberRequestCaptor.getValue();
        assertEquals(crossSellDeliveryContactRequest.getPhoneNumber(), patchPhoneNumberRequest.getPhoneNumber());
        assertEquals("MOBILE", patchPhoneNumberRequest.getType());

        verify(contactClient).updateOrCreateAddress(eq("deliveryContactUuid"), patchAddressRequestCaptor.capture());
        var patchAddressRequest = patchAddressRequestCaptor.getValue();
        var addressRequest = crossSellDeliveryContactRequest.getDeliveryAddress();
        assertEquals(addressRequest.getArea(), patchAddressRequest.getArea());
        assertEquals(addressRequest.getTown(), patchAddressRequest.getTown());
        assertEquals(addressRequest.getStreetName(), patchAddressRequest.getStreet());
        assertEquals(addressRequest.getStreetNumber(), patchAddressRequest.getStreetNumber());
        assertEquals(addressRequest.getPostCode(), patchAddressRequest.getCode());
        assertEquals(AddressTypeEnum.DELIVERY, patchAddressRequest.getType());
    }
}
