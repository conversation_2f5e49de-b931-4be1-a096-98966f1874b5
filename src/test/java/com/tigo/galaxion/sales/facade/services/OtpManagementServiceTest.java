package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.otpManagement.request.SendCodeRequest;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.GetTokenResponse;
import com.tigo.galaxion.sales.facade.connector.otpManagement.response.SendCodeResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.TestPropertySource;

import java.net.URI;

import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(locations = "classpath:application-test.properties")
public class OtpManagementServiceTest {


    @InjectMocks
    private OtpManagementService otpManagementService;

    @BeforeEach
    void setUp() {
        GetTokenResponse getTokenResponse = GetTokenResponse.builder()
                .access_token("test-token")
                .build();

    //    when(otpManagementTokenClient.getToken(any(URI.class), any(GetTokenRequest.class)))
    //            .thenReturn(getTokenResponse);
    }



}