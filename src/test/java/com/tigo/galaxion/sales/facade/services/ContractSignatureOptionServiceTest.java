package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.model.entity.ProspectEntity;
import com.tigo.galaxion.sales.facade.model.repository.ProspectRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ContractSignatureOptionServiceTest {

    @Mock
    private ProspectRepository prospectRepository;

    @InjectMocks
    private ContractSignatureOptionService contractSignatureOptionService;

    @Test
    void updateContractSignatureOption_prospectExist_updateProspect() {
        // GIVEN
        var prospectRef = "RO6EW4O9";
        var contractSignatureOption = "SCRIVE";
        var prospectEntity = ProspectEntity.builder().reference(prospectRef).contractSignatureOption("SHOP").build();
        when(prospectRepository.findByReference(prospectRef)).thenReturn(Optional.of(prospectEntity));
        when(prospectRepository.save(any())).thenReturn(prospectEntity);

        // WHEN
        contractSignatureOptionService.updateContractSignatureOption(prospectRef,contractSignatureOption);

        // THEN
        verify(prospectRepository).save(prospectEntity);
    }

    @Test
    void updateContractSignatureOption_prospectNotExist_createProspect() {
        // GIVEN
        var prospectRef = "RO6EW4O9";
        var contractSignatureOption = "SCRIVE";
        var prospectEntity = ProspectEntity.builder().reference(prospectRef).contractSignatureOption(contractSignatureOption).build();
        when(prospectRepository.findByReference(prospectRef)).thenReturn(Optional.empty());
        when(prospectRepository.save(any())).thenReturn(prospectEntity);

        // WHEN
        contractSignatureOptionService.updateContractSignatureOption(prospectRef,contractSignatureOption);

        // THEN
        verify(prospectRepository).save(prospectEntity);
    }
}
