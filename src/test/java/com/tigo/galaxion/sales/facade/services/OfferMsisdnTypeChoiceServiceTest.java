package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.domain.problem.OfferNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoOfferResponse;
import com.tigo.galaxion.sales.facade.model.entity.OfferEntity;
import com.tigo.galaxion.sales.facade.model.repository.OfferRepository;
import com.tigo.galaxion.sales.facade.services.retrieval.OfferRetrievalService;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OfferMsisdnTypeChoiceServiceTest {

    @Mock
    private CartService cartService;

    @Mock
    private OfferRetrievalService offerRetrievalService;

    @Mock
    private OfferRepository offerRepository;

    @InjectMocks
    private OfferMsisdnTypeChoiceService offerMsisdnTypeChoiceService;

    private static final String MSISDN_TYPE_CHOICE = "RANDOM";
    private static final String REFERENCE = "RO6EW4O9";

    @Nested
    class UpdateForProspectTest {

        @Test
        void update_offerExist_isNumberUpdated() {
            // GIVEN
            var offerId = 1L;
            var cartOfferResponse = TigoOfferResponse.builder().id(1L).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForProspect(REFERENCE)).thenReturn(cartResponse);
            when(offerRetrievalService.get(offerId)).thenReturn(Optional.of(new OfferEntity()));

            // WHEN
            var tigoCartResponse = offerMsisdnTypeChoiceService.updateForProspect(REFERENCE, offerId, MSISDN_TYPE_CHOICE);

            // THEN
            var offerResponse = tigoCartResponse.getOffers().get(0);
            assertEquals(MSISDN_TYPE_CHOICE, offerResponse.getNumberType());
        }

        @Test
        void update_offerNotExistInCart_throwException() {
            // GIVEN
            var offerId = 2L;
            var cartOfferResponse = TigoOfferResponse.builder().id(1L).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForProspect(REFERENCE)).thenReturn(cartResponse);

            // WHEN
            // THEN
            assertThrows(OfferNotFoundProblem.class, () -> offerMsisdnTypeChoiceService.updateForProspect(REFERENCE, offerId, MSISDN_TYPE_CHOICE));
        }

        @Test
        void update_offerNotExistInSales_createNewOffer() {
            // GIVEN
            var offerId = 1L;
            var cartOfferResponse = TigoOfferResponse.builder().id(offerId).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForProspect(REFERENCE)).thenReturn(cartResponse);

            // WHEN
            var tigoCartResponse = offerMsisdnTypeChoiceService.updateForProspect(REFERENCE, offerId, MSISDN_TYPE_CHOICE);

            // THEN
            var offerResponse = tigoCartResponse.getOffers().get(0);
            assertEquals(MSISDN_TYPE_CHOICE, offerResponse.getNumberType());
        }
    }

    @Nested
    class UpdateForCrossSellTest {

        @Test
        void update_offerExist_isNumberUpdated() {
            // GIVEN
            var offerId = 1L;
            var cartOfferResponse = TigoOfferResponse.builder().id(1L).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForCrossSell(REFERENCE)).thenReturn(cartResponse);
            when(offerRetrievalService.get(offerId)).thenReturn(Optional.of(new OfferEntity()));

            // WHEN
            var tigoCartResponse = offerMsisdnTypeChoiceService.updateForCrossSell(REFERENCE, offerId, MSISDN_TYPE_CHOICE);

            // THEN
            var offerResponse = tigoCartResponse.getOffers().get(0);
            assertEquals(MSISDN_TYPE_CHOICE, offerResponse.getNumberType());
        }

        @Test
        void update_offerNotExistInCart_throwException() {
            // GIVEN
            var offerId = 2L;
            var cartOfferResponse = TigoOfferResponse.builder().id(1L).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForCrossSell(REFERENCE)).thenReturn(cartResponse);

            // WHEN
            // THEN
            assertThrows(OfferNotFoundProblem.class, () -> offerMsisdnTypeChoiceService.updateForCrossSell(REFERENCE, offerId, MSISDN_TYPE_CHOICE));
        }

        @Test
        void update_offerNotExistInSales_createNewOffer() {
            // GIVEN
            var offerId = 1L;
            var cartOfferResponse = TigoOfferResponse.builder().id(offerId).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForCrossSell(REFERENCE)).thenReturn(cartResponse);

            // WHEN
            var tigoCartResponse = offerMsisdnTypeChoiceService.updateForCrossSell(REFERENCE, offerId, MSISDN_TYPE_CHOICE);

            // THEN
            var offerResponse = tigoCartResponse.getOffers().get(0);
            assertEquals(MSISDN_TYPE_CHOICE, offerResponse.getNumberType());
        }
    }
}
