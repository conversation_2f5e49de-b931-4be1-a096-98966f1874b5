package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.domain.request.PaymentNotificationRequest;
import com.tigo.galaxion.sales.facade.services.order_notification.PaymentNotificationService;

import mc.monacotelecom.workflow.app.dto.WorkflowSignalRequestDTO;

@ExtendWith(MockitoExtension.class)
public class PaymentNotificationServiceTest {

    @Mock
    WorkflowEngineClient wfeClient;

    @InjectMocks
    PaymentNotificationService paymentNotificationService;

    @Test
    void notifyPaymentTest() {
        // GIVEN
        var request = PaymentNotificationRequest.builder()                
                .paymentStatus("COMPLETED")
                .paymentDate("2024-08-27T23:11:23")
                .build();

        doNothing().when(wfeClient).processSignal(any(WorkflowSignalRequestDTO.class));

        // WHEN
        paymentNotificationService.notifyPayment(request, "100055");

        // THEN
        verify(wfeClient, times(1)).processSignal(any(WorkflowSignalRequestDTO.class));
        assertNotNull(paymentNotificationService);
    }

}
