package com.tigo.galaxion.sales.facade.factory;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Address;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.ChannelNotification;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.CustomerReference;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;

public class ProspectLeadResponseFactory {

	public ProspectLeadResponse getProspectLeadResponse() {
		ProspectLeadResponse prospectLeadResponse = new ProspectLeadResponse();
				prospectLeadResponse.setAddress(new Address[]{
					AddressFactory.getAddress(1, "1"),
					AddressFactory.getAddress(2, "2")
				});
				prospectLeadResponse.setCustomer(CustomerFactory.getCustomer("1"));
				prospectLeadResponse.setCustomerReference(new CustomerReference[]{
					CustomerReferenceFactory.getCustomerReference("1"),
					CustomerReferenceFactory.getCustomerReference("2")
				});
				prospectLeadResponse.setScoring(ScoringFactory.getScoring(1));
				prospectLeadResponse.setChannelNotification(new ChannelNotification[]{
					ChannelNotificationFactory.getChannelNotification(1),
					ChannelNotificationFactory.getChannelNotification(2)
				});
		return prospectLeadResponse;
	}
}
