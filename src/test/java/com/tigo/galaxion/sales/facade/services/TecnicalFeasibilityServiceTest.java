package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.TecnicalFeasibilityClient;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.FoCoverage;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.request.FeasibilityServicesRequest;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.request.TecnicalFeasibilityRequestBody;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.response.FeasibilityServicesResponse;
import com.tigo.galaxion.sales.facade.connector.tecnicalFeasibility.domain.response.TecnicalFeasibilityResponseBody;

@ExtendWith(MockitoExtension.class)
public class TecnicalFeasibilityServiceTest {
    @Mock
    TecnicalFeasibilityClient tecnicalFeasibilityClient;

    @InjectMocks
    TecnicalFeasibilityService tecnicalFeasibilityService;

    @Test
    void TecnicalFeasibilityValidTest() {
        // GIVEN
        var request = TecnicalFeasibilityRequestBody
                .builder()
                .feasibilityServicesRequest(
                        FeasibilityServicesRequest
                                .builder()
                                .addressCode("1431065734")
                                .latitude("6.15728425979614")
                                .longitude("-75.604972839355")
                                .municipalityCode("05631")
                                .requestCode("4d798365-85aa-4171-b31b-ff529a903456")
                                .build())
                .build();

        var expectedResponse = TecnicalFeasibilityResponseBody
                .builder()
                .feasibilityServicesResponse(
                        FeasibilityServicesResponse.builder()
                                .foCoverage(FoCoverage.builder().coverage("defaultCoverageValue").build())
                                .build())
                .build();
        when(tecnicalFeasibilityClient.createTecnical((any()))).thenReturn(expectedResponse);

        var actualResponse = tecnicalFeasibilityService.createTecnical(request);
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        assertEquals("defaultCoverageValue",
                actualResponse.getFeasibilityServicesResponse().getFoCoverage().getCoverage());
    }
}
