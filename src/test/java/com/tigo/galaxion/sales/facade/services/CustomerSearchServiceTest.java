package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.domain.problem.CustomerIdentityDocumentNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.request.CustomerSearchRequest;
import com.tigo.galaxion.sales.facade.domain.response.CustomerSearchResponse;
import com.tigo.galaxion.sales.facade.model.entity.AccountContactView;
import com.tigo.galaxion.sales.facade.model.repository.AccountContactViewRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomerSearchServiceTest {

    @InjectMocks
    private CustomerSearchService customerSearchService;

    @Mock
    private AccountContactViewRepository customerRepo;

    @Test
    void testGetCustomerSearch_ExistingIdentityDocument() {
        // Arrange
        CustomerSearchRequest request = new CustomerSearchRequest("*********", "CC");
        AccountContactView identityDocument = new AccountContactView(
                "1",
                "2",
                "John",
                "Doe",
                "*********",
                "CC",
                new Date(),
                "*********0",
                "<EMAIL>");

        when(customerRepo.findByIdentityDocumentIdentifierAndIdentityDocumentType(request.getIdentifier(),
                request.getType()))
                .thenReturn(List.of(identityDocument));

        // Act
        CustomerSearchResponse response = customerSearchService.getCustomerSearch(request);

        // Assert
        assertEquals("CC", response.getType());
        assertEquals("*********", response.getIdentifier());
        assertEquals("John", response.getFirst_name());
        assertEquals("Doe", response.getLast_name());
        assertEquals("<EMAIL>", response.getEmail());
        assertEquals("*********0", response.getPhone_number());
    }

    @Test
    void testGetCustomerSearch_NonExistingIdentityDocument() {
        // Arrange
        CustomerSearchRequest request = new CustomerSearchRequest("*********", "CC");
        when(customerRepo.findByIdentityDocumentIdentifierAndIdentityDocumentType(request.getIdentifier(),
                request.getType()))
                .thenReturn(List.of());

        // Act and Assert
        assertThrows(
                CustomerIdentityDocumentNotFoundProblem.class,
                () -> customerSearchService.getCustomerSearch(request));
    }

}