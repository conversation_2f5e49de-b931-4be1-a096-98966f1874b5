package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.tigo.galaxion.sales.facade.domain.request.ProspectusLogRequest;
import com.tigo.galaxion.sales.facade.domain.response.ProspectusLogResponse;
import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogEntity;
import com.tigo.galaxion.sales.facade.model.entity.ProspectusLogId;
import com.tigo.galaxion.sales.facade.model.repository.ProspectusLogRepository;

@ExtendWith(MockitoExtension.class)
public class ProspectusLogServiceTest {

        @InjectMocks
        private ProspectusLogService prospectusLogService;

        @Mock
        private ProspectusLogRepository prospectLogRepository;

        @Test
        void prospectusLogCreate_returnProspectusLogCreateResponse() {
                // Arrange
                String reference = "5047EY04";
                String customer_id_type = "CC";
                String customer_id_number = "9874563210";
                String user_id = "mamayer";
                String user_name = "María Alejandra Meyer";
                String ip_equipment = "***********";
                String sales_channel = "TELESALES";
                String authentication_type = "NA";
                String transaction = "Nueva_Suscripcion";

                LocalDateTime create_at = LocalDateTime.now();
                ProspectusLogRequest request = ProspectusLogRequest.builder()
                                .reference(reference)
                                .customer_id_type(customer_id_type)
                                .customer_id_number(customer_id_number)
                                .user_id(user_id)
                                .user_name(user_name)
                                .ip_equipment(ip_equipment)
                                .sales_channel(sales_channel)
                                .authentication_type(authentication_type)
                                .transaction(transaction)
                                .build();

                ProspectusLogEntity prospectusLogEntity = ProspectusLogEntity.builder()
                                .prospectusLogId(ProspectusLogId.builder()
                                                .reference(reference)
                                                .customer_id_type(customer_id_type)
                                                .customer_id_number(customer_id_number)
                                                .build())
                                .user_id(user_id)
                                .user_name(user_name)
                                .ip_equipment(ip_equipment)
                                .sales_channel(sales_channel)
                                .authentication_type(authentication_type)
                                .transaction(transaction)
                                .createdAt(create_at)
                                .build();

                when(prospectLogRepository.save(any(ProspectusLogEntity.class))).thenReturn(prospectusLogEntity);

                // Act
                ProspectusLogResponse response = prospectusLogService.createProspectusLog(request, ip_equipment);

                // Assert
                assertNotNull(response);
                assertEquals(reference, response.getReference());
                assertEquals(customer_id_type, response.getCustomer_id_type());
                assertEquals(customer_id_number, response.getCustomer_id_number());
                assertEquals(user_id, response.getUser_id());
                assertEquals(user_name, response.getUser_name());
                assertEquals(ip_equipment, response.getIp_equipment());
                assertEquals(sales_channel, response.getSales_channel());
                assertEquals(authentication_type, response.getAuthentication_type());
                assertEquals(transaction, response.getTransaction());
                assertNotNull(response.getCreated_at());

                verify(prospectLogRepository, times(1)).save(any(ProspectusLogEntity.class));
        }

        @Test
        void prospectusLogGetProspectusLog_returnProspectusLogResponse() {
                // Arrange
                String reference = "5047EY04";
                String customer_id_type = "CC";
                String customer_id_number = "9874563210";
                String user_id = "mamayer";
                String user_name = "María Alejandra Meyer";
                String ip_equipment = "***********";
                String sales_channel = "TELESALES";
                String authentication_type = "NA";
                String transaction = "Nueva_Suscripcion";

                LocalDateTime create_at = LocalDateTime.now();
                ProspectusLogRequest request = ProspectusLogRequest.builder()
                                .reference(reference)
                                .customer_id_type(customer_id_type)
                                .customer_id_number(customer_id_number)
                                .user_id(user_id)
                                .user_name(user_name)
                                .ip_equipment(ip_equipment)
                                .sales_channel(sales_channel)
                                .authentication_type(authentication_type)
                                .transaction(transaction)
                                .build();

                ProspectusLogEntity prospectusLogEntity = ProspectusLogEntity.builder()
                                .prospectusLogId(ProspectusLogId.builder()
                                                .reference(reference)
                                                .customer_id_type(customer_id_type)
                                                .customer_id_number(customer_id_number)
                                                .build())
                                .user_id(user_id)
                                .user_name(user_name)
                                .ip_equipment(ip_equipment)
                                .sales_channel(sales_channel)
                                .authentication_type(authentication_type)
                                .transaction(transaction)
                                .createdAt(create_at)
                                .build();

                when(prospectLogRepository.findByProspectusLogId_ReferenceOrderByCreatedAtDesc(any(String.class))).thenReturn(List.of(prospectusLogEntity));

                // Act
                ProspectusLogResponse response = prospectusLogService.getProspectReference(reference);

                // Assert
                assertNotNull(response);
                assertEquals(reference, response.getReference());
                assertEquals(customer_id_type, response.getCustomer_id_type());
                assertEquals(customer_id_number, response.getCustomer_id_number());
                assertEquals(user_id, response.getUser_id());
                assertEquals(user_name, response.getUser_name());
                assertEquals(ip_equipment, response.getIp_equipment());
                assertEquals(sales_channel, response.getSales_channel());
                assertEquals(authentication_type, response.getAuthentication_type());
                assertEquals(transaction, response.getTransaction());
                assertNotNull(response.getCreated_at());

                verify(prospectLogRepository, times(1)).findByProspectusLogId_ReferenceOrderByCreatedAtDesc(any(String.class));
        }
}