package com.tigo.galaxion.sales.facade.factory;

import com.tigo.galaxion.sales.facade.connector.alfresco.response.AlfrescoAuthResponse;

public class AlfrescoAuthResponseFactory {

	public static AlfrescoAuthResponse getAuthResponse(int id, String strings){
		return AlfrescoAuthResponse.builder()
        .accessToken(strings)
        .tokenType(strings)
        .refreshToken(strings)
        .expiresIn(6000000000L)
        .scope(strings)
        .id(id)
        .jti(strings)
        .build();
	}
}
