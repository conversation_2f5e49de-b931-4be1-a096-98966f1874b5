package com.tigo.galaxion.sales.facade.factory;

import java.math.BigInteger;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Address;

public class AddressFactory {

	public static Address getAddress(int ints, String strings) {
		return Address.builder()
			.addressType(ints)
			.departmentCode(strings)
			.department(strings)
			.municipalityCode(strings)
			.municipality(strings)
			.address(strings)
			.latitude(strings)
			.longitude(strings)
			.stratum(ints)
			.microzone(strings)
			.countryCode(strings)
			.addressCode(strings)
			.descriptionAddress(strings)
			.coreInstallationAddressId(new BigInteger(strings))
			.build();
	}
}
