package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.domain.request.GetTaskRequest;
import com.tigo.galaxion.sales.facade.services.field_service.get_task.GetTaskClient;
import com.tigo.galaxion.sales.facade.soap.get_task.*;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.ws.client.core.WebServiceTemplate;

import java.text.SimpleDateFormat;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.any;


@ExtendWith(SpringExtension.class)
public class GetTaskClientTest {

    @Mock
    private WebServiceTemplate webServiceTemplate;

    @InjectMocks
    private GetTaskClient getTaskClient;

    @Nested
    class GetTaskClientUnitTest {


        GetTaskRequest buildSampleInputRequest() {
            GetTaskRequest request = new GetTaskRequest();
            request.setCallId("14875214");
            return request;
        }

        @Test
        void testGetTask_success() throws Exception {
            GetTaskRequest getTaskRequest = buildSampleInputRequest();

            getTaskRequest.setCallId("14875214");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            Stamp stamp = new Stamp();
            stamp.setCreatedBy("system");
            stamp.setTimeCreated(javax.xml.datatype.DatatypeFactory.newInstance().newXMLGregorianCalendar("2024-01-02"));
            stamp.setCreatingProcess(1001);
            stamp.setModifiedBy("admin");
            stamp.setTimeModified(javax.xml.datatype.DatatypeFactory.newInstance().newXMLGregorianCalendar("2024-01-03"));
            stamp.setModifyingProcess(2002);

            TaskRequiredSkill1 requiredSkill1 = new TaskRequiredSkill1();
            requiredSkill1.setKey("skillA");
            requiredSkill1.setSkillLevel(5);
            RequiredSkills1 requiredSkills1 = new RequiredSkills1();
            requiredSkills1.setTaskRequiredSkill1(requiredSkill1);

            Attributes attrs = new Attributes();
            attrs.setType("typeA");
            attrs.setUrl("http://example.com/resource");

            MCMVMaterialUsedKey materialUsedKey = new MCMVMaterialUsedKey();
            materialUsedKey.setAttributes(attrs);
            materialUsedKey.setId("mat-123");
            materialUsedKey.setIsDeleted(false);
            materialUsedKey.setProductConsumedNumber("consumed123");
            materialUsedKey.setCreatedDate(javax.xml.datatype.DatatypeFactory.newInstance().newXMLGregorianCalendar("2024-01-05"));
            materialUsedKey.setCreatedById("creator-id");
            materialUsedKey.setLastModifiedDate(javax.xml.datatype.DatatypeFactory.newInstance().newXMLGregorianCalendar("2024-01-06"));
            materialUsedKey.setLastModifiedById("modifier-id");
            materialUsedKey.setSystemModstamp(javax.xml.datatype.DatatypeFactory.newInstance().newXMLGregorianCalendar("2024-01-07"));
            materialUsedKey.setLastViewedDate(javax.xml.datatype.DatatypeFactory.newInstance().newXMLGregorianCalendar("2024-01-08"));
            materialUsedKey.setLastReferencedDate(javax.xml.datatype.DatatypeFactory.newInstance().newXMLGregorianCalendar("2024-01-09"));
            materialUsedKey.setWorkOrderId("wo-001");
            materialUsedKey.setWorkOrderLineItemId("line-001");
            materialUsedKey.setProductItemId("prod-item-1");
            materialUsedKey.setPricebookEntryId("pricebook-1");
            materialUsedKey.setProduct2Id("prod2-1");
            materialUsedKey.setProductName("Cable Modem");
            materialUsedKey.setQuantityUnitOfMeasure("Unidad");
            materialUsedKey.setQuantityConsumed(2.0);
            materialUsedKey.setUnitPrice(100L);
            materialUsedKey.setDescription("desc");
            materialUsedKey.setDiscount("0.0");
            materialUsedKey.setListPrice(120.0);
            materialUsedKey.setTotalPrice(200L);
            materialUsedKey.setSubtotal(180L);
            materialUsedKey.setIsConsumed(true);
            materialUsedKey.setMCExternalRefIDC("ext-1");
            materialUsedKey.setMCAreaOperativaC("Panama");
            materialUsedKey.setMCSerialNumberC("SER123");
            materialUsedKey.setMCCountryC("PA");
            materialUsedKey.setMCServiceResourceC("servRes");
            materialUsedKey.setMCProduct2C("prod2Ref");

            Date earlyStart = sdf.parse("2024-02-01");
            Date dueDate = sdf.parse("2024-02-02");
            Date lateStart = sdf.parse("2024-02-03");
            Date openDate = sdf.parse("2024-02-01");
            Date contactDate = sdf.parse("2024-02-02");
            Date confirmationDate = sdf.parse("2024-02-03");
            Date appoinmentStart = sdf.parse("2024-02-04");
            Date appoinmentFinish = sdf.parse("2024-02-05");

            TaskResponse taskResponse = new TaskResponse();
            taskResponse.setKey(99);
            taskResponse.setRevision(2);
            taskResponse.setStamp(stamp);
            taskResponse.setCallID("callid-123");
            taskResponse.setNumber(321);
            taskResponse.setEarlyStart(earlyStart);
            taskResponse.setDueDate(dueDate);
            taskResponse.setLateStart(lateStart);
            taskResponse.setPriority("Alta");
            taskResponse.setStatus("Abierto");
            taskResponse.setCustomer("Luis García");
            taskResponse.setCalendar("MainCalendar");
            taskResponse.setRegion("Panama");
            taskResponse.setDistrict("Distrito Norte");
            taskResponse.setPostcode("0801");
            taskResponse.setPreferredEngineers("John Doe");
            taskResponse.setContractType("Full");
            taskResponse.setOpenDate(openDate);
            taskResponse.setContactDate(contactDate);
            taskResponse.setConfirmationDate(confirmationDate);
            taskResponse.setTaskType("Instalación");
            taskResponse.setDuration(60.0);
            taskResponse.setRequiredEngineers("Jane");
            taskResponse.setNumberOfRequiredEngineers(2);
            taskResponse.setRequiredSkills2("Electricidad");
            taskResponse.setEngineerType("Senior");
            taskResponse.setRequiredEngineerTools("Drill");
            taskResponse.setCritical(1);
            taskResponse.setTimeDependencies("dep1");
            taskResponse.setEngineerDependencies("dep2");
            taskResponse.setAppointmentStart(appoinmentStart);
            taskResponse.setAppointmentFinish(appoinmentFinish);
            taskResponse.setContactName("Carlos Client");
            taskResponse.setContactPhoneNumber(5551234);
            taskResponse.setBinaryData("N/A");
            taskResponse.setLatitude(9.01);
            taskResponse.setLongitude(-79.5);
            taskResponse.setGisDataSource(101);
            taskResponse.setStreet("Av Central");
            taskResponse.setCity("Panama");
            taskResponse.setMcState("Activo");
            taskResponse.setState("PA");
            taskResponse.setTaskStatusContext(1);
            taskResponse.setIsCrewTask(0);
            taskResponse.setCountryID("PA");
            taskResponse.setEngineerRequirements("Certificado");
            taskResponse.setIsScheduled(1);
            taskResponse.setCustomerEmail(0);
            taskResponse.setExcludedEngineers(0);
            taskResponse.setRequiredCrewSize(0);
            taskResponse.setInJeopardy(0);
            taskResponse.setPinned(0);
            taskResponse.setJeopardyState(0);
            taskResponse.setDisplayStatus("Abierto");
            Date dispatchDate = sdf.parse("2024-02-06");
            Date scheduleDate = sdf.parse("2024-02-07");
            Date displayDate = sdf.parse("2024-02-08");
            Date onSiteDate = sdf.parse("2024-02-09");
            taskResponse.setDispatchDate(dispatchDate);
            taskResponse.setScheduleDate(scheduleDate);
            taskResponse.setDisplayDate(displayDate);
            taskResponse.setOnSiteDate(onSiteDate);
            taskResponse.setComment("Todo bien");
            taskResponse.setCustomerReference("ref321");
            taskResponse.setStateSubdivision("Betania");
            taskResponse.setCitySubdivision("San Francisco");
            taskResponse.setTeam("Equipo A");
            taskResponse.setSignature("firmado");
            taskResponse.setExternalRefID("ext123");
            taskResponse.setPartsUsed("Cable, Modem");
            taskResponse.setAssets("asset1, asset2");
            taskResponse.setBackReportings("N/A");
            taskResponse.setTaskTypeCategory("Daños");
            taskResponse.setWorkOrderItem("WO-321");
            CustomerAccount customerAccount = new CustomerAccount();
            customerAccount.setId("CA123");
            customerAccount.setIsDeleted(false);
            customerAccount.setName("Luis García");

            taskResponse.setUserCustomerAccount(customerAccount);

            GtTaskResponse mockResponse = new GtTaskResponse();
            mockResponse.setTask(taskResponse);
            when(webServiceTemplate.marshalSendAndReceive(any(GtTaskRequest.class))).thenReturn(mockResponse);

            GtTaskResponse response = getTaskClient.getTask(getTaskRequest);


            assertEquals(99, response.getTask().getKey());
            assertEquals(2, response.getTask().getRevision());
            assertEquals("callid-123", response.getTask().getCallID());
            assertEquals(321, response.getTask().getNumber());

            SimpleDateFormat sdff = new SimpleDateFormat("yyyy-MM-dd");
            assertEquals("2024-02-01", sdff.format(response.getTask().getEarlyStart()));
            assertEquals("2024-02-02", sdff.format(response.getTask().getDueDate()));
            assertEquals("2024-02-03", sdff.format(response.getTask().getLateStart()));
            assertEquals("2024-02-01", sdff.format(response.getTask().getOpenDate()));
            assertEquals("2024-02-02", sdff.format(response.getTask().getContactDate()));
            assertEquals("2024-02-03", sdff.format(response.getTask().getConfirmationDate()));
            assertEquals("2024-02-04", sdff.format(response.getTask().getAppointmentStart()));
            assertEquals("2024-02-05", sdff.format(response.getTask().getAppointmentFinish()));
            assertEquals("2024-02-06", sdff.format(response.getTask().getDispatchDate()));
            assertEquals("2024-02-07", sdff.format(response.getTask().getScheduleDate()));
            assertEquals("2024-02-08", sdff.format(response.getTask().getDisplayDate()));
            assertEquals("2024-02-09", sdff.format(response.getTask().getOnSiteDate()));

            assertEquals("Alta", response.getTask().getPriority());
            assertEquals("Abierto", response.getTask().getStatus());
            assertEquals("Luis García", response.getTask().getCustomer());
            assertEquals("MainCalendar", response.getTask().getCalendar());
            assertEquals("Panama", response.getTask().getRegion());
            assertEquals("Distrito Norte", response.getTask().getDistrict());
            assertEquals("0801", response.getTask().getPostcode());
            assertEquals("John Doe", response.getTask().getPreferredEngineers());
            assertEquals("Full", response.getTask().getContractType());
            assertEquals("Instalación", response.getTask().getTaskType());
            assertEquals(60.0, response.getTask().getDuration());
            assertEquals("Jane", response.getTask().getRequiredEngineers());
            assertEquals(2, response.getTask().getNumberOfRequiredEngineers());
            assertEquals("Electricidad", response.getTask().getRequiredSkills2());
            assertEquals("Senior", response.getTask().getEngineerType());
            assertEquals("Drill", response.getTask().getRequiredEngineerTools());
            assertEquals(1, response.getTask().getCritical());
            assertEquals("dep1", response.getTask().getTimeDependencies());
            assertEquals("dep2", response.getTask().getEngineerDependencies());
            assertEquals("Carlos Client", response.getTask().getContactName());
            assertEquals(5551234, response.getTask().getContactPhoneNumber());
            assertEquals("N/A", response.getTask().getBinaryData());
            assertEquals(9.01, response.getTask().getLatitude());
            assertEquals(-79.5, response.getTask().getLongitude());
            assertEquals(101, response.getTask().getGisDataSource());
            assertEquals("Av Central", response.getTask().getStreet());
            assertEquals("Panama", response.getTask().getCity());
            assertEquals("Activo", response.getTask().getMcState());
            assertEquals("PA", response.getTask().getState());
            assertEquals(1, response.getTask().getTaskStatusContext());
            assertEquals(0, response.getTask().getIsCrewTask());
            assertEquals("PA", response.getTask().getCountryID());
            assertEquals("Certificado", response.getTask().getEngineerRequirements());
            assertEquals(1, response.getTask().getIsScheduled());
            assertEquals(0, response.getTask().getCustomerEmail());
            assertEquals(0, response.getTask().getExcludedEngineers());
            assertEquals(0, response.getTask().getRequiredCrewSize());
            assertEquals(0, response.getTask().getInJeopardy());
            assertEquals(0, response.getTask().getPinned());
            assertEquals(0, response.getTask().getJeopardyState());
            assertEquals("Abierto", response.getTask().getDisplayStatus());
            assertEquals("Todo bien", response.getTask().getComment());
            assertEquals("ref321", response.getTask().getCustomerReference());
            assertEquals("Betania", response.getTask().getStateSubdivision());
            assertEquals("San Francisco", response.getTask().getCitySubdivision());
            assertEquals("Equipo A", response.getTask().getTeam());
            assertEquals("firmado", response.getTask().getSignature());
            assertEquals("ext123", response.getTask().getExternalRefID());
            assertEquals("Cable, Modem", response.getTask().getPartsUsed());
            assertEquals("asset1, asset2", response.getTask().getAssets());
            assertEquals("N/A", response.getTask().getBackReportings());
            assertEquals("Daños", response.getTask().getTaskTypeCategory());
            assertEquals("WO-321", response.getTask().getWorkOrderItem());
            assertNotNull(response.getTask().getUserCustomerAccount());
            assertEquals("CA123", response.getTask().getUserCustomerAccount().getId());
            assertFalse(response.getTask().getUserCustomerAccount().isIsDeleted());
            assertEquals("Luis García", response.getTask().getUserCustomerAccount().getName());
        }

        @Test
        void testGetTask_whenTemplateThrowsException_shouldPropagate() {
            GetTaskRequest getTaskRequest = buildSampleInputRequest();
            when(webServiceTemplate.marshalSendAndReceive(any(GtTaskRequest.class)))
                    .thenThrow(new RuntimeException("Error de prueba"));
            Exception thrown = assertThrows(Exception.class, () -> getTaskClient.getTask(getTaskRequest));
            assertTrue(thrown.getMessage().contains("Error de prueba"));
        }

        @Test
        void testGetTask_whenTemplateReturnsNull_shouldReturnNull() throws Exception {
            GetTaskRequest getTaskRequest = buildSampleInputRequest();
            when(webServiceTemplate.marshalSendAndReceive(any(GtTaskRequest.class))).thenReturn(null);
            GtTaskResponse response = getTaskClient.getTask(getTaskRequest);
            assertNull(response, "La respuesta debe ser null si el template retorna null");
        }
    }
}