package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;

import org.h2.command.dml.MergeUsing.When;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.tigo.galaxion.sales.facade.connector.customer.search.ExternalCustomerSearchClient;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.request.ExternalCustomerRequest;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.Address;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.AgeRange;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.BasicInformation;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.Emails;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomer;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.ExternalCustomerFullDetail;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.MobilePhones;
import com.tigo.galaxion.sales.facade.connector.customer.search.domain.response.Phones;
import com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum;
import com.tigo.galaxion.sales.facade.domain.problem.ExternalCustomerExpeditionDateProblem;
import com.tigo.galaxion.sales.facade.domain.problem.ExternalCustomerSearchNotFoundProblem;

@ExtendWith(MockitoExtension.class)
public class ExternalCustomerSearchServiceTest {

  @Mock
  private ExternalCustomerSearchClient externalCustomerSearchClient;

  @InjectMocks
  private ExternalCustomerSearchService externalCustomerSearchService;

  @Test
  void testSearchCustomerByExternalId_Success_NIT() {
    // GIVEN
    BasicInformation basicInformation = new BasicInformation(
        "economicActivivty",
        "CIIU",
        "documentStatus",
        "2024-01-01T00:00:00.000+00:00",
        "M",
        "1",
        "CO",
        "hola nuevo mundo !",
        "mundo",
        "!",
        "hola",
        "nuevo",
        "12345",
        "CC",
        new AgeRange("18", "99"));

    Address address = new Address(
        "12345",
        "asdfghjklñ",
        "1",
        "MiDC",
        "algo",
        "12",
        "algo2",
        "345",
        "8",
        "1",
        "20",
        "2024-01-02",
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        "null",
        "2024-01-31");
    ArrayList<Address> listOfAddresses = new ArrayList<Address>();
    listOfAddresses.add(address);

    Phones phones = new Phones(
        "CAS",
        "12345678",
        "algo",
        "11",
        "algo2",
        "001",
        "1",
        "1",
        "1",
        "2024-01-01",
        "1138221",
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        "null",
        "2024-01-31",
        "502");

    MobilePhones cellPhones = new MobilePhones(
        "3127124026",
        "RC",
        "1",
        "1",
        "1",
        "2024-01-01",
        "2024-01-01",
        "COL");

    Emails emails = new Emails(
        "<EMAIL>",
        "RC",
        "1",
        "1",
        "1",
        "free",
        "2024-01-01",
        "2024-01-01");

    ExternalCustomerFullDetail externalCustomerFullDetail = new ExternalCustomerFullDetail(
        basicInformation,
        listOfAddresses,
        phones,
        cellPhones,
        emails);

    ExternalCustomerRequest dto = new ExternalCustomerRequest("NIT", "123456");

    when(externalCustomerSearchClient.searchCustomerByExternalId(dto.id(), dto.typeId()))
        .thenReturn(externalCustomerFullDetail);
    // WHEN
    ExternalCustomer customer = null;
    try {
      customer = externalCustomerSearchService.searchCustomerByExternalId("NIT", "123456", "2024-01-01");
    } catch (Exception e) {
      e.printStackTrace();
    }

    // THEN
    assertNotNull(customer);
  }

  @Test
  void testSearchCustomerByExternalId_Success() {
    // GIVEN
    BasicInformation basicInformation = new BasicInformation(
        "economicActivivty",
        "CIIU",
        "documentStatus",
        "2024-01-01T00:00:00.000+00:00",
        "M",
        "1",
        "CO",
        "hola nuevo mundo !",
        "mundo",
        "!",
        "hola",
        "nuevo",
        "12345",
        "CC",
        new AgeRange("18", "99"));

    Address address = new Address(
        "12345",
        "asdfghjklñ",
        "1",
        "MiDC",
        "algo",
        "12",
        "algo2",
        "345",
        "8",
        "1",
        "20",
        "2024-01-02",
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        "null",
        "2024-01-31");
    ArrayList<Address> listOfAddresses = new ArrayList<Address>();
    listOfAddresses.add(address);

    Phones phones = new Phones(
        "CAS",
        "12345678",
        "algo",
        "11",
        "algo2",
        "001",
        "1",
        "1",
        "1",
        "2024-01-01",
        "1138221",
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        "null",
        "2024-01-31",
        "502");

    MobilePhones cellPhones = new MobilePhones(
        "3127124026",
        "RC",
        "1",
        "1",
        "1",
        "2024-01-01",
        "2024-01-01",
        "COL");

    Emails emails = new Emails(
        "<EMAIL>",
        "RC",
        "1",
        "1",
        "1",
        "free",
        "2024-01-01",
        "2024-01-01");

    ExternalCustomerFullDetail externalCustomerFullDetail = new ExternalCustomerFullDetail(
        basicInformation,
        listOfAddresses,
        phones,
        cellPhones,
        emails);

    ExternalCustomerRequest dto = new ExternalCustomerRequest("CC", "123456");

    when(externalCustomerSearchClient.searchCustomerByExternalId(dto.id(), dto.typeId()))
        .thenReturn(externalCustomerFullDetail);
    // WHEN
    ExternalCustomer customer = null;
    try {
      customer = externalCustomerSearchService.searchCustomerByExternalId("CC", "123456", "2024-01-01");
    } catch (Exception e) {
      e.printStackTrace();
    }

    // THEN
    assertNotNull(customer);
  }

  @Test
  void testSearchCustomerByExternalId_CustomerNotFound() {
    // Arrange
    String idType = IdDocumentTypeEnum.NIT.name();
    String idNumber = "123456789";
    String expeditionDate = "2024-10-01";

    // Simular que no se encuentra al cliente
    when(externalCustomerSearchClient.searchCustomerByExternalId(idNumber, idType)).thenReturn(null);

    // Act & Assert
    Exception exception = assertThrows(ExternalCustomerSearchNotFoundProblem.class, () -> {
      externalCustomerSearchService.searchCustomerByExternalId(idType, idNumber, expeditionDate);
    });
    assertNotNull(exception);
  }

  @Test
  void testSearchCustomerByExternalId_BasicInformation_Null() {
    // Arrange
    String idType = IdDocumentTypeEnum.NIT.name();
    String idNumber = "123456789";
    String expeditionDate = "2024-10-01";

    // Simular que no se encuentra al cliente
    when(externalCustomerSearchClient.searchCustomerByExternalId(idNumber, idType))
        .thenReturn(new ExternalCustomerFullDetail(null, null, null, null, null));

    // Act & Assert
    Exception exception = assertThrows(ExternalCustomerSearchNotFoundProblem.class, () -> {
      externalCustomerSearchService.searchCustomerByExternalId(idType, idNumber, expeditionDate);
    });
    assertNotNull(exception);
  }

  @Test
  void testSearchCustomerByExternalId_ExceptionThrown() {
    // Arrange
    String idType = IdDocumentTypeEnum.NIT.name();
    String idNumber = "123456789";
    String expeditionDate = "2024-10-01";

    // Simular una excepción al buscar el cliente
    when(externalCustomerSearchClient.searchCustomerByExternalId(idNumber, idType)).thenThrow(new RuntimeException());

    // Act & Assert
    Exception exception = assertThrows(ExternalCustomerSearchNotFoundProblem.class, () -> {
      externalCustomerSearchService.searchCustomerByExternalId(idType, idNumber, expeditionDate);
    });
    assertNotNull(exception);
  }

  @Test
  void testSearchCustomerByExternalId_ExpeditionDateMismatch() throws Exception {
    // Arrange
    String idType = "OTHER_ID_TYPE"; // Usar un tipo de documento diferente a NIT
    String idNumber = "123456789";
    String expeditionDate = "2024-11-01";

    BasicInformation basicInformation = new BasicInformation(
        "economicActivivty",
        "CIIU",
        "documentStatus",
        "2024-01-01T00:00:00.000+00:00",
        "M",
        "1",
        "CO",
        "hola nuevo mundo !",
        "mundo",
        "!",
        "hola",
        "nuevo",
        "12345",
        "CC",
        new AgeRange("18", "99"));

    Address address = new Address(
        "12345",
        "asdfghjklñ",
        "1",
        "MiDC",
        "algo",
        "12",
        "algo2",
        "345",
        "8",
        "1",
        "20",
        "2024-01-02",
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        "null",
        "2024-01-31");
    ArrayList<Address> listOfAddresses = new ArrayList<Address>();
    listOfAddresses.add(address);

    Phones phones = new Phones(
        "CAS",
        "12345678",
        "algo",
        "11",
        "algo2",
        "001",
        "1",
        "1",
        "1",
        "2024-01-01",
        "1138221",
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        "null",
        "2024-01-31",
        "502");

    MobilePhones cellPhones = new MobilePhones(
        "3127124026",
        "RC",
        "1",
        "1",
        "1",
        "2024-01-01",
        "2024-01-01",
        "COL");

    Emails emails = new Emails(
        "<EMAIL>",
        "RC",
        "1",
        "1",
        "1",
        "free",
        "2024-01-01",
        "2024-01-01");

    ExternalCustomerFullDetail externalCustomerFullDetail = new ExternalCustomerFullDetail(
        basicInformation,
        listOfAddresses,
        phones,
        cellPhones,
        emails);

    // Simular respuesta del cliente de búsqueda
    when(externalCustomerSearchClient.searchCustomerByExternalId(idNumber, idType))
        .thenReturn(externalCustomerFullDetail);

    // Act & Assert
    Exception exception = assertThrows(ExternalCustomerExpeditionDateProblem.class, () -> {
      externalCustomerSearchService.searchCustomerByExternalId(idType, idNumber, expeditionDate);
    });
    assertNotNull(exception);
  }
}
