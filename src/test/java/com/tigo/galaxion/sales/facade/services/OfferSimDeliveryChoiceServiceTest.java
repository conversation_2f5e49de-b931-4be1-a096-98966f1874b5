package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.domain.problem.OfferNotFoundProblem;
import com.tigo.galaxion.sales.facade.domain.response.TigoCartResponse;
import com.tigo.galaxion.sales.facade.domain.response.TigoOfferResponse;
import com.tigo.galaxion.sales.facade.model.repository.OfferRepository;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OfferSimDeliveryChoiceServiceTest {

    @Mock
    private CartService cartService;
    @Mock
    private OfferRepository contactIdentityDocumentRepository;

    @Mock
    private ContractSignatureOptionService  contractSignatureOptionService;

    @InjectMocks
    private OfferSimDeliveryChoiceService offerSimDeliveryChoiceService;

    @Nested
    class UpdateForProspectTest {

        @Test
        void update_offerExist_isShippingUpdate() {
            // GIVEN
            var deliveryType = "typeuuuuuuuh";
            var reference = "RO6EW4O9";
            var offerId = 1L;
            var cartOfferResponse = TigoOfferResponse.builder().id(1L).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForProspect(reference)).thenReturn(cartResponse);
            doNothing().when(contractSignatureOptionService).updateContractSignatureOption(reference, null);

            // WHEN
            var tigoCartResponse = offerSimDeliveryChoiceService.updateForProspect(reference, offerId, deliveryType);

            // THEN
            var offerResponse = tigoCartResponse.getOffers().get(0);
            assertEquals(deliveryType, offerResponse.getSimDeliveryType());
        }

        @Test
        void update_offerNotExist_isShippingUpdate() {
            // GIVEN
            var reference = "RO6EW4O9";
            var offerId = 2L;
            var cartOfferResponse = TigoOfferResponse.builder().id(1L).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForProspect(reference)).thenReturn(cartResponse);

            // WHEN
            // THEN
            assertThrows(OfferNotFoundProblem.class, () -> offerSimDeliveryChoiceService.updateForProspect(reference, offerId, "deliveryType"));

        }
    }

    @Nested
    class UpdateForCrossSellTest {

        @Test
        void update_offerExist_isShippingUpdate() {
            // GIVEN
            var deliveryType = "typeuuuuuuuh";
            var reference = "RO6EW4O9";
            var offerId = 1L;
            var cartOfferResponse = TigoOfferResponse.builder().id(1L).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForCrossSell(reference)).thenReturn(cartResponse);
            doNothing().when(contractSignatureOptionService).updateContractSignatureOption(reference, null);

            // WHEN
            var tigoCartResponse = offerSimDeliveryChoiceService.updateForCrossSell(reference, offerId, deliveryType);

            // THEN
            var offerResponse = tigoCartResponse.getOffers().get(0);
            assertEquals(deliveryType, offerResponse.getSimDeliveryType());
        }

        @Test
        void update_offerNotExist_isShippingUpdate() {
            // GIVEN
            var reference = "RO6EW4O9";
            var offerId = 2L;
            var cartOfferResponse = TigoOfferResponse.builder().id(1L).build();
            var cartResponse = TigoCartResponse.builder().offers(Collections.singletonList(cartOfferResponse)).build();
            when(cartService.getCartForCrossSell(reference)).thenReturn(cartResponse);

            // WHEN
            // THEN
            assertThrows(OfferNotFoundProblem.class, () -> offerSimDeliveryChoiceService.updateForCrossSell(reference, offerId, "deliveryType"));
        }
    }
}
