package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.collection.CollectionClient;
import com.tigo.galaxion.sales.facade.connector.cross.sell.CrossSellClient;
import com.tigo.galaxion.sales.facade.domain.problem.AccountInCollectionProblem;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EligibilityServiceTest {

    @Mock
    CollectionClient collectionClient;

    @Mock
    CrossSellClient crossSellClient;


    @InjectMocks
    EligibilityService eligibilityService;

    @Nested
    class CheckCrossSellEligibilityTest {

        @Test
        void validCheckCrossSellEligibility() {
            // GIVEN
            String accountId = "1";
            when(collectionClient.checkAccountIsInCollection(any())).thenReturn(false);

            when(crossSellClient.getServiceGroupsEligibility(any())).thenReturn(new ArrayList<>());

            // WHEN
            eligibilityService.checkCrossSellEligibility(accountId);

            // THEN
            verify(collectionClient).checkAccountIsInCollection(accountId);
            verify(crossSellClient).getServiceGroupsEligibility(accountId);

        }
    }

    @Nested
    class CheckAccountIsInCollectionTest {

        @Test
        void invalidCheckAccountIsInCollection() {
            //GIVEN
            var accountId = "1";

            when(collectionClient.checkAccountIsInCollection(any())).thenReturn(true);

            //WHEN
            //THEN
            assertThrows(AccountInCollectionProblem.class, () -> eligibilityService.checkAccountIsInCollection(accountId));
        }
    }
}
