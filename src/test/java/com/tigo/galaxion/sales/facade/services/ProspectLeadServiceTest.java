package com.tigo.galaxion.sales.facade.services;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.fasterxml.jackson.databind.JsonNode;
import com.tigo.galaxion.sales.facade.connector.prospectlead.ProspectLeadClient;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Address;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.request.ProspectLeadRequest;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponseByEntity;

//@ExtendWith(MockitoExtension.class)
public class ProspectLeadServiceTest {

    @Mock
    private ProspectLeadClient client;

    @Mock
    private AddressService addressService;

    @InjectMocks
    private ProspectLeadService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    void testGetProspectInfoByEntityType() {
        String prospectId = "1040948G";
        String entity = "address";
        Integer entityType = 1;

        // Crear el objeto de respuesta esperado
        ProspectLeadResponseByEntity response = new ProspectLeadResponseByEntity();
        Address address = new Address();
        address.setAddressType(1);
        address.setDepartmentCode("57");
        address.setDepartment("Antioquia");
        address.setMunicipalityCode("5631000");
        address.setMunicipality("Sabaneta");
        address.setAddress("Cl 33 No. 30-57");
        address.setLatitude("4.9537");
        address.setLongitude("94.8231");
        address.setStratum(1);
        address.setMicrozone("CALDAS");
        address.setCountryCode("57");
        address.setAddressCode("1431065734");
        address.setDescriptionAddress(null);
        // Asigna la dirección al objeto de respuesta
        response.setAddress(new Address[]{address});

        when(client.getProspectEntityByEntityType(prospectId, entity, entityType)).thenReturn(response);

        ProspectLeadResponseByEntity result = service.getProspectInfoByEntityType(prospectId, entity, entityType);

        assertEquals(response, result);
        verify(client, times(1)).getProspectEntityByEntityType(prospectId, entity, entityType);
    }
 
    @Test
    void testGetAllProspectInfo() {
        String prospectId = "1040948G";
        ProspectLeadResponse response = new ProspectLeadResponse();
        when(client.getAllProspectEntities(prospectId)).thenReturn(response);

        ProspectLeadResponse result = service.getAllProspectInfo(prospectId);

        assertEquals(response, result);
        verify(client, times(1)).getAllProspectEntities(prospectId);
    }
    
    @Test
    void testUpdateAllProspectInfo() {
        String prospectId = "1040948G";
        ProspectLeadRequest request = null;

        service.updateAllProspectInfo(prospectId, request);

        verify(client, times(1)).updateAllProspectEntities(prospectId, request);
    }

    @Test
    void testUpdateProspectInfoByEntity() {
        String prospectId = "1040948G";
        String entity = "customer";
        JsonNode request = mock(JsonNode.class);

        service.updateProspectInfoByEntity(prospectId, entity, request);

        verify(client, times(1)).updateProspectEntyByEntityType(prospectId, entity, request);
    }

    @Test
    void testUpdateProspectInfoByEntityWithNull() {
        String prospectId = "1040948G";
        String entity = "customer";
        JsonNode request = null;

        service.updateProspectInfoByEntity(prospectId, entity, request);

        verify(client, times(1)).updateProspectEntyByEntityType(prospectId, entity, request);
    }
}
