package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.request.UpdateProspectCreditScoreRequest;
import com.tigo.galaxion.sales.facade.connector.contact.ContactClient;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactEmailV2Response;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactIdentityDocumentV2Response;
import com.tigo.galaxion.sales.facade.connector.contact.domain.response.ContactV2Response;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.TigoCreditScoreClient;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.request.TigoCreditScoreRequest;
import com.tigo.galaxion.sales.facade.connector.opco.credit.score.domain.response.TigoCreditScoreResponse;
import com.tigo.galaxion.sales.facade.domain.enumeration.IdDocumentTypeEnum;
import com.tigo.galaxion.sales.facade.domain.problem.IdentityDocumentAlreadyExistsProblem;
import com.tigo.galaxion.sales.facade.domain.request.CreditScoreRequest;
import com.tigo.galaxion.sales.facade.model.entity.ContactIdentityDocumentEntity;
import com.tigo.galaxion.sales.facade.model.repository.ContactIdentityDocumentRepository;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CreditScoreServiceTest {

    @Mock
    private TigoCreditScoreClient tigoCreditScoreClient;
    @Mock
    private ContactClient contactClient;
    @Mock
    private AcquisitionProspectClient acquisitionProspectClient;
    @Mock
    private ContactIdentityDocumentRepository contactIdentityDocumentRepository;

    @InjectMocks
    private CreditScoreService creditScoreService;

    @Nested
    class UpdateContactTest {

        @Test
        void identityDocumentAlreadyExistInGalaxionWithSameCountry_throwIdentityDocumentAlreadyExistsProblem() {
            //GIVEN
            var prospectRef = "ref01";
            var documentId = "0123456789";
            var creditScoreRequest = CreditScoreRequest
                    .builder()
                    .documentId(documentId)
                    .nationality("AR")
                    .build();

            var firstname = "Denis";
            var lastname = "Schafer";
            var birthDate = LocalDate.now();
            var email = "<EMAIL>";
            var contactResponse = ContactV2Response.builder()
                    .emails(List.of(ContactEmailV2Response.builder().email(email).build()))
                    .firstName(firstname)
                    .lastName(lastname)
                    .birthDate(birthDate)
                    .identityDocuments(List.of(ContactIdentityDocumentV2Response.builder().nationality("AR").build()))
                    .build();
            when(contactClient.searchContact(documentId)).thenReturn(List.of(contactResponse));

            //WHEN
            //THEN
            var message = assertThrows(IdentityDocumentAlreadyExistsProblem.class, () -> creditScoreService.executeCreditScore(prospectRef, creditScoreRequest)).getDetail();
            assertEquals(String.format("This document already exist in galaxion with : [firstname : %s, lastname : %s, email : %s, birthdate : %s].", firstname, lastname, email, birthDate), message);
        }

        @Test
        void identityDocumentNotExistInGalaxion_updateCreditScoreAndContact() {
            //GIVEN
            var prospectRef = "ref";
            var documentId = "0123456789";
            var documentType = IdDocumentTypeEnum.NIT;
            var nationality = "CO";
            var creditScoreRequest = CreditScoreRequest
                    .builder()
                    .documentId(documentId)
                    .documentType(documentType)
                    .nationality(nationality)
                    .build();

            when(contactClient.searchContact(documentId)).thenReturn(Collections.emptyList());

            var score = "3";
            var blacklisted = false;
            var tigoCreditScoreResponse = TigoCreditScoreResponse
                    .builder()
                    .score(score)
                    .blacklisted(blacklisted)
                    .build();
            when(tigoCreditScoreClient.score(any())).thenReturn(tigoCreditScoreResponse);

            //WHEN
            creditScoreService.executeCreditScore(prospectRef, creditScoreRequest);

            //THEN
            var tigoCreditScoreRequestArgumentCaptor = ArgumentCaptor.forClass(TigoCreditScoreRequest.class);
            verify(tigoCreditScoreClient).score(tigoCreditScoreRequestArgumentCaptor.capture());
            assertEquals(documentId, tigoCreditScoreRequestArgumentCaptor.getValue().getDocumentNumber());
            assertEquals(documentType.getName(), tigoCreditScoreRequestArgumentCaptor.getValue().getType());
            assertEquals(nationality, tigoCreditScoreRequestArgumentCaptor.getValue().getNationality());

            var updateProspectCreditScoreRequestArgumentCaptor = ArgumentCaptor.forClass(UpdateProspectCreditScoreRequest.class);
            verify(acquisitionProspectClient).updateProspectCreditScore(eq(prospectRef), updateProspectCreditScoreRequestArgumentCaptor.capture());
            assertEquals(score, updateProspectCreditScoreRequestArgumentCaptor.getValue().getCreditScore());
            assertEquals(blacklisted, updateProspectCreditScoreRequestArgumentCaptor.getValue().isBlacklisted());
            assertEquals(nationality, tigoCreditScoreRequestArgumentCaptor.getValue().getNationality());

            var contactIdentityDocumentEntityArgumentCaptor = ArgumentCaptor.forClass(ContactIdentityDocumentEntity.class);
            verify(contactIdentityDocumentRepository).save(contactIdentityDocumentEntityArgumentCaptor.capture());
            assertEquals(documentId, contactIdentityDocumentEntityArgumentCaptor.getValue().getDocumentIdentifier());
            assertEquals(documentType, contactIdentityDocumentEntityArgumentCaptor.getValue().getDocumentType());
            assertEquals(nationality, contactIdentityDocumentEntityArgumentCaptor.getValue().getNationality());
        }

        @Test
        void identityDocumentNotExistInGalaxionWithDifferentCountry_doesNotThrowException() {
            //GIVEN
            var prospectRef = "ref";
            var documentId = "0123456789";
            var documentType = IdDocumentTypeEnum.NIT;
            var nationality = "CO";
            var creditScoreRequest = CreditScoreRequest
                    .builder()
                    .documentId(documentId)
                    .documentType(documentType)
                    .nationality(nationality)
                    .build();
            var contactResponse = ContactV2Response.builder()
                    .emails(List.of(ContactEmailV2Response.builder().email("<EMAIL>").build()))
                    .firstName("Denis")
                    .lastName("Schafer")
                    .birthDate(LocalDate.now())
                    .identityDocuments(List.of(ContactIdentityDocumentV2Response.builder().nationality("AR").build()))
                    .build();

            when(contactClient.searchContact(documentId)).thenReturn(List.of(contactResponse));

            var tigoCreditScoreResponse = TigoCreditScoreResponse
                    .builder()
                    .score("3")
                    .blacklisted(false)
                    .build();
            when(tigoCreditScoreClient.score(any())).thenReturn(tigoCreditScoreResponse);

            //WHEN
            //THEN
            assertDoesNotThrow(() -> creditScoreService.executeCreditScore(prospectRef, creditScoreRequest));
        }
    }

}
