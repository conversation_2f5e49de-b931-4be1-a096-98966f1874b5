package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.order.domain.OrderClient;
import com.tigo.galaxion.sales.facade.connector.order.domain.response.GetOrdersResponse;
import com.tigo.galaxion.sales.facade.connector.order.domain.response.OrderResponse;
import com.tigo.galaxion.sales.facade.connector.order.service.ServiceClient;
import com.tigo.galaxion.sales.facade.connector.order.service.response.EntityResponse;
import com.tigo.galaxion.sales.facade.connector.order.service.response.OrderServiceResponse;
import com.tigo.galaxion.sales.facade.connector.order.service.response.ServiceResponse;
import com.tigo.galaxion.sales.facade.domain.problem.ValidDateProblem;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class OrderServiceTest {

    @Mock
    private OrderClient orderClient;

    @Mock
    private ServiceClient serviceClient;

    @InjectMocks
    private OrderService orderService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetOrdersByClient_Success() {
        Integer page = 0;
        Integer size = 10;
        Long msisdn = 123456789L;
        String start = "2023-01-01";
        String end = "2023-01-31";

        List<OrderResponse> orderResponseList = new ArrayList<>();
        OrderResponse orderResponse = new OrderResponse();
        orderResponse.setId("order123");
        orderResponse.setCreationDate("2023-01-02");
        orderResponse.setCompletionDate("2023-01-10");
        orderResponse.setState("COMPLETED");
        orderResponseList.add(orderResponse);

        when(orderClient.getOrder(anyString(), any(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(orderResponseList);

        Page<GetOrdersResponse> result = orderService.getOrdersByClient(page, size, msisdn, start, end);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());

        GetOrdersResponse response = result.getContent().get(0);
        assertEquals("order123", response.getOrderId());
        assertEquals("2023-01-02", response.getCreationDate());
        assertEquals("2023-01-10", response.getCompletionDate());
        assertEquals("COMPLETED", response.getLifecycleState());

        verify(orderClient, times(1)).getOrder(anyString(), eq(msisdn), anyString(), eq(start), eq(end), eq(page), eq(size));
    }

    @Test
    void testGetOrdersByClient_InvalidDate() {
        Integer page = 0;
        Integer size = 10;
        Long msisdn = 123456789L;
        String start = "2023-02-01";
        String end = "2023-01-01";

        Exception exception = assertThrows(ValidDateProblem.class, () -> orderService.getOrdersByClient(page, size, msisdn, start, end));

        assertTrue(exception.getMessage().contains("StartDate: 2023-02-01 is after EndDate: 2023-01-01"));
    }

    @Test
    void testGetServiceByClient_Success() {
        Long msisdn = 123456789L;
        Integer page = 0;
        Integer size = 10;
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        EntityResponse entity = new EntityResponse();
        entity.setId("1");
        entity.setState("COMPLETED");
        List<EntityResponse> entities = Collections.singletonList(entity);

        ServiceResponse mockServiceResponse = new ServiceResponse(0, 10, 1, entities);

        when(serviceClient.getOrder(anyString(), eq(msisdn), eq(startDate), eq(endDate), eq(page), eq(size)))
                .thenReturn(mockServiceResponse);

        OrderServiceResponse response = orderService.getServicesByClient(page, size, msisdn, startDate, endDate);

        assertEquals(1, response.getContent().size());
        assertEquals("1", response.getContent().get(0).getOrderId());
        assertEquals("COMPLETED", response.getContent().get(0).getLifecycleState());

        verify(serviceClient, times(1)).getOrder(anyString(), eq(msisdn), eq(startDate), eq(endDate), eq(page), eq(size));
    }

    @Test
    void testGetServiceByClient_InvalidDate() {
        Integer page = 0;
        Integer size = 10;
        Long msisdn = 123456789L;
        String start = "2023-02-01";
        String end = "2023-01-01";

        Exception exception = assertThrows(ValidDateProblem.class, () -> orderService.getServicesByClient(page, size, msisdn, start, end));

        assertTrue(exception.getMessage().contains("StartDate: 2023-02-01 is after EndDate: 2023-01-01"));
    }
}
