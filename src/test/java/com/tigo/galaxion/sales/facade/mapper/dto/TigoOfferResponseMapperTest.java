package com.tigo.galaxion.sales.facade.mapper.dto;

import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.enumeration.CommitmentDurationEnum;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.BaseOfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.ChargeResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.DiscountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.HandsetResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.OfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.PricePlanResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.SimCardResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.TariffPlanDiscountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.UsageResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.equipments.InclusiveHandsetResponse;
import com.tigo.galaxion.sales.facade.services.retrieval.OfferRetrievalService;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TigoOfferResponseMapperTest {

    @InjectMocks
    private TigoOfferResponseMapper tigoOfferResponseMapper;

    @Mock
    private OfferRetrievalService offerRetrievalService;

    @Nested
    class BuildTigoOfferResponsesTest {

        @Test
        void withValidOffer_returnMaltOfferResponse() {
            // GIVEN
            var offerId = 1L;
            var baseOffer = BaseOfferResponse.builder()
                                             .commitmentDuration(CommitmentDurationEnum.EIGHTEEN)
                                             .charges(List.of(ChargeResponse.builder().pricePlan(PricePlanResponse.builder().build()).build()))
                                             .usages(List.of(UsageResponse.builder().build()))
                                             .tariffPlanDiscounts(List.of(TariffPlanDiscountResponse.builder().build()))
                                             .build();
            var offer = OfferResponse.builder()
                                     .id(offerId)
                                     .baseOffer(baseOffer)
                                     .equipments(List.of(HandsetResponse.builder().charge(ChargeResponse.builder().pricePlan(PricePlanResponse.builder().build()).build()).build()))
                                     .inclusiveEquipments(List.of(InclusiveHandsetResponse.builder().build()))
                                     .discounts(List.of(DiscountResponse.builder().build()))
                                     .simCard(SimCardResponse.builder().build())
                                     .build();

            when(offerRetrievalService.get(offerId)).thenReturn(Optional.empty());

            // WHEN
            var offerResponses = tigoOfferResponseMapper.buildTigoOfferResponses(List.of(offer));

            // THEN
            assertEquals(1, offerResponses.size());
            assertEquals(CommitmentDurationEnum.EIGHTEEN.name(), offerResponses.get(0).getBaseOffer().getCommitmentDuration());
        }

    }
}
