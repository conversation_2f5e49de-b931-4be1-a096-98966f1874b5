package com.tigo.galaxion.sales.facade.factory;

import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.CustomerReference;

public class CustomerReferenceFactory {

	public static CustomerReference getCustomerReference(String documentId) {
		return CustomerReference.builder()
        .referenceType(1)
        .documentType("CC")
        .documentId("1232111"+documentId)
        .names("hola")
        .lastName("mundo")
        .email("<EMAIL>")
        .phone("111111111111")
        .build();
	}
}
