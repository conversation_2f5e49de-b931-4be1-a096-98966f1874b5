spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=MySQL;
spring.datasource.username=sa
spring.datasource.password=

spring.cache.type=NONE

logging.level.org.springframework.boot.autoconfigure=ERROR
spring.jpa.database-platform=org.hibernate.dialect.MariaDBDialect

normalize.default.stratum=3

# MANAGEMENT OTP

environment.url.management-otp-token-service=https://api.servers1.tigocloud.net/server/tokens
environment.url.management-otp-code-service=https://test.id.tigo.com/tigoid/pub/v2
management-otp.subId="166110c9-8b82-40c4-8c60-e7d1e9e9a0a8"
management-otp.appId="7a77ff70-19bf-417c-85f8-9d597f199da8"
management-otp.text="El codigo de seguridad que solicitaste es {token}. TIGO nunca solicitara tu codigo, es personal y confidencial. No lo compartas con otra persona."
management-otp.length=4
management-otp.connect.email=email-otp
management-otp.connect.phone=msisdn-otp
management-otp.type="numeric"
management-otp.ttl=60000

################################################################################
# Alfresco
################################################################################
alfresco.params.idTramite=31
alfresco.params.idCanal=1
alfresco.params.reqFirmaRemota=false
alfresco.params.reqFirmaManuscrita=true
alfresco.params.reqFirmaElectronica=false
alfresco.params.urlRespuesta=http://crm.devdmstigo.co:8080/servicioUrl
alfresco.proxy.use-proxy=false
alfresco.proxy.host=proxyserver.epmtelco.com.co
alfresco.proxy.port=8080

symphonica.statuses=CANCELLED,REJECTED,PARTIAL,FAILED

wfe.signal.upfrontpayment=WAIT_UPFRONT_PAYMENT
wfe.signal.SP_WAIT_BB_PROV_REQUEST=SOM_WAIT_PROVISIONING