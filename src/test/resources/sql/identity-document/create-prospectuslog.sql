CREATE TABLE prospectus_log (
  customer_id_number varchar(100) NOT NULL,
  customer_id_type varchar(50) NOT NULL,
  reference varchar(25) NOT NULL,
  created_at timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  user_id varchar(100) NOT NULL,
  user_name varchar(255) NOT NULL,
  ip_quipment varchar(25) NOT NULL,
  sales_channel varchar(100) NOT NULL,
  authentication_type varchar(100) NOT NULL,
  transaction varchar(150) NOT NULL,
  PRIMARY KEY (reference,customer_id_type,customer_id_number)
);