CREATE TABLE contacts.communication_preference (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  channel varchar(64) DEFAULT NULL,
  language varchar(5) DEFAULT NULL,
  email varchar(255) DEFAULT NULL,
  phone_number varchar(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

CREATE TABLE contacts.contact (
  uuid varchar(36) NOT NULL,
  first_name varchar(255) NOT NULL,
  last_name varchar(255) NOT NULL,
  birth_date date DEFAULT NULL,
  communication_preference_id bigint(20) DEFAULT NULL,
  title varchar(10) DEFAULT NULL,
  nationality varchar(64) DEFAULT NULL,
  gdpr_validation_date date DEFAULT NULL,
  gdpr_language varchar(5) DEFAULT NULL,
  created_at datetime NOT NULL DEFAULT current_timestamp(),
  updated_at datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  allow_third_parties tinyint(1) DEFAULT NULL,
  company_position varchar(255) DEFAULT NULL,
  is_vip tinyint(1) DEFAULT 0,
  last_id_check datetime DEFAULT NULL,
  marketing_profiling tinyint(1) DEFAULT 0,
  PRIMARY KEY (uuid),
  KEY idx__contact__communication_preference_id (communication_preference_id),
  KEY idx__contact__first_name_last_name_email (first_name,last_name),
  KEY idx__contact__last_name_email (last_name),
  CONSTRAINT fk__contact__communication_preference_id FOREIGN KEY (communication_preference_id) REFERENCES communication_preference (id)
);

CREATE TABLE contacts.identity_document (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  expiration_date date DEFAULT NULL,
  identifier varchar(255) DEFAULT NULL,
  nationality varchar(255) DEFAULT NULL,
  type varchar(255) DEFAULT NULL,
  contact_uuid varchar(36) NOT NULL,
  is_main_identity_document tinyint(4) NOT NULL,
  PRIMARY KEY (id),
  KEY idx__identity_document__contact_uuid (contact_uuid),
  CONSTRAINT fk__identity_document__contact_uuid FOREIGN KEY (contact_uuid) REFERENCES contact (uuid)
);
