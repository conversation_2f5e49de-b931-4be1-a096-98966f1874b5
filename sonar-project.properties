# Required metadata
sonar.projectKey=galaxion_projects_epicmt_backend_tigo-sales-facade_AYmreK8z-VjCnCrWMU8-
sonar.projectName=tigo-sales-facade
sonar.scm.provider=git
sonar.sources=src/main/java
sonar.tests=src/test/java
sonar.language=java
sonar.java.binaries=target
sonar.java.source=17
sonar.sourceEncoding=UTF-8

sonar.exclusions=src/main/resources/**,\
                 **/*Configuration.java,\
                 **/client/**,\
                 **/configuration/**,\
                 **/event/**,\
                 **/exception/**,\
                 **/mapper/**,\
                 **/stream/**,\
                 **/domain/**,\
                 **/model/**,\
                 **/config/**,\
                 **/controller/**,\
                 **/controller/FieldServiceController.java,\
                 **/soap/**,\
                 **/soap/get_task/**,\
                 **/connector/**,\
                 **/helper/**,\
                 **/services/AccountsService.java,\
                 **/services/NotificationService.java,\
                 **/services/OtpManagementService.java,\
                 **/services/AddressService.java,\
                 **/services/AlfrescoService.java,\
                 **/services/CartService.java,\
                 **/services/CatalogService.java,\
                 **/services/ContractSignatureOptionService.java,\
                 **/services/CounterService.java,\
                 **/services/CoverageService.java,\
                 **/services/CreateCrossSellService.java,\
                 **/services/CreditScoreService.java,\
                 **/services/CrossSellContactService.java,\
                 **/services/CrossSellOfferService.java,\
                 **/services/ProspectLeadService.java,\
                 **/services/retrieval/**,\
                 **/services/order_notification/**,\
                 **/services/field_service/**,\
                 **/TigoSalesFacadeApplication.java

# Report Dependency Check
sonar.dependencyCheck.jsonReportPath=dependency-check-report.json
sonar.dependencyCheck.htmlReportPath=dependency-check-report.html

